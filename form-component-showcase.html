<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIMA Form Component System - Mobile-First Showcase</title>
    <link rel="stylesheet" href="assets/dist/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.0/css/all.min.css"/>
    <style>
        body {
            font-family: var(--font-family-primary);
            line-height: 1.6;
            margin: 0;
            padding: var(--spacing-6);
            background: var(--color-gray-50);
        }
        .showcase-section {
            background: var(--color-white);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-6);
            margin-bottom: var(--spacing-6);
            box-shadow: var(--shadow-sm);
        }
        .showcase-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-gray-900);
            margin-bottom: var(--spacing-4);
            border-bottom: 2px solid var(--color-primary-200);
            padding-bottom: var(--spacing-2);
        }
        .showcase-subtitle {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--color-gray-800);
            margin: var(--spacing-6) 0 var(--spacing-3) 0;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: var(--spacing-6);
        }
        @media (min-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        .code-block {
            background: var(--color-gray-900);
            color: var(--color-gray-100);
            padding: var(--spacing-4);
            border-radius: var(--border-radius-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            overflow-x: auto;
            margin: var(--spacing-4) 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="showcase-title">VIMA Form Component System</h1>
        <p>Mobile-first, touch-optimized form system with real-time validation, accessibility features, and responsive design patterns.</p>

        <!-- Basic Form Controls -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Basic Form Controls</h2>
            <div class="demo-grid">
                <div>
                    <h3>Text Input</h3>
                    <div class="form-group">
                        <label class="form-label form-label--required" for="text-input">Full Name</label>
                        <input type="text" id="text-input" class="form-control" placeholder="Enter your full name">
                        <div class="form-help">This field is required</div>
                    </div>
                </div>
                
                <div>
                    <h3>Email Input</h3>
                    <div class="form-group">
                        <label class="form-label form-label--required" for="email-input">Email Address</label>
                        <input type="email" id="email-input" class="form-control" placeholder="<EMAIL>">
                        <div class="form-feedback form-feedback--valid">Valid email format</div>
                    </div>
                </div>
                
                <div>
                    <h3>Password Input</h3>
                    <div class="form-group">
                        <label class="form-label form-label--required" for="password-input">Password</label>
                        <input type="password" id="password-input" class="form-control" placeholder="Enter secure password">
                        <div class="form-help">Minimum 8 characters with numbers and symbols</div>
                    </div>
                </div>
                
                <div>
                    <h3>Phone Input</h3>
                    <div class="form-group">
                        <label class="form-label form-label--optional" for="phone-input">Phone Number</label>
                        <input type="tel" id="phone-input" class="form-control" placeholder="+****************">
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Control Sizes -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Form Control Sizes</h2>
            <div class="form-group">
                <label class="form-label" for="input-sm">Small Input</label>
                <input type="text" id="input-sm" class="form-control form-control--sm" placeholder="Small input">
            </div>
            
            <div class="form-group">
                <label class="form-label" for="input-default">Default Input</label>
                <input type="text" id="input-default" class="form-control" placeholder="Default input">
            </div>
            
            <div class="form-group">
                <label class="form-label" for="input-lg">Large Input</label>
                <input type="text" id="input-lg" class="form-control form-control--lg" placeholder="Large input">
            </div>
        </div>

        <!-- Select Controls -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Select Controls</h2>
            <div class="demo-grid">
                <div>
                    <h3>Single Select</h3>
                    <div class="form-group">
                        <label class="form-label form-label--required" for="country-select">Country</label>
                        <select id="country-select" class="form-select">
                            <option value="">Choose a country</option>
                            <option value="us">United States</option>
                            <option value="ca">Canada</option>
                            <option value="uk">United Kingdom</option>
                            <option value="fr">France</option>
                            <option value="de">Germany</option>
                        </select>
                    </div>
                </div>
                
                <div>
                    <h3>Multiple Select</h3>
                    <div class="form-group">
                        <label class="form-label" for="skills-select">Skills</label>
                        <select id="skills-select" class="form-select" multiple size="4">
                            <option value="js">JavaScript</option>
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="php">PHP</option>
                            <option value="css">CSS</option>
                            <option value="html">HTML</option>
                        </select>
                        <div class="form-help">Hold Ctrl/Cmd to select multiple options</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Textarea -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Textarea Controls</h2>
            <div class="demo-grid">
                <div>
                    <h3>Default Textarea</h3>
                    <div class="form-group">
                        <label class="form-label" for="message-textarea">Message</label>
                        <textarea id="message-textarea" class="form-textarea" placeholder="Enter your message here..."></textarea>
                    </div>
                </div>
                
                <div>
                    <h3>Large Textarea</h3>
                    <div class="form-group">
                        <label class="form-label" for="description-textarea">Description</label>
                        <textarea id="description-textarea" class="form-textarea form-textarea--lg" placeholder="Provide detailed description..."></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkboxes and Radios -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Checkboxes and Radio Buttons</h2>
            <div class="demo-grid">
                <div>
                    <h3>Checkboxes</h3>
                    <div class="form-group">
                        <label class="form-label">Interests</label>
                        <div class="form-check">
                            <input type="checkbox" id="interest-tech" class="form-check-input" value="technology">
                            <label class="form-check-label" for="interest-tech">Technology</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" id="interest-business" class="form-check-input" value="business">
                            <label class="form-check-label" for="interest-business">Business</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" id="interest-design" class="form-check-input" value="design">
                            <label class="form-check-label" for="interest-design">Design</label>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>Radio Buttons</h3>
                    <div class="form-group">
                        <label class="form-label form-label--required">Experience Level</label>
                        <div class="form-check">
                            <input type="radio" id="exp-beginner" name="experience" class="form-check-input" value="beginner">
                            <label class="form-check-label" for="exp-beginner">Beginner (0-2 years)</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" id="exp-intermediate" name="experience" class="form-check-input" value="intermediate">
                            <label class="form-check-label" for="exp-intermediate">Intermediate (3-5 years)</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" id="exp-advanced" name="experience" class="form-check-input" value="advanced">
                            <label class="form-check-label" for="exp-advanced">Advanced (5+ years)</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Groups -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Input Groups</h2>
            <div class="demo-grid">
                <div>
                    <h3>Input with Prefix</h3>
                    <div class="form-group">
                        <label class="form-label" for="website-input">Website URL</label>
                        <div class="input-group">
                            <span class="input-group-text">https://</span>
                            <input type="url" id="website-input" class="form-control" placeholder="www.example.com">
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>Input with Suffix</h3>
                    <div class="form-group">
                        <label class="form-label" for="price-input">Price</label>
                        <div class="input-group">
                            <input type="number" id="price-input" class="form-control" placeholder="0.00">
                            <span class="input-group-text">USD</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Validation States -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Validation States</h2>
            <div class="demo-grid">
                <div>
                    <h3>Valid State</h3>
                    <div class="form-group">
                        <label class="form-label" for="valid-input">Valid Input</label>
                        <input type="text" id="valid-input" class="form-control is-valid" value="Valid input value">
                        <div class="form-feedback form-feedback--valid">
                            <i class="fas fa-check-circle"></i> Looks good!
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3>Invalid State</h3>
                    <div class="form-group">
                        <label class="form-label" for="invalid-input">Invalid Input</label>
                        <input type="text" id="invalid-input" class="form-control is-invalid" value="Invalid input">
                        <div class="form-feedback form-feedback--invalid">
                            <i class="fas fa-exclamation-circle"></i> Please provide a valid input.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Layouts -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Form Layouts</h2>
            
            <h3>Responsive Form Row</h3>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="first-name">First Name</label>
                    <input type="text" id="first-name" class="form-control" placeholder="First name">
                </div>
                <div class="form-group">
                    <label class="form-label" for="last-name">Last Name</label>
                    <input type="text" id="last-name" class="form-control" placeholder="Last name">
                </div>
                <div class="form-group">
                    <label class="form-label" for="email-row">Email</label>
                    <input type="email" id="email-row" class="form-control" placeholder="Email address">
                </div>
            </div>
            
            <h3>Inline Form Group</h3>
            <div class="form-group form-group--inline">
                <label class="form-label" for="inline-input">Company Name</label>
                <div class="form-control-wrapper">
                    <input type="text" id="inline-input" class="form-control" placeholder="Enter company name">
                    <div class="form-help">This will be displayed on your profile</div>
                </div>
            </div>
        </div>

        <!-- Complete Contact Form Example -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Complete Contact Form Example</h2>
            <form class="form" novalidate>
                <div class="form__section">
                    <h3 class="form__title">Contact Information</h3>
                    <p class="form__subtitle">Please fill out the form below and we'll get back to you as soon as possible.</p>
                    
                    <div class="form-row form-row--2-cols">
                        <div class="form-group">
                            <label class="form-label form-label--required" for="contact-first-name">First Name</label>
                            <input type="text" id="contact-first-name" class="form-control" placeholder="John" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label form-label--required" for="contact-last-name">Last Name</label>
                            <input type="text" id="contact-last-name" class="form-control" placeholder="Doe" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label form-label--required" for="contact-email">Email Address</label>
                        <input type="email" id="contact-email" class="form-control" placeholder="<EMAIL>" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label form-label--optional" for="contact-phone">Phone Number</label>
                        <input type="tel" id="contact-phone" class="form-control" placeholder="+****************">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label form-label--required" for="contact-subject">Subject</label>
                        <select id="contact-subject" class="form-select" required>
                            <option value="">Choose a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="support">Technical Support</option>
                            <option value="sales">Sales Question</option>
                            <option value="partnership">Partnership Opportunity</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label form-label--required" for="contact-message">Message</label>
                        <textarea id="contact-message" class="form-textarea" placeholder="Please describe your inquiry in detail..." required></textarea>
                        <div class="form-help">Minimum 10 characters required</div>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" id="contact-newsletter" class="form-check-input" value="yes">
                            <label class="form-check-label" for="contact-newsletter">
                                Subscribe to our newsletter for updates and news
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" id="contact-terms" class="form-check-input" value="yes" required>
                            <label class="form-check-label" for="contact-terms">
                                I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                            </label>
                        </div>
                    </div>
                    
                    <div class="btn-group btn-group--center">
                        <button type="submit" class="btn btn--primary btn--lg">
                            <i class="fas fa-paper-plane btn__icon btn__icon--left"></i>
                            Send Message
                        </button>
                        <button type="reset" class="btn btn--secondary btn--lg">
                            Clear Form
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Demo form validation
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    // Add validation classes
                    form.classList.add('was-validated');
                    
                    // Demo success message
                    alert('Form submitted successfully! (This is just a demo)');
                });
            });
            
            // Real-time validation demo
            const emailInputs = document.querySelectorAll('input[type="email"]');
            emailInputs.forEach(input => {
                input.addEventListener('blur', function() {
                    const isValid = this.checkValidity();
                    this.classList.toggle('is-valid', isValid);
                    this.classList.toggle('is-invalid', !isValid);
                });
            });
        });
    </script>
</body>
</html>
