const mix = require('laravel-mix');

// Production optimizations
if (mix.inProduction()) {
  mix.options({
    terser: {
      terserOptions: {
        compress: {
          drop_console: true,
        },
      },
    },
  });
}

// Configure paths
mix.setPublicPath('assets/dist');

// JavaScript compilation with code splitting
mix
  .js('assets/js/app.js', 'js')
  .extract(['jquery', '@popperjs/core', 'bootstrap', 'swiper'])
  .js('assets/js/i18n.js', 'js')
  .js('assets/js/scrolling-tabs.js', 'js');

// SCSS compilation with optimization
mix
  .sass('assets/sass/style.scss', 'css/style-legacy.css')
  .sass('assets/src/scss/main.scss', 'css/style.css')
  .options({
    processCssUrls: false,
    postCss: [
      require('autoprefixer'),
      require('cssnano')({
        preset: 'default',
      }),
    ],
  });

// Image optimization
mix.copy('assets/images', 'assets/dist/images');

// Font copying
mix.copy('assets/fonts', 'assets/dist/fonts');

// Copy i18n files
mix.copy('assets/i18n', 'assets/dist/i18n');

// Copy presentations
mix.copy('assets/presentations', 'assets/dist/presentations');

// Browser sync configuration
if (!mix.inProduction()) {
  mix.browserSync({
    proxy: false, // Set to your local server URL if using one
    server: {
      baseDir: './', // Serve from project root
      index: 'index.html', // Adjust if needed
    },
    files: [
      'assets/dist/css/**/*.css',
      'assets/dist/js/**/*.js',
      '**/*.html',
      '**/*.php',
      'assets/dist/css/*.css',
      'assets/dist/js/*.js',
    ],
    watchOptions: {
      usePolling: true,
      interval: 1000,
    },
    open: false, // Don't auto-open browser
    notify: false, // Disable browser notifications
  });
}

// Autoload jQuery and Popper
mix.autoload({
  jquery: [
    '$',
    'window.jQuery',
    'jQuery',
    'window.$',
    'jquery',
    'window.jquery',
  ],
  '@popperjs/core': ['Popper'],
});

// Versioning for cache busting in production
if (mix.inProduction()) {
  mix.version();
}

// Source maps for development
if (!mix.inProduction()) {
  mix.sourceMaps();
}

// Browser sync for development
// (Moved to consolidated block above)
