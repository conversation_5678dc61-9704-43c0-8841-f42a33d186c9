<!-- Mobile-First Navigation Component -->
<!-- This is the new navigation structure to replace the existing header -->

<header class="navigation__header">
  <div class="container">
    <div class="row align-items-center justify-content-between">
      <!-- Logo -->
      <div class="col-auto">
        <a href="home.html" class="navigation__logo-link">
          <img src="assets/images/logo-vima.svg" class="navigation__logo" alt="VIMA Holding Logo">
        </a>
      </div>
      
      <!-- Controls (Language Switcher + Menu Toggle) -->
      <div class="col-auto">
        <div class="navigation__controls">
          <!-- Language Switcher -->
          <div class="language-switcher__container">
            <select id="language-selector" class="language-switcher__select">
              <option value="en">EN</option>
              <option value="fr">FR</option>
            </select>
            <i class="fas fa-chevron-down language-switcher__icon"></i>
          </div>
          
          <!-- Navigation Toggle Button -->
          <button class="navigation__toggle" 
                  aria-label="Toggle navigation menu" 
                  aria-expanded="false" 
                  aria-controls="navigation-panel">
            <span class="navigation__toggle__line"></span>
            <span class="navigation__toggle__line"></span>
            <span class="navigation__toggle__line"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</header>

<!-- Navigation Panel (Slide-in) -->
<div class="navigation__panel" 
     id="navigation-panel" 
     role="navigation" 
     aria-label="Main navigation" 
     aria-hidden="true">
  
  <!-- Main Navigation Menu -->
  <nav class="navigation__menu">
    
    <!-- Primary Navigation Section -->
    <div class="navigation__menu__section">
      <h3 class="navigation__menu__title" data-i18n="navigation.sections.main">Main</h3>
      <ul class="navigation__menu__list">
        <li class="navigation__menu__item">
          <a href="about-us.html" class="navigation__menu__link">
            <i class="fas fa-info-circle navigation__menu__icon"></i>
            <span data-i18n="navigation.about">About Us</span>
          </a>
        </li>
        <li class="navigation__menu__item">
          <a href="vima-partnerships.html" class="navigation__menu__link">
            <i class="fas fa-handshake navigation__menu__icon"></i>
            <span data-i18n="navigation.partnerships">Partnerships</span>
          </a>
        </li>
      </ul>
    </div>
    
    <!-- Business Units Section with Progressive Disclosure -->
    <div class="navigation__menu__section">
      <h3 class="navigation__menu__title" data-i18n="navigation.sections.business">Business Units</h3>
      <ul class="navigation__menu__list">
        <li class="navigation__menu__item">
          <a href="vima-trading.html" class="navigation__menu__link">
            <i class="fas fa-chart-line navigation__menu__icon"></i>
            <span data-i18n="navigation.trading">VIMA Trading</span>
          </a>
        </li>
        <li class="navigation__menu__item">
          <a href="vima-technology.html" class="navigation__menu__link">
            <i class="fas fa-laptop-code navigation__menu__icon"></i>
            <span data-i18n="navigation.technology">VIMA Technology</span>
          </a>
        </li>
        <li class="navigation__menu__item">
          <a href="vima-investments.html" class="navigation__menu__link">
            <i class="fas fa-coins navigation__menu__icon"></i>
            <span data-i18n="navigation.investments">VIMA Investments</span>
          </a>
        </li>
        <li class="navigation__menu__item">
          <a href="vima-communication.html" class="navigation__menu__link">
            <i class="fas fa-bullhorn navigation__menu__icon"></i>
            <span data-i18n="navigation.communications">VIMA Communications</span>
          </a>
        </li>
      </ul>
    </div>
    
    <!-- More Section with Submenu -->
    <div class="navigation__menu__section">
      <h3 class="navigation__menu__title" data-i18n="navigation.sections.more">More</h3>
      <ul class="navigation__menu__list">
        <li class="navigation__menu__item">
          <button class="navigation__submenu__toggle" 
                  aria-expanded="false" 
                  aria-controls="submenu-more">
            <div class="navigation__submenu__content-wrapper">
              <i class="fas fa-ellipsis-h navigation__submenu__icon"></i>
              <span data-i18n="navigation.more">More Options</span>
            </div>
            <i class="fas fa-chevron-down navigation__submenu__arrow"></i>
          </button>
          
          <!-- Submenu Content -->
          <div class="navigation__submenu__content" id="submenu-more">
            <ul class="navigation__submenu__list">
              <li class="navigation__submenu__item">
                <a href="#" class="navigation__submenu__link" data-i18n="navigation.fellows">Fellows</a>
              </li>
              <li class="navigation__submenu__item">
                <a href="#" class="navigation__submenu__link" data-i18n="navigation.terms">Terms & Conditions</a>
              </li>
              <li class="navigation__submenu__item">
                <a href="#" class="navigation__submenu__link" data-i18n="navigation.privacy">Privacy Policy</a>
              </li>
            </ul>
          </div>
        </li>
      </ul>
    </div>
  </nav>
  
  <!-- Contact Information -->
  <div class="navigation__contact">
    <h4 class="navigation__contact__title" data-i18n="navigation.contact.title">Contact Information</h4>
    <ul class="navigation__contact__list">
      <li class="navigation__contact__item">
        <i class="fas fa-map-marker-alt navigation__contact__icon"></i>
        <span data-i18n="contact.address">123 Business Street, City, Country</span>
      </li>
      <li class="navigation__contact__item">
        <i class="fas fa-envelope navigation__contact__icon"></i>
        <span data-i18n="contact.email"><EMAIL></span>
      </li>
      <li class="navigation__contact__item">
        <i class="fas fa-phone navigation__contact__icon"></i>
        <span data-i18n="contact.phone">+****************</span>
      </li>
    </ul>
  </div>
</div>

<!-- Navigation Overlay -->
<div class="navigation__overlay" aria-hidden="true"></div>

<!-- Screen Reader Only Styles -->
<style>
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>

<!-- Navigation Component Initialization -->
<script>
// This will be integrated into the main app.js
document.addEventListener('DOMContentLoaded', function() {
  // Initialize navigation component
  if (typeof Navigation !== 'undefined') {
    window.vimaNavigation = new Navigation();
  }
  
  // Language switcher functionality
  const languageSelector = document.getElementById('language-selector');
  if (languageSelector) {
    languageSelector.addEventListener('change', function(e) {
      // Integrate with existing i18n system
      if (window.changeLanguage) {
        window.changeLanguage(e.target.value);
      }
    });
  }
});
</script>
