# VIMA Holding - Frontend Architecture Plan

## Overview

This document outlines the technical architecture for the complete frontend rebuild of the VIMA Holding website. The plan focuses on creating a modern, scalable, and maintainable frontend while preserving all existing functionality and backend integrations.

## Current Architecture Analysis

### Existing Technology Stack

- **Build System**: Laravel Mix 2.0.0 with Webpack
- **CSS Framework**: Bootstrap 4.0.0
- **CSS Preprocessor**: SCSS
- **JavaScript**: jQuery 3.3.1 + ES5
- **Slider Library**: Swiper.js 6.8.4
- **Icons**: Font Awesome 5.15.0
- **Fonts**: Proxima Nova Alt (custom)
- **Internationalization**: Custom JSON-based system

### Current File Structure

```
assets/
├── dist/           # Compiled assets
├── fonts/          # Custom font files
├── i18n/           # Translation files
├── images/         # Static images
├── js/             # JavaScript source
├── sass/           # SCSS source files
└── presentations/  # PDF files
```

### Current Build Process

- Laravel Mix compiles SCSS to CSS
- JavaScript bundling with vendor extraction
- Basic asset optimization
- Browser-sync for development

## Proposed Modern Architecture

### Enhanced Technology Stack

- **Build System**: Laravel Mix 6.x (latest) with optimized Webpack 5
- **CSS Framework**: Bootstrap 5.x + Custom Design System
- **CSS Architecture**: SCSS with BEM methodology + CSS Custom Properties
- **JavaScript**: Modern ES6+ with modular architecture
- **Component System**: Reusable SCSS/JS component library
- **Performance**: Advanced optimization and code splitting
- **Development**: Enhanced developer experience with hot reloading

### New File Structure

```
assets/
├── dist/                    # Compiled assets (optimized)
├── src/
│   ├── scss/
│   │   ├── abstracts/       # Variables, mixins, functions
│   │   ├── base/           # Reset, typography, base styles
│   │   ├── components/     # Reusable components
│   │   ├── layout/         # Header, footer, grid
│   │   ├── pages/          # Page-specific styles
│   │   ├── themes/         # Theme variations
│   │   └── vendors/        # Third-party overrides
│   ├── js/
│   │   ├── components/     # Reusable JS components
│   │   ├── modules/        # Feature modules
│   │   ├── utils/          # Utility functions
│   │   └── app.js          # Main entry point
│   └── images/
│       ├── optimized/      # Optimized images
│       ├── svg/            # SVG icons
│       └── raw/            # Source images
├── fonts/                  # Font files
├── i18n/                   # Translation files
└── presentations/          # PDF files
```

## Design System Architecture

### 1. SCSS Architecture (BEM + 7-1 Pattern)

#### Abstracts Layer

```scss
// _variables.scss - Design tokens
$colors: (
  primary: (
    50: #f0f9ff,
    100: #e0f2fe,
    500: #0ea5e9,
    900: #0c4a6e,
  ),
  // ... more color scales
);

$spacing: (
  xs: 0.25rem,
  sm: 0.5rem,
  md: 1rem,
  lg: 1.5rem,
  xl: 2rem,
  // ... more spacing values
);

$typography: (
  font-family: (
    primary: (
      'Proxima Nova Alt',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif,
    ),
    heading: (
      'Proxima Nova Alt Bold',
      -apple-system,
      BlinkMacSystemFont,
      sans-serif,
    ),
  ),
  font-size: (
    xs: 0.75rem,
    sm: 0.875rem,
    base: 1rem,
    lg: 1.125rem,
    xl: 1.25rem,
    // ... more sizes
  ),
);
```

#### Component Architecture

```scss
// Button component example
.btn {
  // Base button styles
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-family: var(--font-family-primary);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;

  // Modifiers
  &--primary {
    background: linear-gradient(
      105deg,
      var(--color-primary-500) 0%,
      var(--color-primary-700) 100%
    );
    color: var(--color-white);
    box-shadow: 0 4px 12px rgba(var(--color-primary-500-rgb), 0.3);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(var(--color-primary-500-rgb), 0.4);
    }
  }

  &--secondary {
    background: var(--color-gray-100);
    color: var(--color-gray-900);
    border: 1px solid var(--color-gray-300);

    &:hover {
      background: var(--color-gray-200);
    }
  }

  // States
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
}
```

### 2. JavaScript Architecture

#### Modular Component System

```javascript
// components/Navigation.js
class Navigation {
  constructor(element) {
    this.element = element;
    this.toggleButton = element.querySelector('.nav-toggle');
    this.overlay = element.querySelector('.nav-overlay');
    this.isOpen = false;

    this.init();
  }

  init() {
    this.bindEvents();
    this.setupAccessibility();
  }

  bindEvents() {
    this.toggleButton.addEventListener('click', () => this.toggle());
    this.overlay.addEventListener('click', (e) => this.handleOverlayClick(e));
    document.addEventListener('keydown', (e) => this.handleKeydown(e));
  }

  toggle() {
    this.isOpen ? this.close() : this.open();
  }

  open() {
    this.element.classList.add('nav--open');
    this.toggleButton.setAttribute('aria-expanded', 'true');
    this.isOpen = true;
    document.body.classList.add('nav-open');
  }

  close() {
    this.element.classList.remove('nav--open');
    this.toggleButton.setAttribute('aria-expanded', 'false');
    this.isOpen = false;
    document.body.classList.remove('nav-open');
  }

  handleKeydown(e) {
    if (e.key === 'Escape' && this.isOpen) {
      this.close();
    }
  }
}

// Auto-initialize
document.addEventListener('DOMContentLoaded', () => {
  const navElements = document.querySelectorAll('.navigation');
  navElements.forEach((nav) => new Navigation(nav));
});
```

#### Utility Functions

```javascript
// utils/animations.js
export const fadeIn = (element, duration = 300) => {
  element.style.opacity = '0';
  element.style.display = 'block';

  const start = performance.now();

  const animate = (currentTime) => {
    const elapsed = currentTime - start;
    const progress = Math.min(elapsed / duration, 1);

    element.style.opacity = progress;

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

// utils/debounce.js
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};
```

## Build System Optimization

### Enhanced webpack.mix.js Configuration

```javascript
const mix = require('laravel-mix');

// Production optimizations
if (mix.inProduction()) {
  mix.options({
    terser: {
      terserOptions: {
        compress: {
          drop_console: true,
        },
      },
    },
  });
}

// Configure paths
mix.setPublicPath('assets/dist');

// JavaScript compilation with code splitting
mix
  .js('assets/src/js/app.js', 'js')
  .extract(['jquery', 'bootstrap', 'swiper'])
  .js('assets/src/js/pages/home.js', 'js/pages')
  .js('assets/src/js/pages/about.js', 'js/pages');

// SCSS compilation with optimization
mix
  .sass('assets/src/scss/app.scss', 'css')
  .sass('assets/src/scss/critical.scss', 'css')
  .options({
    processCssUrls: false,
    postCss: [
      require('autoprefixer'),
      require('cssnano')({
        preset: 'default',
      }),
    ],
  });

// Image optimization
mix.copy('assets/src/images', 'assets/dist/images');

// Font copying
mix.copy('assets/fonts', 'assets/dist/fonts');

// Browser sync configuration
mix.browserSync({
  proxy: false,
  server: {
    baseDir: './',
  },
  files: ['*.html', 'assets/dist/css/*.css', 'assets/dist/js/*.js'],
  notify: false,
});

// Versioning for cache busting in production
if (mix.inProduction()) {
  mix.version();
}

// Source maps for development
if (!mix.inProduction()) {
  mix.sourceMaps();
}
```

### Package.json Updates

```json
{
  "name": "vima-holding-frontend",
  "version": "2.0.0",
  "description": "Modern frontend for VIMA Holding website",
  "scripts": {
    "dev": "npm run development",
    "development": "mix",
    "watch": "mix watch",
    "watch-poll": "mix watch -- --watch-options-poll=1000",
    "hot": "mix watch --hot",
    "prod": "npm run production",
    "production": "mix --production",
    "build": "npm run production",
    "lint:css": "stylelint 'assets/src/scss/**/*.scss' --fix",
    "lint:js": "eslint 'assets/src/js/**/*.js' --fix",
    "optimize:images": "imagemin 'assets/src/images/**/*' --out-dir='assets/dist/images'",
    "analyze": "npm run production -- --analyze"
  },
  "devDependencies": {
    "laravel-mix": "^6.0.49",
    "autoprefixer": "^10.4.0",
    "cssnano": "^5.1.0",
    "eslint": "^8.0.0",
    "stylelint": "^14.0.0",
    "imagemin": "^8.0.0",
    "imagemin-webp": "^6.0.0"
  },
  "dependencies": {
    "bootstrap": "^5.2.0",
    "swiper": "^8.4.0",
    "intersection-observer": "^0.12.0"
  }
}
```

## Performance Optimization Strategy

### 1. Critical CSS Implementation

- Extract above-the-fold CSS
- Inline critical styles in HTML head
- Load non-critical CSS asynchronously

### 2. JavaScript Optimization

- Code splitting by page/feature
- Lazy loading for non-essential components
- Tree shaking to eliminate unused code
- Modern ES6+ with Babel transpilation

### 3. Image Optimization

- WebP format with fallbacks
- Responsive images with srcset
- Lazy loading implementation
- SVG optimization for icons

### 4. Caching Strategy

- Asset versioning for cache busting
- Service worker for offline functionality
- Browser caching optimization

## Component Library Structure

### Core Components

#### 1. Navigation Component

```scss
.navigation {
  &__brand {
    /* Logo styles */
  }
  &__toggle {
    /* Mobile menu toggle */
  }
  &__menu {
    /* Navigation menu */
  }
  &__item {
    /* Menu items */
  }
  &__link {
    /* Menu links */
  }

  // States
  &--open {
    /* Open state */
  }
  &--scrolled {
    /* Scrolled state */
  }
}
```

#### 2. Button Component

```scss
.btn {
  // Base styles

  // Variants
  &--primary {
    /* Primary button */
  }
  &--secondary {
    /* Secondary button */
  }
  &--outline {
    /* Outline button */
  }

  // Sizes
  &--sm {
    /* Small button */
  }
  &--lg {
    /* Large button */
  }

  // States
  &:hover {
    /* Hover state */
  }
  &:focus {
    /* Focus state */
  }
  &:disabled {
    /* Disabled state */
  }
}
```

#### 3. Card Component

```scss
.card {
  &__header {
    /* Card header */
  }
  &__body {
    /* Card body */
  }
  &__footer {
    /* Card footer */
  }
  &__image {
    /* Card image */
  }

  // Variants
  &--elevated {
    /* Elevated card */
  }
  &--outlined {
    /* Outlined card */
  }
  &--interactive {
    /* Interactive card */
  }
}
```

### Layout Components

#### 1. Grid System

```scss
.container {
  // Container styles with max-width
}

.row {
  // Flexbox row
}

.col {
  // Flexible column

  // Responsive variants
  &-sm-6 {
    /* Small screen: 50% width */
  }
  &-md-4 {
    /* Medium screen: 33% width */
  }
  &-lg-3 {
    /* Large screen: 25% width */
  }
}
```

#### 2. Section Component

```scss
.section {
  // Base section styles

  // Variants
  &--hero {
    /* Hero section */
  }
  &--feature {
    /* Feature section */
  }
  &--testimonial {
    /* Testimonial section */
  }

  // Spacing
  &--sm {
    /* Small padding */
  }
  &--lg {
    /* Large padding */
  }
}
```

## Responsive Design Strategy

### Breakpoint System

```scss
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
);

// Mixins for responsive design
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}
```

### Mobile-First Approach

1. Design for mobile first
2. Progressive enhancement for larger screens
3. Touch-friendly interactions
4. Optimized performance for mobile devices

## Accessibility Implementation

### 1. Semantic HTML

- Proper heading hierarchy
- Meaningful alt text for images
- Form labels and descriptions
- Landmark roles

### 2. ARIA Implementation

- ARIA labels for interactive elements
- Live regions for dynamic content
- Focus management
- Screen reader optimization

### 3. Keyboard Navigation

- Tab order optimization
- Keyboard shortcuts
- Focus indicators
- Skip links

## Testing Strategy

### 1. Cross-Browser Testing

- Chrome, Firefox, Safari, Edge
- Mobile browsers (iOS Safari, Chrome Mobile)
- Legacy browser support (IE11 if required)

### 2. Device Testing

- Desktop (1920x1080, 1366x768)
- Tablet (iPad, Android tablets)
- Mobile (iPhone, Android phones)

### 3. Performance Testing

- Lighthouse audits
- WebPageTest analysis
- Core Web Vitals monitoring

## Migration Plan

### Phase 1: Infrastructure Setup (Week 1)

1. Update build system to Laravel Mix 6.x
2. Restructure file organization
3. Set up new SCSS architecture
4. Configure performance optimizations

### Phase 2: Design System (Week 2)

1. Implement design tokens
2. Create base components
3. Set up typography system
4. Establish color system

### Phase 3: Component Development (Week 3-4)

1. Build navigation component
2. Create button variations
3. Develop card components
4. Implement form components

### Phase 4: Page Implementation (Week 5-6)

1. Rebuild homepage
2. Update about page
3. Modernize business unit pages
4. Enhance partnerships page

### Phase 5: Optimization & Testing (Week 7-8)

1. Performance optimization
2. Cross-browser testing
3. Accessibility audit
4. Final polish and documentation

This architecture plan provides a solid foundation for creating a modern, maintainable, and high-performance frontend while preserving all existing functionality.
