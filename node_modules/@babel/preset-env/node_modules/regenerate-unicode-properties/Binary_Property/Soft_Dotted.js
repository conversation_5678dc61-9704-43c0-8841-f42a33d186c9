const set = require('regenerate')(0x12F, 0x249, 0x268, 0x29D, 0x2B2, 0x3F3, 0x456, 0x458, 0x1D62, 0x1D96, 0x1DA4, 0x1DA8, 0x1E2D, 0x1ECB, 0x2071, 0x2C7C, 0x1DF1A, 0x1E068);
set.addRange(0x69, 0x6A).addRange(0x2148, 0x2149).addRange(0x1D422, 0x1D423).addRange(0x1D456, 0x1D457).addRange(0x1D48A, 0x1D48B).addRange(0x1D4BE, 0x1D4BF).addRange(0x1D4F2, 0x1D4F3).addRange(0x1D526, 0x1D527).addRange(0x1D55A, 0x1D55B).addRange(0x1D58E, 0x1D58F).addRange(0x1D5C2, 0x1D5C3).addRange(0x1D5F6, 0x1D5F7).addRange(0x1D62A, 0x1D62B).addRange(0x1D65E, 0x1D65F).addRange(0x1D692, 0x1D693).addRange(0x1E04C, 0x1E04D);
exports.characters = set;
