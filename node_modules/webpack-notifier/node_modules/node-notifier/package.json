{"name": "node-notifier", "version": "9.0.1", "description": "A Node.js module for sending notifications on native Mac, Windows (post and pre 8) and Linux (or Growl as fallback)", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "jest", "example": "node ./example/message.js", "example:mac": "node ./example/advanced.js", "example:mac:input": "node ./example/macInput.js", "example:windows": "node ./example/toaster.js", "example:windows:actions": "node ./example/toaster-with-actions.js", "lint": "eslint example/*.js lib/*.js notifiers/*.js test/**/*.js index.js"}, "jest": {"testRegex": "/test/[^_]*.js", "testEnvironment": "node", "setupFilesAfterEnv": ["./test/_test-matchers.js"]}, "repository": {"type": "git", "url": "git+ssh://**************/mikaelbr/node-notifier.git"}, "keywords": ["notification center", "mac os x 10.8", "notify", "terminal-notifier", "notify-send", "growl", "windows 8 notification", "toaster", "notification"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"eslint": "^7.6.0", "eslint-config-semistandard": "^15.0.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "husky": "^4.2.5", "jest": "^26.4.0", "lint-staged": "^10.2.11", "prettier": "^2.0.5"}, "dependencies": {"growly": "^1.3.0", "is-wsl": "^2.2.0", "semver": "^7.3.2", "shellwords": "^0.1.1", "uuid": "^8.3.0", "which": "^2.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,json,css,md}": ["prettier --write", "git add"]}, "bugs": {"url": "https://github.com/mikaelbr/node-notifier/issues"}, "homepage": "https://github.com/mikaelbr/node-notifier#readme", "directories": {"example": "example", "test": "test"}}