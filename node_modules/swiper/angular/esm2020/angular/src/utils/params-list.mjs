/* underscore in name -> watch for changes */
export const paramsList = [
    'init',
    'enabled',
    '_direction',
    'touchEventsTarget',
    'initialSlide',
    '_speed',
    'cssMode',
    'updateOnWindowResize',
    'resizeObserver',
    'nested',
    'focusableElements',
    '_width',
    '_height',
    'preventInteractionOnTransition',
    'userAgent',
    'url',
    '_edgeSwipeDetection',
    '_edgeSwipeThreshold',
    '_freeMode',
    '_autoHeight',
    'setWrapperSize',
    'virtualTranslate',
    '_effect',
    'breakpoints',
    '_spaceBetween',
    '_slidesPerView',
    'maxBackfaceHiddenSlides',
    '_grid',
    '_slidesPerGroup',
    '_slidesPerGroupSkip',
    '_slidesPerGroupAuto',
    '_centeredSlides',
    '_centeredSlidesBounds',
    '_slidesOffsetBefore',
    '_slidesOffsetAfter',
    'normalizeSlideIndex',
    '_centerInsufficientSlides',
    '_watchOverflow',
    'roundLengths',
    'touchRatio',
    'touchAngle',
    'simulateTouch',
    '_shortSwipes',
    '_longSwipes',
    'longSwipesRatio',
    'longSwipesMs',
    '_followFinger',
    'allowTouchMove',
    '_threshold',
    'touchMoveStopPropagation',
    'touchStartPreventDefault',
    'touchStartForcePreventDefault',
    'touchReleaseOnEdges',
    'uniqueNavElements',
    '_resistance',
    '_resistanceRatio',
    '_watchSlidesProgress',
    '_grabCursor',
    'preventClicks',
    'preventClicksPropagation',
    '_slideToClickedSlide',
    '_preloadImages',
    'updateOnImagesReady',
    '_loop',
    '_loopAdditionalSlides',
    '_loopedSlides',
    '_loopedSlidesLimit',
    '_loopFillGroupWithBlank',
    'loopPreventsSlide',
    '_rewind',
    '_allowSlidePrev',
    '_allowSlideNext',
    '_swipeHandler',
    '_noSwiping',
    'noSwipingClass',
    'noSwipingSelector',
    'passiveListeners',
    'containerModifierClass',
    'slideClass',
    'slideBlankClass',
    'slideActiveClass',
    'slideDuplicateActiveClass',
    'slideVisibleClass',
    'slideDuplicateClass',
    'slideNextClass',
    'slideDuplicateNextClass',
    'slidePrevClass',
    'slideDuplicatePrevClass',
    'wrapperClass',
    'runCallbacksOnInit',
    'observer',
    'observeParents',
    'observeSlideChildren',
    // modules
    'a11y',
    'autoplay',
    '_controller',
    'coverflowEffect',
    'cubeEffect',
    'fadeEffect',
    'flipEffect',
    'creativeEffect',
    'cardsEffect',
    'hashNavigation',
    'history',
    'keyboard',
    'lazy',
    'mousewheel',
    '_navigation',
    '_pagination',
    'parallax',
    '_scrollbar',
    '_thumbs',
    'virtual',
    'zoom',
    'on',
];
//# sourceMappingURL=data:application/json;base64,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