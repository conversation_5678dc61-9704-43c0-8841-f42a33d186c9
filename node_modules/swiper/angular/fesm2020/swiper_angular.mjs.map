{"version": 3, "file": "swiper_angular.mjs", "sources": ["../../../src/angular/src/utils/params-list.ts", "../../../src/angular/src/utils/utils.ts", "../../../src/angular/src/utils/get-params.ts", "../../../src/angular/src/swiper-slide.directive.ts", "../../../src/angular/src/swiper.component.ts", "../../../src/angular/src/swiper.component.html", "../../../src/angular/src/swiper.module.ts", "../../../src/angular/src/public-api.ts", "../../../src/swiper_angular.ts"], "sourcesContent": ["/* underscore in name -> watch for changes */\nexport const paramsList = [\n  'init',\n  'enabled',\n  '_direction',\n  'touchEventsTarget',\n  'initialSlide',\n  '_speed',\n  'cssMode',\n  'updateOnWindowResize',\n  'resizeObserver',\n  'nested',\n  'focusableElements',\n  '_width',\n  '_height',\n  'preventInteractionOnTransition',\n  'userAgent',\n  'url',\n  '_edgeSwipeDetection',\n  '_edgeSwipeThreshold',\n  '_freeMode',\n  '_autoHeight',\n  'setWrapperSize',\n  'virtualTranslate',\n  '_effect',\n  'breakpoints',\n  '_spaceBetween',\n  '_slidesPerView',\n  'maxBackfaceHiddenSlides',\n  '_grid',\n  '_slidesPerGroup',\n  '_slidesPerGroupSkip',\n  '_slidesPerGroupAuto',\n  '_centeredSlides',\n  '_centeredSlidesBounds',\n  '_slidesOffsetBefore',\n  '_slidesOffsetAfter',\n  'normalizeSlideIndex',\n  '_centerInsufficientSlides',\n  '_watchOverflow',\n  'roundLengths',\n  'touchRatio',\n  'touchAngle',\n  'simulateTouch',\n  '_shortSwipes',\n  '_longSwipes',\n  'longSwipesRatio',\n  'longSwipesMs',\n  '_followFinger',\n  'allowTouchMove',\n  '_threshold',\n  'touchMoveStopPropagation',\n  'touchStartPreventDefault',\n  'touchStartForcePreventDefault',\n  'touchReleaseOnEdges',\n  'uniqueNavElements',\n  '_resistance',\n  '_resistanceRatio',\n  '_watchSlidesProgress',\n  '_grabCursor',\n  'preventClicks',\n  'preventClicksPropagation',\n  '_slideToClickedSlide',\n  '_preloadImages',\n  'updateOnImagesReady',\n  '_loop',\n  '_loopAdditionalSlides',\n  '_loopedSlides',\n  '_loopedSlidesLimit',\n  '_loopFillGroupWithBlank',\n  'loopPreventsSlide',\n  '_rewind',\n  '_allowSlidePrev',\n  '_allowSlideNext',\n  '_swipeHandler',\n  '_noSwiping',\n  'noSwipingClass',\n  'noSwipingSelector',\n  'passiveListeners',\n  'containerModifierClass',\n  'slideClass',\n  'slideBlankClass',\n  'slideActiveClass',\n  'slideDuplicateActiveClass',\n  'slideVisibleClass',\n  'slideDuplicateClass',\n  'slideNextClass',\n  'slideDuplicateNextClass',\n  'slidePrevClass',\n  'slideDuplicatePrevClass',\n  'wrapperClass',\n  'runCallbacksOnInit',\n  'observer',\n  'observeParents',\n  'observeSlideChildren',\n\n  // modules\n  'a11y',\n  'autoplay',\n  '_controller',\n  'coverflowEffect',\n  'cubeEffect',\n  'fadeEffect',\n  'flipEffect',\n  'creativeEffect',\n  'cardsEffect',\n  'hashNavigation',\n  'history',\n  'keyboard',\n  'lazy',\n  'mousewheel',\n  '_navigation',\n  '_pagination',\n  'parallax',\n  '_scrollbar',\n  '_thumbs',\n  'virtual',\n  'zoom',\n  'on',\n];\n", "export function isObject(o: any): boolean {\n  return (\n    typeof o === 'object' &&\n    o !== null &&\n    o.constructor &&\n    Object.prototype.toString.call(o).slice(8, -1) === 'Object'\n  );\n}\n\nexport function isEnabled(val: boolean | { enabled?: boolean }) {\n  return typeof val !== 'undefined' && typeof val !== 'boolean' && val.enabled === true;\n}\n\nexport function isShowEl(val: any, obj: any, el: any): boolean {\n  return (\n    (coerceBooleanProperty(val) === true && obj && !obj.el) ||\n    !(\n      typeof obj !== 'boolean' &&\n      obj.el !== el?.nativeElement &&\n      (typeof obj.el === 'string' || typeof obj.el === 'object')\n    )\n  );\n}\n\nexport function extend(target: any, src: any) {\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src)\n    .filter((key) => noExtend.indexOf(key) < 0)\n    .forEach((key) => {\n      if (typeof target[key] === 'undefined') {\n        target[key] = src[key];\n        return;\n      }\n      if (target[key] && !src[key]) {\n        return;\n      }\n      if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n        if (src[key].__swiper__) target[key] = src[key];\n        else extend(target[key], src[key]);\n      } else {\n        target[key] = src[key];\n      }\n    });\n}\n\nexport function coerceBooleanProperty(value: any): boolean {\n  return value != null && `${value}` !== 'false';\n}\n\nexport const ignoreNgOnChanges = ['pagination', 'navigation', 'scrollbar', 'virtual'];\n\nexport function setProperty(val: any, obj = {}): {} | false {\n  if (isObject(val)) {\n    return val;\n  }\n\n  if (coerceBooleanProperty(val) === true) {\n    return obj;\n  }\n\n  return false;\n}\n", "// @ts-ignore\nimport Swiper from 'swiper';\nimport { paramsList } from './params-list';\nimport { extend, isObject } from './utils';\ntype KeyValueType = { [x: string]: any };\nexport const allowedParams = paramsList.map((key) => key.replace(/_/, ''));\nexport function getParams(obj: any = {}) {\n  const params: any = {\n    on: {},\n  };\n  // const events = {};\n  const passedParams: KeyValueType = {};\n  extend(params, Swiper.defaults);\n  extend(params, Swiper.extendedDefaults);\n  params._emitClasses = true;\n  params.init = false;\n\n  const rest: KeyValueType = {};\n  const allowedParams = paramsList.map((key) => key.replace(/_/, ''));\n  Object.keys(obj).forEach((key: string) => {\n    const _key = key.replace(/^_/, '');\n    if (allowedParams.indexOf(_key) >= 0) {\n      if (isObject(obj[key])) {\n        params[_key] = {};\n        passedParams[_key] = {};\n        extend(params[_key], obj[key]);\n        extend(passedParams[_key], obj[key]);\n      } else {\n        params[_key] = obj[key];\n        passedParams[_key] = obj[key];\n      }\n    }\n    // else if (key.search(/on[A-Z]/) === 0 && typeof obj[key] === 'function') {\n    //   events[`${_key[2].toLowerCase()}${key.substr(3)}`] = obj[key];\n    // }\n    else {\n      rest[_key] = obj[key];\n    }\n  });\n  ['navigation', 'pagination', 'scrollbar'].forEach((key) => {\n    if (params[key] === true) params[key] = {};\n    if (params[key] === false) delete params[key];\n  });\n\n  return { params, passedParams, rest };\n}\n", "import { Directive, Input, TemplateRef } from '@angular/core';\nimport { coerceBooleanProperty } from './utils/utils';\n@Directive({\n  selector: 'ng-template[swiperSlide]',\n})\nexport class SwiperSlideDirective {\n  @Input() virtualIndex: number;\n  @Input() class: string = '';\n  @Input()\n  set ngClass(val: string) {\n    this.class = [this.class || '', val].join(' ');\n  }\n  @Input('data-swiper-autoplay') autoplayDelay: string | null = null;\n  @Input()\n  set zoom(val: boolean) {\n    this._zoom = coerceBooleanProperty(val);\n  }\n  get zoom() {\n    return this._zoom;\n  }\n  private _zoom: boolean;\n  slideIndex: number;\n  get classNames() {\n    return this._classNames;\n  }\n  set classNames(val) {\n    if (this._classNames === val) {\n      return;\n    }\n    this._classNames = val;\n    this.slideData = {\n      isActive: this._hasClass(['swiper-slide-active', 'swiper-slide-duplicate-active']),\n      isVisible: this._hasClass(['swiper-slide-visible']),\n      isDuplicate: this._hasClass(['swiper-slide-duplicate']),\n      isPrev: this._hasClass(['swiper-slide-prev', 'swiper-slide-duplicate-prev']),\n      isNext: this._hasClass(['swiper-slide-next', 'swiper-slide-duplicate-next']),\n    };\n  }\n\n  private _hasClass(classNames: string[]) {\n    return classNames.some((className) => this._classNames.indexOf(className) >= 0);\n  }\n  slideData = {\n    isActive: false,\n    isPrev: false,\n    isNext: false,\n    isVisible: false,\n    isDuplicate: false,\n  };\n\n  private _classNames: string;\n  constructor(public template: TemplateRef<any>) {}\n}\n", "import {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChildren,\n  ElementRef,\n  EventEmitter,\n  HostBinding,\n  Inject,\n  Input,\n  NgZone,\n  OnInit,\n  Output,\n  PLATFORM_ID,\n  QueryList,\n  SimpleChanges,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\n// @ts-ignore\nimport Swiper from 'swiper';\nimport { Observable, of, Subject } from 'rxjs';\nimport { getParams } from './utils/get-params';\nimport { SwiperSlideDirective } from './swiper-slide.directive';\nimport { EventsParams } from './swiper-events';\nimport {\n  extend,\n  isObject,\n  setProperty,\n  ignoreNgOnChanges,\n  coerceBooleanProperty,\n  isShowEl,\n  isEnabled,\n} from './utils/utils';\nimport {\n  SwiperOptions,\n  SwiperEvents,\n  NavigationOptions,\n  PaginationOptions,\n  ScrollbarOptions,\n  VirtualOptions,\n} from 'swiper/types';\nimport { isPlatformBrowser } from '@angular/common';\n@Component({\n  selector: 'swiper, [swiper]',\n  templateUrl: './swiper.component.html',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  styles: [\n    `\n      swiper {\n        display: block;\n      }\n    `,\n  ],\n})\nexport class SwiperComponent implements OnInit {\n  @Input() enabled: SwiperOptions['enabled'];\n  @Input() on: SwiperOptions['on'];\n  @Input() direction: SwiperOptions['direction'];\n  @Input() touchEventsTarget: SwiperOptions['touchEventsTarget'];\n  @Input() initialSlide: SwiperOptions['initialSlide'];\n  @Input() speed: SwiperOptions['speed'];\n  @Input() cssMode: SwiperOptions['cssMode'];\n  @Input() updateOnWindowResize: SwiperOptions['updateOnWindowResize'];\n  @Input() resizeObserver: SwiperOptions['resizeObserver'];\n  @Input() nested: SwiperOptions['nested'];\n  @Input() focusableElements: SwiperOptions['focusableElements'];\n  @Input() width: SwiperOptions['width'];\n  @Input() height: SwiperOptions['height'];\n  @Input() preventInteractionOnTransition: SwiperOptions['preventInteractionOnTransition'];\n  @Input() userAgent: SwiperOptions['userAgent'];\n  @Input() url: SwiperOptions['url'];\n  @Input() edgeSwipeDetection: boolean | string;\n  @Input() edgeSwipeThreshold: number;\n  @Input() freeMode: SwiperOptions['freeMode'];\n  @Input() autoHeight: SwiperOptions['autoHeight'];\n  @Input() setWrapperSize: SwiperOptions['setWrapperSize'];\n  @Input() virtualTranslate: SwiperOptions['virtualTranslate'];\n  @Input() effect: SwiperOptions['effect'];\n  @Input() breakpoints: SwiperOptions['breakpoints'];\n  @Input() spaceBetween: SwiperOptions['spaceBetween'];\n  @Input() slidesPerView: SwiperOptions['slidesPerView'];\n  @Input() maxBackfaceHiddenSlides: SwiperOptions['maxBackfaceHiddenSlides'];\n  @Input() grid: SwiperOptions['grid'];\n  @Input() slidesPerGroup: SwiperOptions['slidesPerGroup'];\n  @Input() slidesPerGroupSkip: SwiperOptions['slidesPerGroupSkip'];\n  @Input() centeredSlides: SwiperOptions['centeredSlides'];\n  @Input() centeredSlidesBounds: SwiperOptions['centeredSlidesBounds'];\n  @Input() slidesOffsetBefore: SwiperOptions['slidesOffsetBefore'];\n  @Input() slidesOffsetAfter: SwiperOptions['slidesOffsetAfter'];\n  @Input() normalizeSlideIndex: SwiperOptions['normalizeSlideIndex'];\n  @Input() centerInsufficientSlides: SwiperOptions['centerInsufficientSlides'];\n  @Input() watchOverflow: SwiperOptions['watchOverflow'];\n  @Input() roundLengths: SwiperOptions['roundLengths'];\n  @Input() touchRatio: SwiperOptions['touchRatio'];\n  @Input() touchAngle: SwiperOptions['touchAngle'];\n  @Input() simulateTouch: SwiperOptions['simulateTouch'];\n  @Input() shortSwipes: SwiperOptions['shortSwipes'];\n  @Input() longSwipes: SwiperOptions['longSwipes'];\n  @Input() longSwipesRatio: SwiperOptions['longSwipesRatio'];\n  @Input() longSwipesMs: SwiperOptions['longSwipesMs'];\n  @Input() followFinger: SwiperOptions['followFinger'];\n  @Input() allowTouchMove: SwiperOptions['allowTouchMove'];\n  @Input() threshold: SwiperOptions['threshold'];\n  @Input() touchMoveStopPropagation: SwiperOptions['touchMoveStopPropagation'];\n  @Input() touchStartPreventDefault: SwiperOptions['touchStartPreventDefault'];\n  @Input() touchStartForcePreventDefault: SwiperOptions['touchStartForcePreventDefault'];\n  @Input() touchReleaseOnEdges: SwiperOptions['touchReleaseOnEdges'];\n  @Input() uniqueNavElements: SwiperOptions['uniqueNavElements'];\n  @Input() resistance: SwiperOptions['resistance'];\n  @Input() resistanceRatio: SwiperOptions['resistanceRatio'];\n  @Input() watchSlidesProgress: SwiperOptions['watchSlidesProgress'];\n  @Input() grabCursor: SwiperOptions['grabCursor'];\n  @Input() preventClicks: SwiperOptions['preventClicks'];\n  @Input() preventClicksPropagation: SwiperOptions['preventClicksPropagation'];\n  @Input() slideToClickedSlide: SwiperOptions['slideToClickedSlide'];\n  @Input() preloadImages: SwiperOptions['preloadImages'];\n  @Input() updateOnImagesReady: SwiperOptions['updateOnImagesReady'];\n  @Input() loop: SwiperOptions['loop'];\n  @Input() loopAdditionalSlides: SwiperOptions['loopAdditionalSlides'];\n  @Input() loopedSlides: SwiperOptions['loopedSlides'];\n  @Input() loopedSlidesLimit: SwiperOptions['loopedSlidesLimit'];\n  @Input() loopFillGroupWithBlank: SwiperOptions['loopFillGroupWithBlank'];\n  @Input() loopPreventsSlide: SwiperOptions['loopPreventsSlide'];\n  @Input() rewind: SwiperOptions['rewind'];\n  @Input() allowSlidePrev: SwiperOptions['allowSlidePrev'];\n  @Input() allowSlideNext: SwiperOptions['allowSlideNext'];\n  @Input() swipeHandler: SwiperOptions['swipeHandler'];\n  @Input() noSwiping: SwiperOptions['noSwiping'];\n  @Input() noSwipingClass: SwiperOptions['noSwipingClass'];\n  @Input() noSwipingSelector: SwiperOptions['noSwipingSelector'];\n  @Input() passiveListeners: SwiperOptions['passiveListeners'];\n  @Input() containerModifierClass: SwiperOptions['containerModifierClass'];\n  @Input() slideClass: SwiperOptions['slideClass'] = 'swiper-slide';\n  @Input() slideBlankClass: SwiperOptions['slideBlankClass'];\n  @Input() slideActiveClass: SwiperOptions['slideActiveClass'];\n  @Input() slideDuplicateActiveClass: SwiperOptions['slideDuplicateActiveClass'];\n  @Input() slideVisibleClass: SwiperOptions['slideVisibleClass'];\n  @Input() slideDuplicateClass: SwiperOptions['slideDuplicateClass'];\n  @Input() slideNextClass: SwiperOptions['slideNextClass'];\n  @Input() slideDuplicateNextClass: SwiperOptions['slideDuplicateNextClass'];\n  @Input() slidePrevClass: SwiperOptions['slidePrevClass'];\n  @Input() slideDuplicatePrevClass: SwiperOptions['slideDuplicatePrevClass'];\n  @Input() wrapperClass: SwiperOptions['wrapperClass'] = 'swiper-wrapper';\n  @Input() runCallbacksOnInit: SwiperOptions['runCallbacksOnInit'];\n  @Input() observeParents: SwiperOptions['observeParents'];\n  @Input() observeSlideChildren: SwiperOptions['observeSlideChildren'];\n  @Input() a11y: SwiperOptions['a11y'];\n  @Input() autoplay: SwiperOptions['autoplay'];\n  @Input() controller: SwiperOptions['controller'];\n  @Input() coverflowEffect: SwiperOptions['coverflowEffect'];\n  @Input() cubeEffect: SwiperOptions['cubeEffect'];\n  @Input() fadeEffect: SwiperOptions['fadeEffect'];\n  @Input() flipEffect: SwiperOptions['flipEffect'];\n  @Input() creativeEffect: SwiperOptions['creativeEffect'];\n  @Input() cardsEffect: SwiperOptions['cardsEffect'];\n  @Input() hashNavigation: SwiperOptions['hashNavigation'];\n  @Input() history: SwiperOptions['history'];\n  @Input() keyboard: SwiperOptions['keyboard'];\n  @Input() lazy: SwiperOptions['lazy'];\n  @Input() mousewheel: SwiperOptions['mousewheel'];\n  @Input() parallax: SwiperOptions['parallax'];\n  @Input() thumbs: SwiperOptions['thumbs'];\n  @Input() zoom: SwiperOptions['zoom'];\n  @Input() slidesPerGroupAuto: SwiperOptions['slidesPerGroupAuto'];\n  @Input() class: string;\n  @Input() id: string;\n  @Input()\n  set navigation(val) {\n    const currentNext =\n      typeof this._navigation !== 'boolean' && this._navigation !== ''\n        ? this._navigation?.nextEl\n        : null;\n    const currentPrev =\n      typeof this._navigation !== 'boolean' && this._navigation !== ''\n        ? this._navigation?.prevEl\n        : null;\n    this._navigation = setProperty(val, {\n      nextEl: currentNext || null,\n      prevEl: currentPrev || null,\n    });\n    this.showNavigation = !(\n      coerceBooleanProperty(val) !== true ||\n      (this._navigation &&\n        typeof this._navigation !== 'boolean' &&\n        this._navigation.prevEl !== this._prevElRef?.nativeElement &&\n        (this._navigation.prevEl !== null || this._navigation.nextEl !== null) &&\n        (typeof this._navigation.nextEl === 'string' ||\n          typeof this._navigation.prevEl === 'string' ||\n          typeof this._navigation.nextEl === 'object' ||\n          typeof this._navigation.prevEl === 'object'))\n    );\n  }\n  get navigation() {\n    return this._navigation;\n  }\n  private _navigation: NavigationOptions | boolean | '';\n  showNavigation: boolean = true;\n\n  @Input()\n  set pagination(val) {\n    const current =\n      typeof this._pagination !== 'boolean' && this._pagination !== ''\n        ? this._pagination?.el\n        : null;\n    this._pagination = setProperty(val, {\n      el: current || null,\n    });\n    this.showPagination = isShowEl(val, this._pagination, this._paginationElRef);\n  }\n  get pagination() {\n    return this._pagination;\n  }\n  private _pagination: PaginationOptions | boolean | '';\n  showPagination: boolean = true;\n\n  @Input()\n  set scrollbar(val) {\n    const current =\n      typeof this._scrollbar !== 'boolean' && this._scrollbar !== '' ? this._scrollbar?.el : null;\n    this._scrollbar = setProperty(val, {\n      el: current || null,\n    });\n    this.showScrollbar = isShowEl(val, this._scrollbar, this._scrollbarElRef);\n  }\n  get scrollbar() {\n    return this._scrollbar;\n  }\n  private _scrollbar: ScrollbarOptions | boolean | '';\n  showScrollbar: boolean = true;\n\n  @Input()\n  set virtual(val) {\n    this._virtual = setProperty(val);\n  }\n  get virtual() {\n    return this._virtual;\n  }\n  private _virtual: VirtualOptions | boolean | '';\n\n  @Input()\n  set config(val: SwiperOptions) {\n    this.updateSwiper(val);\n    const { params } = getParams(val);\n    Object.assign(this, params);\n  }\n  @Output('_beforeBreakpoint') s__beforeBreakpoint = new EventEmitter<\n    EventsParams['_beforeBreakpoint']\n  >();\n\n  @Output('_containerClasses') s__containerClasses = new EventEmitter<\n    EventsParams['_containerClasses']\n  >();\n\n  @Output('_slideClass') s__slideClass = new EventEmitter<EventsParams['_slideClass']>();\n\n  @Output('_swiper') s__swiper = new EventEmitter<EventsParams['_swiper']>();\n\n  @Output('activeIndexChange') s_activeIndexChange = new EventEmitter<\n    EventsParams['activeIndexChange']\n  >();\n\n  @Output('afterInit') s_afterInit = new EventEmitter<EventsParams['afterInit']>();\n\n  @Output('autoplay') s_autoplay = new EventEmitter<EventsParams['autoplay']>();\n\n  @Output('autoplayStart') s_autoplayStart = new EventEmitter<EventsParams['autoplayStart']>();\n\n  @Output('autoplayStop') s_autoplayStop = new EventEmitter<EventsParams['autoplayStop']>();\n\n  @Output('autoplayPause') s_autoplayPause = new EventEmitter<EventsParams['autoplayPause']>();\n\n  @Output('autoplayResume') s_autoplayResume = new EventEmitter<EventsParams['autoplayResume']>();\n\n  @Output('beforeDestroy') s_beforeDestroy = new EventEmitter<EventsParams['beforeDestroy']>();\n\n  @Output('beforeInit') s_beforeInit = new EventEmitter<EventsParams['beforeInit']>();\n\n  @Output('beforeLoopFix') s_beforeLoopFix = new EventEmitter<EventsParams['beforeLoopFix']>();\n\n  @Output('beforeResize') s_beforeResize = new EventEmitter<EventsParams['beforeResize']>();\n\n  @Output('beforeSlideChangeStart') s_beforeSlideChangeStart = new EventEmitter<\n    EventsParams['beforeSlideChangeStart']\n  >();\n\n  @Output('beforeTransitionStart') s_beforeTransitionStart = new EventEmitter<\n    EventsParams['beforeTransitionStart']\n  >();\n\n  @Output('breakpoint') s_breakpoint = new EventEmitter<EventsParams['breakpoint']>();\n\n  @Output('changeDirection') s_changeDirection = new EventEmitter<\n    EventsParams['changeDirection']\n  >();\n\n  @Output('click') s_click = new EventEmitter<EventsParams['click']>();\n\n  @Output('doubleTap') s_doubleTap = new EventEmitter<EventsParams['doubleTap']>();\n\n  @Output('doubleClick') s_doubleClick = new EventEmitter<EventsParams['doubleClick']>();\n\n  @Output('destroy') s_destroy = new EventEmitter<EventsParams['destroy']>();\n\n  @Output('fromEdge') s_fromEdge = new EventEmitter<EventsParams['fromEdge']>();\n\n  @Output('hashChange') s_hashChange = new EventEmitter<EventsParams['hashChange']>();\n\n  @Output('hashSet') s_hashSet = new EventEmitter<EventsParams['hashSet']>();\n\n  @Output('imagesReady') s_imagesReady = new EventEmitter<EventsParams['imagesReady']>();\n\n  @Output('init') s_init = new EventEmitter<EventsParams['init']>();\n\n  @Output('keyPress') s_keyPress = new EventEmitter<EventsParams['keyPress']>();\n\n  @Output('lazyImageLoad') s_lazyImageLoad = new EventEmitter<EventsParams['lazyImageLoad']>();\n\n  @Output('lazyImageReady') s_lazyImageReady = new EventEmitter<EventsParams['lazyImageReady']>();\n\n  @Output('loopFix') s_loopFix = new EventEmitter<EventsParams['loopFix']>();\n\n  @Output('momentumBounce') s_momentumBounce = new EventEmitter<EventsParams['momentumBounce']>();\n\n  @Output('navigationHide') s_navigationHide = new EventEmitter<EventsParams['navigationHide']>();\n\n  @Output('navigationShow') s_navigationShow = new EventEmitter<EventsParams['navigationShow']>();\n\n  @Output('navigationPrev') s_navigationPrev = new EventEmitter<EventsParams['navigationPrev']>();\n\n  @Output('navigationNext') s_navigationNext = new EventEmitter<EventsParams['navigationNext']>();\n\n  @Output('observerUpdate') s_observerUpdate = new EventEmitter<EventsParams['observerUpdate']>();\n\n  @Output('orientationchange') s_orientationchange = new EventEmitter<\n    EventsParams['orientationchange']\n  >();\n\n  @Output('paginationHide') s_paginationHide = new EventEmitter<EventsParams['paginationHide']>();\n\n  @Output('paginationRender') s_paginationRender = new EventEmitter<\n    EventsParams['paginationRender']\n  >();\n\n  @Output('paginationShow') s_paginationShow = new EventEmitter<EventsParams['paginationShow']>();\n\n  @Output('paginationUpdate') s_paginationUpdate = new EventEmitter<\n    EventsParams['paginationUpdate']\n  >();\n\n  @Output('progress') s_progress = new EventEmitter<EventsParams['progress']>();\n\n  @Output('reachBeginning') s_reachBeginning = new EventEmitter<EventsParams['reachBeginning']>();\n\n  @Output('reachEnd') s_reachEnd = new EventEmitter<EventsParams['reachEnd']>();\n\n  @Output('realIndexChange') s_realIndexChange = new EventEmitter<\n    EventsParams['realIndexChange']\n  >();\n\n  @Output('resize') s_resize = new EventEmitter<EventsParams['resize']>();\n\n  @Output('scroll') s_scroll = new EventEmitter<EventsParams['scroll']>();\n\n  @Output('scrollbarDragEnd') s_scrollbarDragEnd = new EventEmitter<\n    EventsParams['scrollbarDragEnd']\n  >();\n\n  @Output('scrollbarDragMove') s_scrollbarDragMove = new EventEmitter<\n    EventsParams['scrollbarDragMove']\n  >();\n\n  @Output('scrollbarDragStart') s_scrollbarDragStart = new EventEmitter<\n    EventsParams['scrollbarDragStart']\n  >();\n\n  @Output('setTransition') s_setTransition = new EventEmitter<EventsParams['setTransition']>();\n\n  @Output('setTranslate') s_setTranslate = new EventEmitter<EventsParams['setTranslate']>();\n\n  @Output('slideChange') s_slideChange = new EventEmitter<EventsParams['slideChange']>();\n\n  @Output('slideChangeTransitionEnd') s_slideChangeTransitionEnd = new EventEmitter<\n    EventsParams['slideChangeTransitionEnd']\n  >();\n\n  @Output('slideChangeTransitionStart') s_slideChangeTransitionStart = new EventEmitter<\n    EventsParams['slideChangeTransitionStart']\n  >();\n\n  @Output('slideNextTransitionEnd') s_slideNextTransitionEnd = new EventEmitter<\n    EventsParams['slideNextTransitionEnd']\n  >();\n\n  @Output('slideNextTransitionStart') s_slideNextTransitionStart = new EventEmitter<\n    EventsParams['slideNextTransitionStart']\n  >();\n\n  @Output('slidePrevTransitionEnd') s_slidePrevTransitionEnd = new EventEmitter<\n    EventsParams['slidePrevTransitionEnd']\n  >();\n\n  @Output('slidePrevTransitionStart') s_slidePrevTransitionStart = new EventEmitter<\n    EventsParams['slidePrevTransitionStart']\n  >();\n\n  @Output('slideResetTransitionStart') s_slideResetTransitionStart = new EventEmitter<\n    EventsParams['slideResetTransitionStart']\n  >();\n\n  @Output('slideResetTransitionEnd') s_slideResetTransitionEnd = new EventEmitter<\n    EventsParams['slideResetTransitionEnd']\n  >();\n\n  @Output('sliderMove') s_sliderMove = new EventEmitter<EventsParams['sliderMove']>();\n\n  @Output('sliderFirstMove') s_sliderFirstMove = new EventEmitter<\n    EventsParams['sliderFirstMove']\n  >();\n\n  @Output('slidesLengthChange') s_slidesLengthChange = new EventEmitter<\n    EventsParams['slidesLengthChange']\n  >();\n\n  @Output('slidesGridLengthChange') s_slidesGridLengthChange = new EventEmitter<\n    EventsParams['slidesGridLengthChange']\n  >();\n\n  @Output('snapGridLengthChange') s_snapGridLengthChange = new EventEmitter<\n    EventsParams['snapGridLengthChange']\n  >();\n\n  @Output('snapIndexChange') s_snapIndexChange = new EventEmitter<\n    EventsParams['snapIndexChange']\n  >();\n\n  @Output('tap') s_tap = new EventEmitter<EventsParams['tap']>();\n\n  @Output('toEdge') s_toEdge = new EventEmitter<EventsParams['toEdge']>();\n\n  @Output('touchEnd') s_touchEnd = new EventEmitter<EventsParams['touchEnd']>();\n\n  @Output('touchMove') s_touchMove = new EventEmitter<EventsParams['touchMove']>();\n\n  @Output('touchMoveOpposite') s_touchMoveOpposite = new EventEmitter<\n    EventsParams['touchMoveOpposite']\n  >();\n\n  @Output('touchStart') s_touchStart = new EventEmitter<EventsParams['touchStart']>();\n\n  @Output('transitionEnd') s_transitionEnd = new EventEmitter<EventsParams['transitionEnd']>();\n\n  @Output('transitionStart') s_transitionStart = new EventEmitter<\n    EventsParams['transitionStart']\n  >();\n\n  @Output('update') s_update = new EventEmitter<EventsParams['update']>();\n\n  @Output('zoomChange') s_zoomChange = new EventEmitter<EventsParams['zoomChange']>();\n\n  @Output('swiper') s_swiper = new EventEmitter<any>();\n\n  @Output('lock') s_lock = new EventEmitter<EventsParams['lock']>();\n\n  @Output('unlock') s_unlock = new EventEmitter<EventsParams['unlock']>();\n\n  @ViewChild('prevElRef', { static: false })\n  set prevElRef(el: ElementRef) {\n    this._prevElRef = el;\n    this._setElement(el, this.navigation, 'navigation', 'prevEl');\n  }\n  _prevElRef: ElementRef;\n  @ViewChild('nextElRef', { static: false })\n  set nextElRef(el: ElementRef) {\n    this._nextElRef = el;\n    this._setElement(el, this.navigation, 'navigation', 'nextEl');\n  }\n  _nextElRef: ElementRef;\n  @ViewChild('scrollbarElRef', { static: false })\n  set scrollbarElRef(el: ElementRef) {\n    this._scrollbarElRef = el;\n    this._setElement(el, this.scrollbar, 'scrollbar');\n  }\n  _scrollbarElRef: ElementRef;\n  @ViewChild('paginationElRef', { static: false })\n  set paginationElRef(el: ElementRef) {\n    this._paginationElRef = el;\n    this._setElement(el, this.pagination, 'pagination');\n  }\n  _paginationElRef: ElementRef;\n  @ContentChildren(SwiperSlideDirective, { descendants: false, emitDistinctChangesOnly: true })\n  slidesEl: QueryList<SwiperSlideDirective>;\n  private slides: SwiperSlideDirective[];\n\n  prependSlides: Observable<SwiperSlideDirective[]>;\n  appendSlides: Observable<SwiperSlideDirective[]>;\n\n  swiperRef: Swiper;\n  readonly _activeSlides = new Subject<SwiperSlideDirective[]>();\n\n  get activeSlides() {\n    if (this.virtual) {\n      return this._activeSlides;\n    }\n    return of(this.slides);\n  }\n\n  get zoomContainerClass() {\n    return this.zoom && typeof this.zoom !== 'boolean'\n      ? this.zoom.containerClass\n      : 'swiper-zoom-container';\n  }\n\n  @HostBinding('class') containerClasses: string = 'swiper';\n  constructor(\n    private _ngZone: NgZone,\n    private elementRef: ElementRef,\n    private _changeDetectorRef: ChangeDetectorRef,\n    @Inject(PLATFORM_ID) private _platformId: Object,\n  ) {}\n\n  private _setElement(el: ElementRef, ref: any, update: string, key = 'el') {\n    if (!ref || !el) return;\n    if (el.nativeElement) {\n      if (ref[key] === el.nativeElement) {\n        return;\n      }\n      ref[key] = el.nativeElement;\n    }\n    const updateObj: { [key: string]: boolean } = {};\n    updateObj[update] = true;\n    this.updateInitSwiper(updateObj);\n  }\n  ngOnInit(): void {\n    const { params } = getParams(this);\n    Object.assign(this, params);\n  }\n  ngAfterViewInit() {\n    this.childrenSlidesInit();\n    this.initSwiper();\n    this._changeDetectorRef.detectChanges();\n    setTimeout(() => {\n      this.s_swiper.emit(this.swiperRef);\n    });\n  }\n\n  private childrenSlidesInit() {\n    this.slidesChanges(this.slidesEl);\n    this.slidesEl.changes.subscribe(this.slidesChanges);\n  }\n\n  private slidesChanges = (val: QueryList<SwiperSlideDirective>) => {\n    this.slides = val.map((slide: SwiperSlideDirective, index: number) => {\n      slide.slideIndex = index;\n      slide.classNames = this.slideClass || '';\n      return slide;\n    });\n    if (this.loop && !this.loopedSlides) {\n      this.calcLoopedSlides();\n    }\n    if (!this.virtual) {\n      if (this.loopedSlides) {\n        this.prependSlides = of(this.slides.slice(this.slides.length - this.loopedSlides));\n        this.appendSlides = of(this.slides.slice(0, this.loopedSlides));\n      }\n    } else if (this.swiperRef && this.swiperRef.virtual) {\n      this._ngZone.runOutsideAngular(() => {\n        this.swiperRef.virtual.slides = this.slides;\n        this.swiperRef.virtual.update(true);\n      });\n    }\n    this._changeDetectorRef.detectChanges();\n  };\n\n  get isSwiperActive() {\n    return this.swiperRef && !this.swiperRef.destroyed;\n  }\n\n  initSwiper() {\n    const { params: swiperParams, passedParams } = getParams(this);\n    Object.assign(this, swiperParams);\n    this._ngZone.runOutsideAngular(() => {\n      swiperParams.init = false;\n      if (!swiperParams.virtual) {\n        swiperParams.observer = true;\n      }\n\n      swiperParams.onAny = (eventName: keyof SwiperComponent, ...args: any[]) => {\n        const emitter = this[('s_' + eventName) as keyof SwiperComponent] as EventEmitter<any>;\n        if (emitter) {\n          emitter.emit([...args]);\n        }\n      };\n      const _slideClasses: SwiperEvents['_slideClasses'] = (_, updated) => {\n        updated.forEach(({ slideEl, classNames }, index) => {\n          const dataIndex = slideEl.getAttribute('data-swiper-slide-index');\n          const slideIndex = dataIndex ? parseInt(dataIndex) : index;\n          if (this.virtual) {\n            const virtualSlide = this.slides.find((item) => {\n              return item.virtualIndex && item.virtualIndex === slideIndex;\n            });\n            if (virtualSlide) {\n              virtualSlide.classNames = classNames;\n              return;\n            }\n          }\n\n          if (this.slides[slideIndex]) {\n            this.slides[slideIndex].classNames = classNames;\n          }\n        });\n        this._changeDetectorRef.detectChanges();\n      };\n      const _containerClasses: SwiperEvents['_containerClasses'] = (_, classes) => {\n        setTimeout(() => {\n          this.containerClasses = classes;\n        });\n      };\n      Object.assign(swiperParams.on, {\n        _containerClasses,\n        _slideClasses,\n      });\n      const swiperRef = new Swiper(swiperParams);\n      swiperRef.loopCreate = () => {};\n      swiperRef.loopDestroy = () => {};\n      if (swiperParams.loop) {\n        swiperRef.loopedSlides = this.loopedSlides;\n      }\n      const isVirtualEnabled = isEnabled(swiperRef.params.virtual);\n      if (swiperRef.virtual && isVirtualEnabled) {\n        swiperRef.virtual.slides = this.slides;\n        const extendWith = {\n          cache: false,\n          slides: this.slides,\n          renderExternal: this.updateVirtualSlides,\n          renderExternalUpdate: false,\n        };\n        extend(swiperRef.params.virtual, extendWith);\n        extend(swiperRef.originalParams.virtual, extendWith);\n      }\n\n      if (isPlatformBrowser(this._platformId)) {\n        this.swiperRef = swiperRef.init(this.elementRef.nativeElement);\n        const isVirtualEnabled = isEnabled(this.swiperRef.params.virtual);\n        if (this.swiperRef.virtual && isVirtualEnabled) {\n          this.swiperRef.virtual.update(true);\n        }\n        this._changeDetectorRef.detectChanges();\n      }\n    });\n  }\n\n  style: any = null;\n  currentVirtualData: any; // TODO: type virtualData;\n  private updateVirtualSlides = (virtualData: any) => {\n    // TODO: type virtualData\n    if (\n      !this.swiperRef ||\n      (this.currentVirtualData &&\n        this.currentVirtualData.from === virtualData.from &&\n        this.currentVirtualData.to === virtualData.to &&\n        this.currentVirtualData.offset === virtualData.offset)\n    ) {\n      return;\n    }\n    this.style = this.swiperRef.isHorizontal()\n      ? {\n          [this.swiperRef.rtlTranslate ? 'right' : 'left']: `${virtualData.offset}px`,\n        }\n      : {\n          top: `${virtualData.offset}px`,\n        };\n    this.currentVirtualData = virtualData;\n    this._activeSlides.next(virtualData.slides);\n    this._ngZone.run(() => {\n      this._changeDetectorRef.detectChanges();\n    });\n    this._ngZone.runOutsideAngular(() => {\n      this.swiperRef.updateSlides();\n      this.swiperRef.updateProgress();\n      this.swiperRef.updateSlidesClasses();\n      if (isEnabled(this.swiperRef.params.lazy)) {\n        this.swiperRef.lazy.load();\n      }\n      this.swiperRef.virtual.update(true);\n    });\n    return;\n  };\n\n  ngOnChanges(changedParams: SimpleChanges) {\n    this.updateSwiper(changedParams);\n    this._changeDetectorRef.detectChanges();\n  }\n\n  updateInitSwiper(changedParams: any) {\n    if (!(changedParams && this.swiperRef && !this.swiperRef.destroyed)) {\n      return;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      const {\n        params: currentParams,\n        pagination,\n        navigation,\n        scrollbar,\n        virtual,\n        thumbs,\n      } = this.swiperRef;\n\n      if (changedParams.pagination) {\n        if (\n          this.pagination &&\n          typeof this.pagination !== 'boolean' &&\n          this.pagination.el &&\n          pagination &&\n          !pagination.el\n        ) {\n          this.updateParameter('pagination', this.pagination);\n          pagination.init();\n          pagination.render();\n          pagination.update();\n        } else {\n          pagination.destroy();\n          pagination.el = null;\n        }\n      }\n\n      if (changedParams.scrollbar) {\n        if (\n          this.scrollbar &&\n          typeof this.scrollbar !== 'boolean' &&\n          this.scrollbar.el &&\n          scrollbar &&\n          !scrollbar.el\n        ) {\n          this.updateParameter('scrollbar', this.scrollbar);\n          scrollbar.init();\n          scrollbar.updateSize();\n          scrollbar.setTranslate();\n        } else {\n          scrollbar.destroy();\n          scrollbar.el = null;\n        }\n      }\n\n      if (changedParams.navigation) {\n        if (\n          this.navigation &&\n          typeof this.navigation !== 'boolean' &&\n          this.navigation.prevEl &&\n          this.navigation.nextEl &&\n          navigation &&\n          !navigation.prevEl &&\n          !navigation.nextEl\n        ) {\n          this.updateParameter('navigation', this.navigation);\n          navigation.init();\n          navigation.update();\n        } else if (navigation.prevEl && navigation.nextEl) {\n          navigation.destroy();\n          navigation.nextEl = null;\n          navigation.prevEl = null;\n        }\n      }\n      if (changedParams.thumbs && this.thumbs && this.thumbs.swiper) {\n        this.updateParameter('thumbs', this.thumbs);\n        const initialized = thumbs.init();\n        if (initialized) thumbs.update(true);\n      }\n\n      if (changedParams.controller && this.controller && this.controller.control) {\n        this.swiperRef.controller.control = this.controller.control;\n      }\n\n      this.swiperRef.update();\n    });\n  }\n\n  updateSwiper(changedParams: SimpleChanges | any) {\n    this._ngZone.runOutsideAngular(() => {\n      if (changedParams.config) {\n        return;\n      }\n      if (!(changedParams && this.swiperRef && !this.swiperRef.destroyed)) {\n        return;\n      }\n      for (const key in changedParams) {\n        if (ignoreNgOnChanges.indexOf(key) >= 0) {\n          continue;\n        }\n        const newValue = changedParams[key]?.currentValue ?? changedParams[key];\n        this.updateParameter(key, newValue);\n      }\n\n      if (changedParams.allowSlideNext) {\n        this.swiperRef.allowSlideNext = this.allowSlideNext;\n      }\n      if (changedParams.allowSlidePrev) {\n        this.swiperRef.allowSlidePrev = this.allowSlidePrev;\n      }\n      if (changedParams.direction) {\n        this.swiperRef.changeDirection(this.direction, false);\n      }\n      if (changedParams.breakpoints) {\n        if (this.loop && !this.loopedSlides) {\n          this.calcLoopedSlides();\n        }\n        this.swiperRef.currentBreakpoint = null;\n        this.swiperRef.setBreakpoint();\n      }\n\n      if (changedParams.thumbs || changedParams.controller) {\n        this.updateInitSwiper(changedParams);\n      }\n      this.swiperRef.update();\n    });\n  }\n\n  calcLoopedSlides() {\n    if (!this.loop) {\n      return false;\n    }\n    let slidesPerViewParams = this.slidesPerView;\n    if (this.breakpoints) {\n      const breakpoint = Swiper.prototype.getBreakpoint(this.breakpoints);\n      const breakpointOnlyParams =\n        breakpoint in this.breakpoints ? this.breakpoints[breakpoint] : undefined;\n      if (breakpointOnlyParams && breakpointOnlyParams.slidesPerView) {\n        slidesPerViewParams = breakpointOnlyParams.slidesPerView;\n      }\n    }\n    if (slidesPerViewParams === 'auto') {\n      this.loopedSlides = this.slides.length;\n      return this.slides.length;\n    }\n    let loopedSlides = this.loopedSlides || slidesPerViewParams;\n    if (!loopedSlides) {\n      // ?\n      return false;\n    }\n\n    if (this.loopAdditionalSlides) {\n      loopedSlides += this.loopAdditionalSlides;\n    }\n    if (loopedSlides > this.slides.length) {\n      loopedSlides = this.slides.length;\n    }\n    this.loopedSlides = loopedSlides;\n    return true;\n  }\n\n  updateParameter(key: string, value: any) {\n    if (!(this.swiperRef && !this.swiperRef.destroyed)) {\n      return;\n    }\n    const _key = key.replace(/^_/, '') as keyof SwiperOptions;\n    const isCurrentParamObj = isObject(this.swiperRef.params[_key]);\n\n    if (_key === 'enabled') {\n      if (value === true) {\n        this.swiperRef.enable();\n      } else if (value === false) {\n        this.swiperRef.disable();\n      }\n      return;\n    }\n    if (isCurrentParamObj && isObject(value)) {\n      extend(this.swiperRef.params[_key], value);\n    } else {\n      (this.swiperRef.params[_key] as any) = value;\n    }\n  }\n\n  ngOnDestroy() {\n    this._ngZone.runOutsideAngular(() => {\n      this.swiperRef?.destroy(true, false);\n    });\n  }\n}\n", "<ng-content select=\"[slot=container-start]\"></ng-content>\n<ng-container *ngIf=\"navigation && showNavigation\">\n  <div class=\"swiper-button-prev\" #prevElRef></div>\n  <div class=\"swiper-button-next\" #nextElRef></div>\n</ng-container>\n<div *ngIf=\"scrollbar && showScrollbar\" class=\"swiper-scrollbar\" #scrollbarElRef></div>\n<div *ngIf=\"pagination && showPagination\" class=\"swiper-pagination\" #paginationElRef></div>\n<div [ngClass]=\"wrapperClass\" [attr.id]=\"id\">\n  <ng-content select=\"[slot=wrapper-start]\"></ng-content>\n  <ng-template\n    *ngTemplateOutlet=\"\n      slidesTemplate;\n      context: {\n        loopSlides: prependSlides,\n        key: 'prepend'\n      }\n    \"\n  ></ng-template>\n  <ng-template\n    *ngTemplateOutlet=\"\n      slidesTemplate;\n      context: {\n        loopSlides: activeSlides,\n        key: ''\n      }\n    \"\n  ></ng-template>\n  <ng-template\n    *ngTemplateOutlet=\"\n      slidesTemplate;\n      context: {\n        loopSlides: appendSlides,\n        key: 'append'\n      }\n    \"\n  ></ng-template>\n  <ng-content select=\"[slot=wrapper-end]\"></ng-content>\n</div>\n<ng-content select=\"[slot=container-end]\"></ng-content>\n\n<ng-template #slidesTemplate let-loopSlides=\"loopSlides\" let-slideKey=\"key\">\n  <div\n    *ngFor=\"let slide of loopSlides | async\"\n    [ngClass]=\"\n      (slide.class ? slide.class + ' ' : '') +\n      slideClass +\n      (slideKey !== '' ? ' ' + slideDuplicateClass : '')\n    \"\n    [attr.data-swiper-slide-index]=\"slide.virtualIndex ? slide.virtualIndex : slide.slideIndex\"\n    [attr.data-swiper-autoplay]=\"slide.autoplayDelay\"\n    [style]=\"style\"\n    [ngSwitch]=\"slide.zoom\"\n  >\n    <div *ngSwitchCase=\"true\" [ngClass]=\"zoomContainerClass\">\n      <ng-template\n        [ngTemplateOutlet]=\"slide.template\"\n        [ngTemplateOutletContext]=\"{\n          $implicit: slide.slideData\n        }\"\n      ></ng-template>\n    </div>\n    <ng-container *ngSwitchDefault>\n      <ng-template\n        [ngTemplateOutlet]=\"slide.template\"\n        [ngTemplateOutletContext]=\"{\n          $implicit: slide.slideData\n        }\"\n      ></ng-template>\n    </ng-container>\n  </div>\n</ng-template>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { SwiperComponent } from './swiper.component';\nimport { SwiperSlideDirective } from './swiper-slide.directive';\n@NgModule({\n  declarations: [SwiperComponent, SwiperSlideDirective],\n  exports: [SwiperComponent, SwiperSlideDirective],\n  imports: [CommonModule],\n})\nexport class SwiperModule {}\n", "/*\n * Public API Surface of angular\n */\nexport * from './swiper-events';\nexport * from './swiper.module';\nexport * from './swiper.component';\nexport * from './swiper-slide.directive';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './swiper-angular';\n"], "names": [], "mappings": ";;;;;;;AAAA;AACO,MAAM,UAAU,GAAG;IACxB,MAAM;IACN,SAAS;IACT,YAAY;IACZ,mBAAmB;IACnB,cAAc;IACd,QAAQ;IACR,SAAS;IACT,sBAAsB;IACtB,gBAAgB;IAChB,QAAQ;IACR,mBAAmB;IACnB,QAAQ;IACR,SAAS;IACT,gCAAgC;IAChC,WAAW;IACX,KAAK;IACL,qBAAqB;IACrB,qBAAqB;IACrB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;IACT,aAAa;IACb,eAAe;IACf,gBAAgB;IAChB,yBAAyB;IACzB,OAAO;IACP,iBAAiB;IACjB,qBAAqB;IACrB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,qBAAqB;IACrB,oBAAoB;IACpB,qBAAqB;IACrB,2BAA2B;IAC3B,gBAAgB;IAChB,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,eAAe;IACf,cAAc;IACd,aAAa;IACb,iBAAiB;IACjB,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,YAAY;IACZ,0BAA0B;IAC1B,0BAA0B;IAC1B,+BAA+B;IAC/B,qBAAqB;IACrB,mBAAmB;IACnB,aAAa;IACb,kBAAkB;IAClB,sBAAsB;IACtB,aAAa;IACb,eAAe;IACf,0BAA0B;IAC1B,sBAAsB;IACtB,gBAAgB;IAChB,qBAAqB;IACrB,OAAO;IACP,uBAAuB;IACvB,eAAe;IACf,oBAAoB;IACpB,yBAAyB;IACzB,mBAAmB;IACnB,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,eAAe;IACf,YAAY;IACZ,gBAAgB;IAChB,mBAAmB;IACnB,kBAAkB;IAClB,wBAAwB;IACxB,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,2BAA2B;IAC3B,mBAAmB;IACnB,qBAAqB;IACrB,gBAAgB;IAChB,yBAAyB;IACzB,gBAAgB;IAChB,yBAAyB;IACzB,cAAc;IACd,oBAAoB;IACpB,UAAU;IACV,gBAAgB;IAChB,sBAAsB;;IAGtB,MAAM;IACN,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,gBAAgB;IAChB,aAAa;IACb,gBAAgB;IAChB,SAAS;IACT,UAAU;IACV,MAAM;IACN,YAAY;IACZ,aAAa;IACb,aAAa;IACb,UAAU;IACV,YAAY;IACZ,SAAS;IACT,SAAS;IACT,MAAM;IACN,IAAI;CACL;;ACvHK,SAAU,QAAQ,CAAC,CAAM,EAAA;AAC7B,IAAA,QACE,OAAO,CAAC,KAAK,QAAQ;AACrB,QAAA,CAAC,KAAK,IAAI;AACV,QAAA,CAAC,CAAC,WAAW;QACb,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,EAC3D;AACJ,CAAC;AAEK,SAAU,SAAS,CAAC,GAAoC,EAAA;AAC5D,IAAA,OAAO,OAAO,GAAG,KAAK,WAAW,IAAI,OAAO,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC;AACxF,CAAC;SAEe,QAAQ,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAO,EAAA;AAClD,IAAA,QACE,CAAC,qBAAqB,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;AACtD,QAAA,EACE,OAAO,GAAG,KAAK,SAAS;AACxB,YAAA,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,aAAa;AAC5B,aAAC,OAAO,GAAG,CAAC,EAAE,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,CAC3D,EACD;AACJ,CAAC;AAEe,SAAA,MAAM,CAAC,MAAW,EAAE,GAAQ,EAAA;IAC1C,MAAM,QAAQ,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;AAC3D,IAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AACb,SAAA,MAAM,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,SAAA,OAAO,CAAC,CAAC,GAAG,KAAI;AACf,QAAA,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,EAAE;YACtC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO;AACR,SAAA;QACD,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC5B,OAAO;AACR,SAAA;AACD,QAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACnF,YAAA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU;gBAAE,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;;gBAC3C,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC,SAAA;AAAM,aAAA;YACL,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,SAAA;AACH,KAAC,CAAC,CAAC;AACP,CAAC;AAEK,SAAU,qBAAqB,CAAC,KAAU,EAAA;IAC9C,OAAO,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAA,CAAE,KAAK,OAAO,CAAC;AACjD,CAAC;AAEM,MAAM,iBAAiB,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;SAEtE,WAAW,CAAC,GAAQ,EAAE,GAAG,GAAG,EAAE,EAAA;AAC5C,IAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;AACjB,QAAA,OAAO,GAAG,CAAC;AACZ,KAAA;AAED,IAAA,IAAI,qBAAqB,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;AACvC,QAAA,OAAO,GAAG,CAAC;AACZ,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf;;AC7DA;AAKO,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3D,SAAA,SAAS,CAAC,GAAA,GAAW,EAAE,EAAA;AACrC,IAAA,MAAM,MAAM,GAAQ;AAClB,QAAA,EAAE,EAAE,EAAE;KACP,CAAC;;IAEF,MAAM,YAAY,GAAiB,EAAE,CAAC;AACtC,IAAA,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AAChC,IAAA,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACxC,IAAA,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;AAC3B,IAAA,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC;IAEpB,MAAM,IAAI,GAAiB,EAAE,CAAC;IAC9B,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IACpE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,GAAW,KAAI;QACvC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACnC,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACpC,YAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AACtB,gBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AAClB,gBAAA,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACxB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC/B,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,aAAA;AAAM,iBAAA;gBACL,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;gBACxB,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/B,aAAA;AACF,SAAA;;;;AAII,aAAA;YACH,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,SAAA;AACH,KAAC,CAAC,CAAC;AACH,IAAA,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AACxD,QAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI;AAAE,YAAA,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC3C,QAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK;AAAE,YAAA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AAChD,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;AACxC;;MCxCa,oBAAoB,CAAA;AA8C/B,IAAA,WAAA,CAAmB,QAA0B,EAAA;QAA1B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAkB;QA5CpC,IAAK,CAAA,KAAA,GAAW,EAAE,CAAC;QAKG,IAAa,CAAA,aAAA,GAAkB,IAAI,CAAC;AA8BnE,QAAA,IAAA,CAAA,SAAS,GAAG;AACV,YAAA,QAAQ,EAAE,KAAK;AACf,YAAA,MAAM,EAAE,KAAK;AACb,YAAA,MAAM,EAAE,KAAK;AACb,YAAA,SAAS,EAAE,KAAK;AAChB,YAAA,WAAW,EAAE,KAAK;SACnB,CAAC;KAG+C;IA3CjD,IACI,OAAO,CAAC,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAChD;IAED,IACI,IAAI,CAAC,GAAY,EAAA;AACnB,QAAA,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;KACzC;AACD,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAGD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,GAAG,EAAA;AAChB,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,GAAG,EAAE;YAC5B,OAAO;AACR,SAAA;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG;YACf,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,qBAAqB,EAAE,+BAA+B,CAAC,CAAC;YAClF,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,CAAC;YACnD,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,wBAAwB,CAAC,CAAC;YACvD,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;YAC5E,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;SAC7E,CAAC;KACH;AAEO,IAAA,SAAS,CAAC,UAAoB,EAAA;QACpC,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;KACjF;;kHApCU,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;sGAApB,oBAAoB,EAAA,QAAA,EAAA,0BAAA,EAAA,MAAA,EAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,aAAA,EAAA,CAAA,sBAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA;4FAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAHhC,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0BAA0B;AACrC,iBAAA,CAAA;kGAEU,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAEF,OAAO,EAAA,CAAA;sBADV,KAAK;gBAIyB,aAAa,EAAA,CAAA;sBAA3C,KAAK;uBAAC,sBAAsB,CAAA;gBAEzB,IAAI,EAAA,CAAA;sBADP,KAAK;;;MC2CK,eAAe,CAAA;AA2c1B,IAAA,WAAA,CACU,OAAe,EACf,UAAsB,EACtB,kBAAqC,EAChB,WAAmB,EAAA;QAHxC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;QACtB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAChB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QAjYzC,IAAU,CAAA,UAAA,GAAgC,cAAc,CAAC;QAUzD,IAAY,CAAA,YAAA,GAAkC,gBAAgB,CAAC;QAsDxE,IAAc,CAAA,cAAA,GAAY,IAAI,CAAC;QAiB/B,IAAc,CAAA,cAAA,GAAY,IAAI,CAAC;QAe/B,IAAa,CAAA,aAAA,GAAY,IAAI,CAAC;AAiBD,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAEhE,CAAC;AAEyB,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAEhE,CAAC;AAEmB,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,YAAY,EAA+B,CAAC;AAEpE,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAA2B,CAAC;AAE9C,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAEhE,CAAC;AAEiB,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAA6B,CAAC;AAE7D,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,YAAY,EAA4B,CAAC;AAErD,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAErE,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAgC,CAAC;AAEjE,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAEnE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEvE,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAEvE,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAA8B,CAAC;AAE3D,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAErE,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAgC,CAAC;AAExD,QAAA,IAAA,CAAA,wBAAwB,GAAG,IAAI,YAAY,EAE1E,CAAC;AAE6B,QAAA,IAAA,CAAA,uBAAuB,GAAG,IAAI,YAAY,EAExE,CAAC;AAEkB,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAA8B,CAAC;AAEzD,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAE5D,CAAC;AAEa,QAAA,IAAA,CAAA,OAAO,GAAG,IAAI,YAAY,EAAyB,CAAC;AAEhD,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAA6B,CAAC;AAE1D,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,YAAY,EAA+B,CAAC;AAEpE,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAA2B,CAAC;AAEvD,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,YAAY,EAA4B,CAAC;AAExD,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAA8B,CAAC;AAEjE,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAA2B,CAAC;AAEpD,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,YAAY,EAA+B,CAAC;AAEvE,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAwB,CAAC;AAE9C,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,YAAY,EAA4B,CAAC;AAErD,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAEnE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAE7E,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,YAAY,EAA2B,CAAC;AAEjD,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEtE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEtE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEtE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEtE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEtE,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEnE,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAEhE,CAAC;AAEsB,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEpE,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,YAAY,EAE9D,CAAC;AAEsB,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAEpE,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,YAAY,EAE9D,CAAC;AAEgB,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,YAAY,EAA4B,CAAC;AAEpD,QAAA,IAAA,CAAA,gBAAgB,GAAG,IAAI,YAAY,EAAkC,CAAC;AAE5E,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,YAAY,EAA4B,CAAC;AAEnD,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAE5D,CAAC;AAEc,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAA0B,CAAC;AAEtD,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAA0B,CAAC;AAE5C,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,YAAY,EAE9D,CAAC;AAEyB,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAEhE,CAAC;AAE0B,QAAA,IAAA,CAAA,oBAAoB,GAAG,IAAI,YAAY,EAElE,CAAC;AAEqB,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAErE,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,YAAY,EAAgC,CAAC;AAEnE,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,YAAY,EAA+B,CAAC;AAEnD,QAAA,IAAA,CAAA,0BAA0B,GAAG,IAAI,YAAY,EAE9E,CAAC;AAEkC,QAAA,IAAA,CAAA,4BAA4B,GAAG,IAAI,YAAY,EAElF,CAAC;AAE8B,QAAA,IAAA,CAAA,wBAAwB,GAAG,IAAI,YAAY,EAE1E,CAAC;AAEgC,QAAA,IAAA,CAAA,0BAA0B,GAAG,IAAI,YAAY,EAE9E,CAAC;AAE8B,QAAA,IAAA,CAAA,wBAAwB,GAAG,IAAI,YAAY,EAE1E,CAAC;AAEgC,QAAA,IAAA,CAAA,0BAA0B,GAAG,IAAI,YAAY,EAE9E,CAAC;AAEiC,QAAA,IAAA,CAAA,2BAA2B,GAAG,IAAI,YAAY,EAEhF,CAAC;AAE+B,QAAA,IAAA,CAAA,yBAAyB,GAAG,IAAI,YAAY,EAE5E,CAAC;AAEkB,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAA8B,CAAC;AAEzD,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAE5D,CAAC;AAE0B,QAAA,IAAA,CAAA,oBAAoB,GAAG,IAAI,YAAY,EAElE,CAAC;AAE8B,QAAA,IAAA,CAAA,wBAAwB,GAAG,IAAI,YAAY,EAE1E,CAAC;AAE4B,QAAA,IAAA,CAAA,sBAAsB,GAAG,IAAI,YAAY,EAEtE,CAAC;AAEuB,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAE5D,CAAC;AAEW,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,YAAY,EAAuB,CAAC;AAE7C,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAA0B,CAAC;AAEpD,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,YAAY,EAA4B,CAAC;AAEzD,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,YAAY,EAA6B,CAAC;AAEpD,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,YAAY,EAEhE,CAAC;AAEkB,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAA8B,CAAC;AAE3D,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,YAAY,EAAiC,CAAC;AAElE,QAAA,IAAA,CAAA,iBAAiB,GAAG,IAAI,YAAY,EAE5D,CAAC;AAEc,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAA0B,CAAC;AAElD,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,YAAY,EAA8B,CAAC;AAElE,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAAO,CAAC;AAErC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,YAAY,EAAwB,CAAC;AAEhD,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,YAAY,EAA0B,CAAC;AAkC/D,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAA0B,CAAC;QAezC,IAAgB,CAAA,gBAAA,GAAW,QAAQ,CAAC;AAsClD,QAAA,IAAA,CAAA,aAAa,GAAG,CAAC,GAAoC,KAAI;AAC/D,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAA2B,EAAE,KAAa,KAAI;AACnE,gBAAA,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;gBACzB,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;AACzC,gBAAA,OAAO,KAAK,CAAC;AACf,aAAC,CAAC,CAAC;YACH,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACzB,aAAA;AACD,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,IAAI,CAAC,YAAY,EAAE;oBACrB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACnF,oBAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AACjE,iBAAA;AACF,aAAA;iBAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AACnD,gBAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;oBAClC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC5C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,iBAAC,CAAC,CAAC;AACJ,aAAA;AACD,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;AAC1C,SAAC,CAAC;QAgFF,IAAK,CAAA,KAAA,GAAQ,IAAI,CAAC;AAEV,QAAA,IAAA,CAAA,mBAAmB,GAAG,CAAC,WAAgB,KAAI;;YAEjD,IACE,CAAC,IAAI,CAAC,SAAS;iBACd,IAAI,CAAC,kBAAkB;AACtB,oBAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI;AACjD,oBAAA,IAAI,CAAC,kBAAkB,CAAC,EAAE,KAAK,WAAW,CAAC,EAAE;oBAC7C,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,EACxD;gBACA,OAAO;AACR,aAAA;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AACxC,kBAAE;AACE,oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,OAAO,GAAG,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAI,EAAA,CAAA;AAC5E,iBAAA;AACH,kBAAE;AACE,oBAAA,GAAG,EAAE,CAAA,EAAG,WAAW,CAAC,MAAM,CAAI,EAAA,CAAA;iBAC/B,CAAC;AACN,YAAA,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC5C,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACpB,gBAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;AAC1C,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;AAC9B,gBAAA,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;AAChC,gBAAA,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;gBACrC,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AACzC,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAC5B,iBAAA;gBACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,aAAC,CAAC,CAAC;YACH,OAAO;AACT,SAAC,CAAC;KAxKE;IAhWJ,IACI,UAAU,CAAC,GAAG,EAAA;AAChB,QAAA,MAAM,WAAW,GACf,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE;AAC9D,cAAE,IAAI,CAAC,WAAW,EAAE,MAAM;cACxB,IAAI,CAAC;AACX,QAAA,MAAM,WAAW,GACf,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE;AAC9D,cAAE,IAAI,CAAC,WAAW,EAAE,MAAM;cACxB,IAAI,CAAC;AACX,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;YAClC,MAAM,EAAE,WAAW,IAAI,IAAI;YAC3B,MAAM,EAAE,WAAW,IAAI,IAAI;AAC5B,SAAA,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,GAAG,EACpB,qBAAqB,CAAC,GAAG,CAAC,KAAK,IAAI;aAClC,IAAI,CAAC,WAAW;AACf,gBAAA,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS;gBACrC,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,UAAU,EAAE,aAAa;AAC1D,iBAAC,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC;AACtE,iBAAC,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ;AAC1C,oBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ;AAC3C,oBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ;oBAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAClD,CAAC;KACH;AACD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IAID,IACI,UAAU,CAAC,GAAG,EAAA;AAChB,QAAA,MAAM,OAAO,GACX,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,EAAE;AAC9D,cAAE,IAAI,CAAC,WAAW,EAAE,EAAE;cACpB,IAAI,CAAC;AACX,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;YAClC,EAAE,EAAE,OAAO,IAAI,IAAI;AACpB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;KAC9E;AACD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IAID,IACI,SAAS,CAAC,GAAG,EAAA;QACf,MAAM,OAAO,GACX,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,KAAK,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,GAAG,IAAI,CAAC;AAC9F,QAAA,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YACjC,EAAE,EAAE,OAAO,IAAI,IAAI;AACpB,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;KAC3E;AACD,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAID,IACI,OAAO,CAAC,GAAG,EAAA;AACb,QAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;KAClC;AACD,IAAA,IAAI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IAGD,IACI,MAAM,CAAC,GAAkB,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACvB,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AAClC,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC7B;IA6ND,IACI,SAAS,CAAC,EAAc,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;KAC/D;IAED,IACI,SAAS,CAAC,EAAc,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;KAC/D;IAED,IACI,cAAc,CAAC,EAAc,EAAA;AAC/B,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;KACnD;IAED,IACI,eAAe,CAAC,EAAc,EAAA;AAChC,QAAA,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;KACrD;AAYD,IAAA,IAAI,YAAY,GAAA;QACd,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,aAAa,CAAC;AAC3B,SAAA;AACD,QAAA,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACxB;AAED,IAAA,IAAI,kBAAkB,GAAA;QACpB,OAAO,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS;AAChD,cAAE,IAAI,CAAC,IAAI,CAAC,cAAc;cACxB,uBAAuB,CAAC;KAC7B;IAUO,WAAW,CAAC,EAAc,EAAE,GAAQ,EAAE,MAAc,EAAE,GAAG,GAAG,IAAI,EAAA;AACtE,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE;YAAE,OAAO;QACxB,IAAI,EAAE,CAAC,aAAa,EAAE;YACpB,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,aAAa,EAAE;gBACjC,OAAO;AACR,aAAA;AACD,YAAA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC;AAC7B,SAAA;QACD,MAAM,SAAS,GAA+B,EAAE,CAAC;AACjD,QAAA,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;KAClC;IACD,QAAQ,GAAA;QACN,MAAM,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACnC,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC7B;IACD,eAAe,GAAA;QACb,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;QACxC,UAAU,CAAC,MAAK;YACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,SAAC,CAAC,CAAC;KACJ;IAEO,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KACrD;AAyBD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;KACpD;IAED,UAAU,GAAA;AACR,QAAA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAC/D,QAAA,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC;AAC1B,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACzB,gBAAA,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC9B,aAAA;YAED,YAAY,CAAC,KAAK,GAAG,CAAC,SAAgC,EAAE,GAAG,IAAW,KAAI;gBACxE,MAAM,OAAO,GAAG,IAAI,EAAE,IAAI,GAAG,SAAS,EAAgD,CAAC;AACvF,gBAAA,IAAI,OAAO,EAAE;oBACX,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACzB,iBAAA;AACH,aAAC,CAAC;AACF,YAAA,MAAM,aAAa,GAAkC,CAAC,CAAC,EAAE,OAAO,KAAI;AAClE,gBAAA,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,KAAK,KAAI;oBACjD,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;AAClE,oBAAA,MAAM,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;oBAC3D,IAAI,IAAI,CAAC,OAAO,EAAE;wBAChB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,KAAI;4BAC7C,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,KAAK,UAAU,CAAC;AAC/D,yBAAC,CAAC,CAAC;AACH,wBAAA,IAAI,YAAY,EAAE;AAChB,4BAAA,YAAY,CAAC,UAAU,GAAG,UAAU,CAAC;4BACrC,OAAO;AACR,yBAAA;AACF,qBAAA;AAED,oBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;wBAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC;AACjD,qBAAA;AACH,iBAAC,CAAC,CAAC;AACH,gBAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;AAC1C,aAAC,CAAC;AACF,YAAA,MAAM,iBAAiB,GAAsC,CAAC,CAAC,EAAE,OAAO,KAAI;gBAC1E,UAAU,CAAC,MAAK;AACd,oBAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;AAClC,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC;AACF,YAAA,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;gBAC7B,iBAAiB;gBACjB,aAAa;AACd,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC;AAC3C,YAAA,SAAS,CAAC,UAAU,GAAG,MAAK,GAAG,CAAC;AAChC,YAAA,SAAS,CAAC,WAAW,GAAG,MAAK,GAAG,CAAC;YACjC,IAAI,YAAY,CAAC,IAAI,EAAE;AACrB,gBAAA,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC5C,aAAA;YACD,MAAM,gBAAgB,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC7D,YAAA,IAAI,SAAS,CAAC,OAAO,IAAI,gBAAgB,EAAE;gBACzC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACvC,gBAAA,MAAM,UAAU,GAAG;AACjB,oBAAA,KAAK,EAAE,KAAK;oBACZ,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,cAAc,EAAE,IAAI,CAAC,mBAAmB;AACxC,oBAAA,oBAAoB,EAAE,KAAK;iBAC5B,CAAC;gBACF,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACtD,aAAA;AAED,YAAA,IAAI,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AACvC,gBAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AAC/D,gBAAA,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAClE,gBAAA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,gBAAgB,EAAE;oBAC9C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,iBAAA;AACD,gBAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;AACzC,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAuCD,IAAA,WAAW,CAAC,aAA4B,EAAA;AACtC,QAAA,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AACjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;KACzC;AAED,IAAA,gBAAgB,CAAC,aAAkB,EAAA;AACjC,QAAA,IAAI,EAAE,aAAa,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;YACnE,OAAO;AACR,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,EACJ,MAAM,EAAE,aAAa,EACrB,UAAU,EACV,UAAU,EACV,SAAS,EACT,OAAO,EACP,MAAM,GACP,GAAG,IAAI,CAAC,SAAS,CAAC;YAEnB,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC5B,IACE,IAAI,CAAC,UAAU;AACf,oBAAA,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS;oBACpC,IAAI,CAAC,UAAU,CAAC,EAAE;oBAClB,UAAU;oBACV,CAAC,UAAU,CAAC,EAAE,EACd;oBACA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBACpD,UAAU,CAAC,IAAI,EAAE,CAAC;oBAClB,UAAU,CAAC,MAAM,EAAE,CAAC;oBACpB,UAAU,CAAC,MAAM,EAAE,CAAC;AACrB,iBAAA;AAAM,qBAAA;oBACL,UAAU,CAAC,OAAO,EAAE,CAAC;AACrB,oBAAA,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC;AACtB,iBAAA;AACF,aAAA;YAED,IAAI,aAAa,CAAC,SAAS,EAAE;gBAC3B,IACE,IAAI,CAAC,SAAS;AACd,oBAAA,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS;oBACnC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACjB,SAAS;oBACT,CAAC,SAAS,CAAC,EAAE,EACb;oBACA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBAClD,SAAS,CAAC,IAAI,EAAE,CAAC;oBACjB,SAAS,CAAC,UAAU,EAAE,CAAC;oBACvB,SAAS,CAAC,YAAY,EAAE,CAAC;AAC1B,iBAAA;AAAM,qBAAA;oBACL,SAAS,CAAC,OAAO,EAAE,CAAC;AACpB,oBAAA,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC;AACrB,iBAAA;AACF,aAAA;YAED,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC5B,IACE,IAAI,CAAC,UAAU;AACf,oBAAA,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS;oBACpC,IAAI,CAAC,UAAU,CAAC,MAAM;oBACtB,IAAI,CAAC,UAAU,CAAC,MAAM;oBACtB,UAAU;oBACV,CAAC,UAAU,CAAC,MAAM;oBAClB,CAAC,UAAU,CAAC,MAAM,EAClB;oBACA,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBACpD,UAAU,CAAC,IAAI,EAAE,CAAC;oBAClB,UAAU,CAAC,MAAM,EAAE,CAAC;AACrB,iBAAA;AAAM,qBAAA,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,EAAE;oBACjD,UAAU,CAAC,OAAO,EAAE,CAAC;AACrB,oBAAA,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;AACzB,oBAAA,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1B,iBAAA;AACF,aAAA;AACD,YAAA,IAAI,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC7D,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,gBAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAClC,gBAAA,IAAI,WAAW;AAAE,oBAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtC,aAAA;AAED,YAAA,IAAI,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAC1E,gBAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AAC7D,aAAA;AAED,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AAC1B,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,YAAY,CAAC,aAAkC,EAAA;AAC7C,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,aAAa,CAAC,MAAM,EAAE;gBACxB,OAAO;AACR,aAAA;AACD,YAAA,IAAI,EAAE,aAAa,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;gBACnE,OAAO;AACR,aAAA;AACD,YAAA,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;gBAC/B,IAAI,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBACvC,SAAS;AACV,iBAAA;AACD,gBAAA,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,EAAE,YAAY,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;AACxE,gBAAA,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACrC,aAAA;YAED,IAAI,aAAa,CAAC,cAAc,EAAE;gBAChC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AACrD,aAAA;YACD,IAAI,aAAa,CAAC,cAAc,EAAE;gBAChC,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;AACrD,aAAA;YACD,IAAI,aAAa,CAAC,SAAS,EAAE;gBAC3B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACvD,aAAA;YACD,IAAI,aAAa,CAAC,WAAW,EAAE;gBAC7B,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACzB,iBAAA;AACD,gBAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACxC,gBAAA,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;AAChC,aAAA;AAED,YAAA,IAAI,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,UAAU,EAAE;AACpD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACtC,aAAA;AACD,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AAC1B,SAAC,CAAC,CAAC;KACJ;IAED,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACd,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,IAAI,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,oBAAoB,GACxB,UAAU,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;AAC5E,YAAA,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,aAAa,EAAE;AAC9D,gBAAA,mBAAmB,GAAG,oBAAoB,CAAC,aAAa,CAAC;AAC1D,aAAA;AACF,SAAA;QACD,IAAI,mBAAmB,KAAK,MAAM,EAAE;YAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACvC,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AAC3B,SAAA;AACD,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,mBAAmB,CAAC;QAC5D,IAAI,CAAC,YAAY,EAAE;;AAEjB,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,YAAA,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC;AAC3C,SAAA;AACD,QAAA,IAAI,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACrC,YAAA,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;AACnC,SAAA;AACD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;IAED,eAAe,CAAC,GAAW,EAAE,KAAU,EAAA;AACrC,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;YAClD,OAAO;AACR,SAAA;QACD,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAwB,CAAC;AAC1D,QAAA,MAAM,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QAEhE,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,gBAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;AACzB,aAAA;iBAAM,IAAI,KAAK,KAAK,KAAK,EAAE;AAC1B,gBAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;AAC1B,aAAA;YACD,OAAO;AACR,SAAA;AACD,QAAA,IAAI,iBAAiB,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACxC,YAAA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;AAC5C,SAAA;AAAM,aAAA;YACJ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAS,GAAG,KAAK,CAAC;AAC9C,SAAA;KACF;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC,SAAC,CAAC,CAAC;KACJ;;AAtzBU,eAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,mGA+chB,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;iGA/cV,eAAe,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAA,EAAA,WAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,OAAA,EAAA,OAAA,EAAA,SAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,EAAA,QAAA,EAAA,8BAAA,EAAA,gCAAA,EAAA,SAAA,EAAA,WAAA,EAAA,GAAA,EAAA,KAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,WAAA,EAAA,aAAA,EAAA,YAAA,EAAA,cAAA,EAAA,aAAA,EAAA,eAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,aAAA,EAAA,eAAA,EAAA,YAAA,EAAA,cAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,WAAA,EAAA,aAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,YAAA,EAAA,cAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,WAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,6BAAA,EAAA,+BAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,aAAA,EAAA,eAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,aAAA,EAAA,eAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,QAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAAA,WAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,sBAAA,EAAA,wBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,yBAAA,EAAA,2BAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,uBAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,cAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,MAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,WAAA,EAAA,aAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,OAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA,EAAA,UAAA,EAAA,YAAA,EAAA,QAAA,EAAA,UAAA,EAAA,MAAA,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,kBAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAA,EAAA,YAAA,EAAA,UAAA,EAAA,YAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,MAAA,EAAA,QAAA,EAAA,EAAA,OAAA,EAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,aAAA,EAAA,SAAA,EAAA,SAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,WAAA,EAAA,UAAA,EAAA,UAAA,EAAA,eAAA,EAAA,eAAA,EAAA,cAAA,EAAA,cAAA,EAAA,eAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,YAAA,EAAA,YAAA,EAAA,eAAA,EAAA,eAAA,EAAA,cAAA,EAAA,cAAA,EAAA,wBAAA,EAAA,wBAAA,EAAA,uBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,OAAA,EAAA,WAAA,EAAA,WAAA,EAAA,aAAA,EAAA,aAAA,EAAA,SAAA,EAAA,SAAA,EAAA,UAAA,EAAA,UAAA,EAAA,YAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,aAAA,EAAA,aAAA,EAAA,MAAA,EAAA,MAAA,EAAA,UAAA,EAAA,UAAA,EAAA,eAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,kBAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,cAAA,EAAA,cAAA,EAAA,aAAA,EAAA,aAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,4BAAA,EAAA,4BAAA,EAAA,wBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,wBAAA,EAAA,wBAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,2BAAA,EAAA,2BAAA,EAAA,yBAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,wBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,UAAA,EAAA,UAAA,EAAA,WAAA,EAAA,WAAA,EAAA,mBAAA,EAAA,mBAAA,EAAA,YAAA,EAAA,YAAA,EAAA,eAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,iBAAA,EAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,YAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,uBAAA,EAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,SAAA,EAmbT,oBAAoB,EAAA,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,WAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,WAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC3evC,srEAuEA,EAAA,MAAA,EAAA,CAAA,yBAAA,CAAA,EAAA,UAAA,EAAA,CAAA,EAAA,IAAA,EAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA,QAAA,EAAA,MAAA,EAAA,CAAA,MAAA,EAAA,UAAA,EAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,CAAA,OAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,CAAA,SAAA,EAAA,cAAA,EAAA,eAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,UAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,CAAA,YAAA,EAAA,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,CAAA,eAAA,EAAA,QAAA,EAAA,mBAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,OAAA,EAAA,EAAA,CAAA,SAAA,EAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA;4FDfa,eAAe,EAAA,UAAA,EAAA,CAAA;kBAb3B,SAAS;+BACE,kBAAkB,EAAA,eAAA,EAEX,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAC7B,MAAA,EAAA;AACN,wBAAA,CAAA;;;;AAIC,IAAA,CAAA;AACF,qBAAA,EAAA,QAAA,EAAA,srEAAA,EAAA,CAAA;wIAid2C,MAAM,EAAA,UAAA,EAAA,CAAA;0BAA/C,MAAM;2BAAC,WAAW,CAAA;4CA9cZ,OAAO,EAAA,CAAA;sBAAf,KAAK;gBACG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBACG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBACG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBACG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBACG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBACG,8BAA8B,EAAA,CAAA;sBAAtC,KAAK;gBACG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBACG,GAAG,EAAA,CAAA;sBAAX,KAAK;gBACG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBACG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBACG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBACG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBACG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBACG,uBAAuB,EAAA,CAAA;sBAA/B,KAAK;gBACG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBACG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBACG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBACG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBACG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBACG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBACG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBACG,6BAA6B,EAAA,CAAA;sBAArC,KAAK;gBACG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBACG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBACG,wBAAwB,EAAA,CAAA;sBAAhC,KAAK;gBACG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBACG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBACG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBACG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBACG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,sBAAsB,EAAA,CAAA;sBAA9B,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,SAAS,EAAA,CAAA;sBAAjB,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBACG,sBAAsB,EAAA,CAAA;sBAA9B,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBACG,gBAAgB,EAAA,CAAA;sBAAxB,KAAK;gBACG,yBAAyB,EAAA,CAAA;sBAAjC,KAAK;gBACG,iBAAiB,EAAA,CAAA;sBAAzB,KAAK;gBACG,mBAAmB,EAAA,CAAA;sBAA3B,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,uBAAuB,EAAA,CAAA;sBAA/B,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,uBAAuB,EAAA,CAAA;sBAA/B,KAAK;gBACG,YAAY,EAAA,CAAA;sBAApB,KAAK;gBACG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,oBAAoB,EAAA,CAAA;sBAA5B,KAAK;gBACG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBACG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,eAAe,EAAA,CAAA;sBAAvB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,WAAW,EAAA,CAAA;sBAAnB,KAAK;gBACG,cAAc,EAAA,CAAA;sBAAtB,KAAK;gBACG,OAAO,EAAA,CAAA;sBAAf,KAAK;gBACG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBACG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBACG,UAAU,EAAA,CAAA;sBAAlB,KAAK;gBACG,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBACG,MAAM,EAAA,CAAA;sBAAd,KAAK;gBACG,IAAI,EAAA,CAAA;sBAAZ,KAAK;gBACG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBACG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBACG,EAAE,EAAA,CAAA;sBAAV,KAAK;gBAEF,UAAU,EAAA,CAAA;sBADb,KAAK;gBAiCF,UAAU,EAAA,CAAA;sBADb,KAAK;gBAkBF,SAAS,EAAA,CAAA;sBADZ,KAAK;gBAgBF,OAAO,EAAA,CAAA;sBADV,KAAK;gBAUF,MAAM,EAAA,CAAA;sBADT,KAAK;gBAMuB,mBAAmB,EAAA,CAAA;sBAA/C,MAAM;uBAAC,mBAAmB,CAAA;gBAIE,mBAAmB,EAAA,CAAA;sBAA/C,MAAM;uBAAC,mBAAmB,CAAA;gBAIJ,aAAa,EAAA,CAAA;sBAAnC,MAAM;uBAAC,aAAa,CAAA;gBAEF,SAAS,EAAA,CAAA;sBAA3B,MAAM;uBAAC,SAAS,CAAA;gBAEY,mBAAmB,EAAA,CAAA;sBAA/C,MAAM;uBAAC,mBAAmB,CAAA;gBAIN,WAAW,EAAA,CAAA;sBAA/B,MAAM;uBAAC,WAAW,CAAA;gBAEC,UAAU,EAAA,CAAA;sBAA7B,MAAM;uBAAC,UAAU,CAAA;gBAEO,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAEC,cAAc,EAAA,CAAA;sBAArC,MAAM;uBAAC,cAAc,CAAA;gBAEG,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAEG,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEC,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAED,YAAY,EAAA,CAAA;sBAAjC,MAAM;uBAAC,YAAY,CAAA;gBAEK,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAEC,cAAc,EAAA,CAAA;sBAArC,MAAM;uBAAC,cAAc,CAAA;gBAEY,wBAAwB,EAAA,CAAA;sBAAzD,MAAM;uBAAC,wBAAwB,CAAA;gBAIC,uBAAuB,EAAA,CAAA;sBAAvD,MAAM;uBAAC,uBAAuB,CAAA;gBAIT,YAAY,EAAA,CAAA;sBAAjC,MAAM;uBAAC,YAAY,CAAA;gBAEO,iBAAiB,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB,CAAA;gBAIR,OAAO,EAAA,CAAA;sBAAvB,MAAM;uBAAC,OAAO,CAAA;gBAEM,WAAW,EAAA,CAAA;sBAA/B,MAAM;uBAAC,WAAW,CAAA;gBAEI,aAAa,EAAA,CAAA;sBAAnC,MAAM;uBAAC,aAAa,CAAA;gBAEF,SAAS,EAAA,CAAA;sBAA3B,MAAM;uBAAC,SAAS,CAAA;gBAEG,UAAU,EAAA,CAAA;sBAA7B,MAAM;uBAAC,UAAU,CAAA;gBAEI,YAAY,EAAA,CAAA;sBAAjC,MAAM;uBAAC,YAAY,CAAA;gBAED,SAAS,EAAA,CAAA;sBAA3B,MAAM;uBAAC,SAAS,CAAA;gBAEM,aAAa,EAAA,CAAA;sBAAnC,MAAM;uBAAC,aAAa,CAAA;gBAEL,MAAM,EAAA,CAAA;sBAArB,MAAM;uBAAC,MAAM,CAAA;gBAEM,UAAU,EAAA,CAAA;sBAA7B,MAAM;uBAAC,UAAU,CAAA;gBAEO,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAEG,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEL,SAAS,EAAA,CAAA;sBAA3B,MAAM;uBAAC,SAAS,CAAA;gBAES,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEE,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEK,mBAAmB,EAAA,CAAA;sBAA/C,MAAM;uBAAC,mBAAmB,CAAA;gBAID,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEI,kBAAkB,EAAA,CAAA;sBAA7C,MAAM;uBAAC,kBAAkB,CAAA;gBAIA,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEI,kBAAkB,EAAA,CAAA;sBAA7C,MAAM;uBAAC,kBAAkB,CAAA;gBAIN,UAAU,EAAA,CAAA;sBAA7B,MAAM;uBAAC,UAAU,CAAA;gBAEQ,gBAAgB,EAAA,CAAA;sBAAzC,MAAM;uBAAC,gBAAgB,CAAA;gBAEJ,UAAU,EAAA,CAAA;sBAA7B,MAAM;uBAAC,UAAU,CAAA;gBAES,iBAAiB,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB,CAAA;gBAIP,QAAQ,EAAA,CAAA;sBAAzB,MAAM;uBAAC,QAAQ,CAAA;gBAEE,QAAQ,EAAA,CAAA;sBAAzB,MAAM;uBAAC,QAAQ,CAAA;gBAEY,kBAAkB,EAAA,CAAA;sBAA7C,MAAM;uBAAC,kBAAkB,CAAA;gBAIG,mBAAmB,EAAA,CAAA;sBAA/C,MAAM;uBAAC,mBAAmB,CAAA;gBAIG,oBAAoB,EAAA,CAAA;sBAAjD,MAAM;uBAAC,oBAAoB,CAAA;gBAIH,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAEC,cAAc,EAAA,CAAA;sBAArC,MAAM;uBAAC,cAAc,CAAA;gBAEC,aAAa,EAAA,CAAA;sBAAnC,MAAM;uBAAC,aAAa,CAAA;gBAEe,0BAA0B,EAAA,CAAA;sBAA7D,MAAM;uBAAC,0BAA0B,CAAA;gBAII,4BAA4B,EAAA,CAAA;sBAAjE,MAAM;uBAAC,4BAA4B,CAAA;gBAIF,wBAAwB,EAAA,CAAA;sBAAzD,MAAM;uBAAC,wBAAwB,CAAA;gBAII,0BAA0B,EAAA,CAAA;sBAA7D,MAAM;uBAAC,0BAA0B,CAAA;gBAIA,wBAAwB,EAAA,CAAA;sBAAzD,MAAM;uBAAC,wBAAwB,CAAA;gBAII,0BAA0B,EAAA,CAAA;sBAA7D,MAAM;uBAAC,0BAA0B,CAAA;gBAIG,2BAA2B,EAAA,CAAA;sBAA/D,MAAM;uBAAC,2BAA2B,CAAA;gBAIA,yBAAyB,EAAA,CAAA;sBAA3D,MAAM;uBAAC,yBAAyB,CAAA;gBAIX,YAAY,EAAA,CAAA;sBAAjC,MAAM;uBAAC,YAAY,CAAA;gBAEO,iBAAiB,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB,CAAA;gBAIK,oBAAoB,EAAA,CAAA;sBAAjD,MAAM;uBAAC,oBAAoB,CAAA;gBAIM,wBAAwB,EAAA,CAAA;sBAAzD,MAAM;uBAAC,wBAAwB,CAAA;gBAIA,sBAAsB,EAAA,CAAA;sBAArD,MAAM;uBAAC,sBAAsB,CAAA;gBAIH,iBAAiB,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB,CAAA;gBAIV,KAAK,EAAA,CAAA;sBAAnB,MAAM;uBAAC,KAAK,CAAA;gBAEK,QAAQ,EAAA,CAAA;sBAAzB,MAAM;uBAAC,QAAQ,CAAA;gBAEI,UAAU,EAAA,CAAA;sBAA7B,MAAM;uBAAC,UAAU,CAAA;gBAEG,WAAW,EAAA,CAAA;sBAA/B,MAAM;uBAAC,WAAW,CAAA;gBAEU,mBAAmB,EAAA,CAAA;sBAA/C,MAAM;uBAAC,mBAAmB,CAAA;gBAIL,YAAY,EAAA,CAAA;sBAAjC,MAAM;uBAAC,YAAY,CAAA;gBAEK,eAAe,EAAA,CAAA;sBAAvC,MAAM;uBAAC,eAAe,CAAA;gBAEI,iBAAiB,EAAA,CAAA;sBAA3C,MAAM;uBAAC,iBAAiB,CAAA;gBAIP,QAAQ,EAAA,CAAA;sBAAzB,MAAM;uBAAC,QAAQ,CAAA;gBAEM,YAAY,EAAA,CAAA;sBAAjC,MAAM;uBAAC,YAAY,CAAA;gBAEF,QAAQ,EAAA,CAAA;sBAAzB,MAAM;uBAAC,QAAQ,CAAA;gBAEA,MAAM,EAAA,CAAA;sBAArB,MAAM;uBAAC,MAAM,CAAA;gBAEI,QAAQ,EAAA,CAAA;sBAAzB,MAAM;uBAAC,QAAQ,CAAA;gBAGZ,SAAS,EAAA,CAAA;sBADZ,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;gBAOrC,SAAS,EAAA,CAAA;sBADZ,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;gBAOrC,cAAc,EAAA,CAAA;sBADjB,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,gBAAgB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;gBAO1C,eAAe,EAAA,CAAA;sBADlB,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAA;gBAO/C,QAAQ,EAAA,CAAA;sBADP,eAAe;uBAAC,oBAAoB,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAA;gBAuBtE,gBAAgB,EAAA,CAAA;sBAArC,WAAW;uBAAC,OAAO,CAAA;;;MEzfT,YAAY,CAAA;;0GAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA;2GAAZ,YAAY,EAAA,YAAA,EAAA,CAJR,eAAe,EAAE,oBAAoB,aAE1C,YAAY,CAAA,EAAA,OAAA,EAAA,CADZ,eAAe,EAAE,oBAAoB,CAAA,EAAA,CAAA,CAAA;2GAGpC,YAAY,EAAA,OAAA,EAAA,CAFd,CAAC,YAAY,CAAC,CAAA,EAAA,CAAA,CAAA;4FAEZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBALxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,YAAY,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC;AACrD,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC;oBAChD,OAAO,EAAE,CAAC,YAAY,CAAC;AACxB,iBAAA,CAAA;;;ACRD;;AAEG;;ACFH;;AAEG;;;;"}