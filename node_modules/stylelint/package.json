{"name": "stylelint", "version": "14.16.1", "description": "A mighty, modern CSS linter.", "keywords": ["css-in-js", "css", "less", "lint", "linter", "markdown", "sass", "scss", "stylelint", "sugarss"], "homepage": "https://stylelint.io", "repository": "stylelint/stylelint", "funding": {"type": "opencollective", "url": "https://opencollective.com/stylelint"}, "license": "MIT", "author": "stylelint", "main": "lib/index.js", "types": "types/stylelint/index.d.ts", "bin": {"stylelint": "bin/stylelint.js"}, "files": ["bin/**/*.js", "lib/**/*.js", "!**/__tests__/**", "!lib/testUtils/**", "types/stylelint/index.d.ts"], "scripts": {"benchmark-rule": "node scripts/benchmark-rule.mjs", "format": "prettier . --write --cache", "jest": "jest", "lint": "npm-run-all --parallel --continue-on-error lint:*", "lint:formatting": "prettier . --check --cache", "lint:js": "eslint . --cache --max-warnings=0 --ext .js,.mjs", "lint:md": "remark . --quiet --frail", "lint:types": "tsc", "prepare": "husky install && patch-package", "release": "np --no-release-draft", "pretest": "npm run lint", "test": "jest --coverage", "version": "changeset version", "postversion": "git restore package.json", "watch": "jest --watch", "changelog-to-github-release": "remark --quiet --use ./scripts/remark-changelog-to-github-release.mjs CHANGELOG.md"}, "lint-staged": {"*.{js,mjs}": "eslint --cache --fix", "*.{js,json,md,mjs,ts,yml}": "prettier --write"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint"], "globals": {"__dirname": true, "module": true, "require": true, "testRule": true}, "root": true}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "jest": {"clearMocks": true, "collectCoverage": false, "collectCoverageFrom": ["lib/**/*.js", "!lib/**/{__tests__,testUtils}/**/*.js"], "coverageDirectory": "./.coverage/", "coverageReporters": ["lcov", "text-summary"], "coverageThreshold": {"global": {"branches": 75, "functions": 75, "lines": 75, "statements": 75}}, "moduleNameMapper": {"^stylelint$": "<rootDir>/lib/index.js", "stylelint/lib/utils/getOsEol": "<rootDir>/lib/utils/getOsEol.js"}, "preset": "jest-preset-stylelint", "roots": ["lib", "system-tests"], "testEnvironment": "node", "testRegex": ".*\\.test\\.js$|rules/.*/__tests__/.*\\.js$", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "dependencies": {"@csstools/selector-specificity": "^2.0.2", "balanced-match": "^2.0.0", "colord": "^2.9.3", "cosmiconfig": "^7.1.0", "css-functions-list": "^3.1.0", "debug": "^4.3.4", "fast-glob": "^3.2.12", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^6.0.1", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.2.0", "ignore": "^5.2.1", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.26.0", "mathml-tag-names": "^2.1.3", "meow": "^9.0.0", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.19", "postcss-media-query-parser": "^0.2.3", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.11", "postcss-value-parser": "^4.2.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "supports-hyperlinks": "^2.3.0", "svg-tags": "^1.0.0", "table": "^6.8.1", "v8-compile-cache": "^2.3.0", "write-file-atomic": "^4.0.2"}, "devDependencies": {"@changesets/cli": "^2.25.2", "@changesets/get-github-info": "^0.5.1", "@stylelint/prettier-config": "^2.0.0", "@stylelint/remark-preset": "^4.0.0", "@types/balanced-match": "^1.0.2", "@types/debug": "^4.1.7", "@types/file-entry-cache": "^5.0.2", "@types/global-modules": "^2.0.0", "@types/globjoin": "^0.1.0", "@types/imurmurhash": "^0.1.1", "@types/micromatch": "^4.0.2", "@types/normalize-path": "^3.0.0", "@types/postcss-less": "^4.0.2", "@types/postcss-safe-parser": "^5.0.1", "@types/style-search": "^0.1.3", "@types/svg-tags": "^1.0.0", "@types/write-file-atomic": "^4.0.0", "benchmark": "^2.1.4", "common-tags": "^1.8.2", "deepmerge": "^4.2.2", "eslint": "^8.28.0", "eslint-config-stylelint": "^17.1.0", "husky": "^8.0.2", "jest": "^28.1.3", "jest-preset-stylelint": "^5.0.4", "jest-watch-typeahead": "^2.2.0", "lint-staged": "^13.0.4", "node-fetch": "^3.3.0", "np": "^7.6.2", "npm-run-all": "^4.1.5", "patch-package": "^6.5.0", "postcss-html": "^1.5.0", "postcss-import": "^14.1.0", "postcss-less": "^6.0.0", "postcss-sass": "^0.5.0", "postcss-scss": "^4.0.6", "remark-cli": "^11.0.0", "sugarss": "^4.0.1", "typescript": "^4.9.3"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}