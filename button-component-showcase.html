<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIMA Button Component Library - Mobile-First Showcase</title>
    <link rel="stylesheet" href="assets/dist/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.0/css/all.min.css"/>
    <style>
        body {
            font-family: var(--font-family-primary);
            line-height: 1.6;
            margin: 0;
            padding: var(--spacing-6);
            background: var(--color-gray-50);
        }
        .showcase-section {
            background: var(--color-white);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-6);
            margin-bottom: var(--spacing-6);
            box-shadow: var(--shadow-sm);
        }
        .showcase-title {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--color-gray-900);
            margin-bottom: var(--spacing-4);
            border-bottom: 2px solid var(--color-primary-200);
            padding-bottom: var(--spacing-2);
        }
        .showcase-subtitle {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--color-gray-800);
            margin: var(--spacing-6) 0 var(--spacing-3) 0;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }
        .button-demo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-4);
            border: 1px solid var(--color-gray-200);
            border-radius: var(--border-radius-md);
            background: var(--color-white);
        }
        .button-label {
            font-size: var(--font-size-sm);
            color: var(--color-gray-600);
            text-align: center;
            font-family: monospace;
        }
        .code-block {
            background: var(--color-gray-900);
            color: var(--color-gray-100);
            padding: var(--spacing-4);
            border-radius: var(--border-radius-md);
            font-family: monospace;
            font-size: var(--font-size-sm);
            overflow-x: auto;
            margin: var(--spacing-4) 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="showcase-title">VIMA Button Component Library</h1>
        <p>Mobile-first, touch-optimized button system with BEM methodology. All buttons meet WCAG AA accessibility standards with minimum 44px touch targets.</p>

        <!-- Button Variants -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Button Variants</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <button class="btn btn--primary">Primary Button</button>
                    <span class="button-label">.btn .btn--primary</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--secondary">Secondary Button</button>
                    <span class="button-label">.btn .btn--secondary</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--tertiary">Tertiary Button</button>
                    <span class="button-label">.btn .btn--tertiary</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--success">Success Button</button>
                    <span class="button-label">.btn .btn--success</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--warning">Warning Button</button>
                    <span class="button-label">.btn .btn--warning</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--danger">Danger Button</button>
                    <span class="button-label">.btn .btn--danger</span>
                </div>
            </div>
        </div>

        <!-- Button Sizes -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Button Sizes</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <button class="btn btn--primary btn--sm">Small Button</button>
                    <span class="button-label">.btn .btn--primary .btn--sm</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary">Default Button</button>
                    <span class="button-label">.btn .btn--primary</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary btn--lg">Large Button</button>
                    <span class="button-label">.btn .btn--primary .btn--lg</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary btn--xl">Extra Large</button>
                    <span class="button-label">.btn .btn--primary .btn--xl</span>
                </div>
            </div>
        </div>

        <!-- Button Shapes -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Button Shapes</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <button class="btn btn--primary btn--rounded">Rounded Button</button>
                    <span class="button-label">.btn .btn--primary .btn--rounded</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary btn--square">
                        <i class="fas fa-heart btn__icon btn__icon--only"></i>
                    </button>
                    <span class="button-label">.btn .btn--primary .btn--square</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary btn--circle">
                        <i class="fas fa-plus btn__icon btn__icon--only"></i>
                    </button>
                    <span class="button-label">.btn .btn--primary .btn--circle</span>
                </div>
            </div>
        </div>

        <!-- Button with Icons -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Buttons with Icons</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <button class="btn btn--primary">
                        <i class="fas fa-download btn__icon btn__icon--left"></i>
                        Download
                    </button>
                    <span class="button-label">Icon Left</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--secondary">
                        Upload
                        <i class="fas fa-upload btn__icon btn__icon--right"></i>
                    </button>
                    <span class="button-label">Icon Right</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--success">
                        <i class="fas fa-check btn__icon btn__icon--left"></i>
                        Save Changes
                    </button>
                    <span class="button-label">Success with Icon</span>
                </div>
            </div>
        </div>

        <!-- Button States -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Button States</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <button class="btn btn--primary">Normal State</button>
                    <span class="button-label">Normal</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary" disabled>Disabled State</button>
                    <span class="button-label">Disabled</span>
                </div>
                <div class="button-demo">
                    <button class="btn btn--primary btn--loading">Loading State</button>
                    <span class="button-label">Loading</span>
                </div>
            </div>
        </div>

        <!-- Button Layouts -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Button Layouts</h2>
            
            <h3>Full Width (Mobile) / Auto (Desktop)</h3>
            <button class="btn btn--primary btn--full">Full Width Button</button>
            
            <h3>Always Block</h3>
            <button class="btn btn--secondary btn--block">Block Button</button>
            
            <h3>Fluid Responsive</h3>
            <button class="btn btn--success btn--fluid">Fluid Button</button>
        </div>

        <!-- Button Groups -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Button Groups</h2>
            
            <h3>Default Group (Stacked on Mobile)</h3>
            <div class="btn-group">
                <button class="btn btn--primary">Primary Action</button>
                <button class="btn btn--secondary">Secondary Action</button>
                <button class="btn btn--tertiary">Cancel</button>
            </div>
            
            <h3>Centered Group</h3>
            <div class="btn-group btn-group--center">
                <button class="btn btn--success">Save</button>
                <button class="btn btn--danger">Delete</button>
            </div>
            
            <h3>Connected Group</h3>
            <div class="btn-group btn-group--connected">
                <button class="btn btn--secondary">Previous</button>
                <button class="btn btn--secondary">Current</button>
                <button class="btn btn--secondary">Next</button>
            </div>
        </div>

        <!-- Legacy Support -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Legacy Button Support</h2>
            <div class="button-grid">
                <div class="button-demo">
                    <a href="#" class="round-button box-shadow">Legacy Round Button</a>
                    <span class="button-label">.round-button .box-shadow</span>
                </div>
                <div class="button-demo">
                    <a href="#" class="round-button green">Legacy Green Button</a>
                    <span class="button-label">.round-button .green</span>
                </div>
            </div>
        </div>

        <!-- Usage Examples -->
        <div class="showcase-section">
            <h2 class="showcase-subtitle">Usage Examples</h2>
            
            <h3>Basic Button</h3>
            <div class="code-block">
&lt;button class="btn btn--primary"&gt;Click Me&lt;/button&gt;
            </div>
            
            <h3>Button with Icon</h3>
            <div class="code-block">
&lt;button class="btn btn--secondary btn--lg"&gt;
  &lt;i class="fas fa-download btn__icon btn__icon--left"&gt;&lt;/i&gt;
  Download File
&lt;/button&gt;
            </div>
            
            <h3>Button Group</h3>
            <div class="code-block">
&lt;div class="btn-group btn-group--center"&gt;
  &lt;button class="btn btn--primary"&gt;Save&lt;/button&gt;
  &lt;button class="btn btn--secondary"&gt;Cancel&lt;/button&gt;
&lt;/div&gt;
            </div>
        </div>
    </div>

    <script>
        // Demo loading state toggle
        document.addEventListener('DOMContentLoaded', function() {
            const loadingBtn = document.querySelector('.btn--loading');
            if (loadingBtn) {
                setInterval(() => {
                    loadingBtn.classList.toggle('btn--loading');
                }, 3000);
            }
        });
    </script>
</body>
</html>
