document.addEventListener('DOMContentLoaded', function () {
    const languageSelector = document.getElementById('language-selector');
    const defaultLanguage = 'en';

    languageSelector.addEventListener('change', function () {
        const selectedLanguage = languageSelector.value;
        loadTranslations(selectedLanguage);
    });

    function loadTranslations(language) {
        fetch(`assets/i18n/${language}.json`)
            .then(response => response.json())
            .then(translations => {
                updateTranslations(translations);
            })
            .catch(error => console.error('Error loading translations:', error));
    }

    function updateTranslations(translations) {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const keys = element.getAttribute('data-i18n').split('.');
            let translation = translations;
            keys.forEach(key => {
                translation = translation[key];
            });
            if (translation) {
                element.textContent = translation;
            }
        });
    }

    // Initialize with default language
    loadTranslations(defaultLanguage);
}); 