#!/bin/bash

# =============================================================================
# VIMA Holding - Development Environment Setup Script
# =============================================================================
# This script sets up the development environment with hot reloading
# and performance monitoring for the VIMA Holding website redesign.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js 14+ first."
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please run this script from the project root."
        exit 1
    fi
    
    print_success "All requirements met!"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing/updating dependencies..."
    
    # Install npm dependencies
    npm install --legacy-peer-deps
    
    if [ $? -eq 0 ]; then
        print_success "Dependencies installed successfully!"
    else
        print_error "Failed to install dependencies."
        exit 1
    fi
}

# Function to create backup
create_backup() {
    print_status "Creating backup of current assets..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup compiled assets if they exist
    if [ -d "public/css" ]; then
        cp -r public/css "$BACKUP_DIR/"
        print_success "CSS backup created"
    fi
    
    if [ -d "public/js" ]; then
        cp -r public/js "$BACKUP_DIR/"
        print_success "JS backup created"
    fi
    
    print_success "Backup created in $BACKUP_DIR"
}

# Function to build assets
build_assets() {
    print_status "Building assets for development..."
    
    npm run dev
    
    if [ $? -eq 0 ]; then
        print_success "Assets built successfully!"
    else
        print_error "Failed to build assets."
        exit 1
    fi
}

# Function to start development server
start_dev_server() {
    print_status "Starting development server with hot reloading..."
    print_warning "This will start the development server. Press Ctrl+C to stop."
    print_status "Browser sync will be available at: http://localhost:3000"
    print_status "Webpack dev server will be available at: http://localhost:8080"
    
    # Start hot reloading
    npm run hot
}

# Function to show development commands
show_dev_commands() {
    echo ""
    print_status "Available development commands:"
    echo ""
    echo "  npm run dev          - Build assets for development"
    echo "  npm run watch        - Watch files and rebuild on changes"
    echo "  npm run hot          - Start hot reloading development server"
    echo "  npm run prod         - Build assets for production"
    echo "  npm run lint:css     - Lint and fix SCSS files"
    echo "  npm run lint:js      - Lint and fix JavaScript files"
    echo ""
    print_status "File structure:"
    echo ""
    echo "  assets/src/scss/     - New SCSS architecture"
    echo "  assets/src/js/       - New JavaScript architecture"
    echo "  assets/sass/         - Legacy SCSS (preserved)"
    echo "  assets/js/           - Legacy JavaScript (preserved)"
    echo ""
    print_status "Generated files:"
    echo ""
    echo "  public/css/style.css        - New architecture styles"
    echo "  public/css/style-legacy.css - Legacy styles"
    echo "  public/js/app.js            - Compiled JavaScript"
    echo ""
}

# Function to check server compatibility
check_server_compatibility() {
    print_status "Checking DigitalOcean server compatibility..."
    
    # Check available memory
    if command_exists free; then
        MEMORY=$(free -m | awk 'NR==2{printf "%.1f", $3*100/$2 }')
        print_status "Memory usage: ${MEMORY}%"
        
        if (( $(echo "$MEMORY > 80" | bc -l) )); then
            print_warning "High memory usage detected. Consider optimizing build process."
        fi
    fi
    
    # Check disk space
    if command_exists df; then
        DISK_USAGE=$(df -h . | awk 'NR==2{print $5}' | sed 's/%//')
        print_status "Disk usage: ${DISK_USAGE}%"
        
        if [ "$DISK_USAGE" -gt 80 ]; then
            print_warning "High disk usage detected. Consider cleaning up old files."
        fi
    fi
    
    print_success "Server compatibility check completed!"
}

# Main execution
main() {
    echo ""
    print_status "VIMA Holding - Development Environment Setup"
    print_status "============================================="
    echo ""
    
    # Parse command line arguments
    case "${1:-setup}" in
        "setup")
            check_requirements
            install_dependencies
            create_backup
            build_assets
            show_dev_commands
            ;;
        "dev")
            check_requirements
            build_assets
            ;;
        "hot")
            check_requirements
            start_dev_server
            ;;
        "check")
            check_requirements
            check_server_compatibility
            ;;
        "help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  setup    - Full setup (default)"
            echo "  dev      - Build development assets"
            echo "  hot      - Start hot reloading server"
            echo "  check    - Check system requirements and compatibility"
            echo "  help     - Show this help message"
            echo ""
            ;;
        *)
            print_error "Unknown command: $1"
            print_status "Run '$0 help' for available commands."
            exit 1
            ;;
    esac
    
    echo ""
    print_success "Development environment setup completed!"
    echo ""
}

# Run main function with all arguments
main "$@"
