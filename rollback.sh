#!/bin/bash

# =============================================================================
# VIMA Holding - Rollback Script
# =============================================================================
# This script provides safe rollback procedures for the VIMA Holding website
# redesign implementation. It can restore the system to previous states.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to confirm action
confirm_action() {
    local message="$1"
    echo ""
    print_warning "$message"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled."
        exit 0
    fi
}

# Function to create emergency backup
create_emergency_backup() {
    print_status "Creating emergency backup before rollback..."
    
    local backup_dir="emergency_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup current state
    if [ -f "package.json" ]; then
        cp package.json "$backup_dir/"
    fi
    
    if [ -f "webpack.mix.js" ]; then
        cp webpack.mix.js "$backup_dir/"
    fi
    
    if [ -d "assets/src" ]; then
        cp -r assets/src "$backup_dir/"
    fi
    
    if [ -d "public/css" ]; then
        cp -r public/css "$backup_dir/"
    fi
    
    if [ -d "public/js" ]; then
        cp -r public/js "$backup_dir/"
    fi
    
    print_success "Emergency backup created in $backup_dir"
}

# Function to rollback to Laravel Mix 2.x
rollback_laravel_mix() {
    print_status "Rolling back to Laravel Mix 2.x..."
    
    # Check if backup files exist
    if [ ! -f "package.json.backup" ]; then
        print_error "package.json.backup not found. Cannot rollback Laravel Mix."
        exit 1
    fi
    
    if [ ! -f "webpack.mix.js.backup" ]; then
        print_error "webpack.mix.js.backup not found. Cannot rollback Laravel Mix."
        exit 1
    fi
    
    # Restore backup files
    cp package.json.backup package.json
    cp webpack.mix.js.backup webpack.mix.js
    
    print_success "Laravel Mix configuration restored to version 2.x"
    
    # Reinstall dependencies
    print_status "Reinstalling dependencies for Laravel Mix 2.x..."
    rm -rf node_modules package-lock.json
    npm install
    
    print_success "Dependencies reinstalled for Laravel Mix 2.x"
}

# Function to remove new architecture
remove_new_architecture() {
    print_status "Removing new SCSS/JS architecture..."
    
    # Remove new architecture directories
    if [ -d "assets/src" ]; then
        rm -rf assets/src
        print_success "Removed assets/src directory"
    fi
    
    # Remove generated CSS from new architecture
    if [ -f "public/css/style.css" ]; then
        rm public/css/style.css
        print_success "Removed new architecture CSS"
    fi
    
    print_success "New architecture removed"
}

# Function to rebuild legacy assets
rebuild_legacy_assets() {
    print_status "Rebuilding legacy assets..."
    
    npm run dev
    
    if [ $? -eq 0 ]; then
        print_success "Legacy assets rebuilt successfully!"
    else
        print_error "Failed to rebuild legacy assets."
        exit 1
    fi
}

# Function to verify rollback
verify_rollback() {
    print_status "Verifying rollback..."
    
    # Check Laravel Mix version
    local mix_version=$(npm list laravel-mix --depth=0 2>/dev/null | grep laravel-mix | sed 's/.*@//')
    print_status "Laravel Mix version: $mix_version"
    
    # Check if legacy CSS exists
    if [ -f "public/css/style-legacy.css" ] || [ -f "public/css/style.css" ]; then
        print_success "CSS files found"
    else
        print_warning "No CSS files found. You may need to run 'npm run dev'"
    fi
    
    # Check if legacy JS exists
    if [ -f "public/js/app.js" ]; then
        print_success "JavaScript files found"
    else
        print_warning "No JavaScript files found. You may need to run 'npm run dev'"
    fi
    
    print_success "Rollback verification completed"
}

# Function to show rollback options
show_rollback_options() {
    echo ""
    print_status "Available rollback options:"
    echo ""
    echo "  1. full        - Complete rollback to Laravel Mix 2.x (removes all new changes)"
    echo "  2. architecture - Remove new SCSS/JS architecture only (keep Laravel Mix 6.x)"
    echo "  3. laravel-mix  - Rollback Laravel Mix only (keep new architecture)"
    echo "  4. verify       - Verify current state without making changes"
    echo "  5. help         - Show this help message"
    echo ""
}

# Function to check system state
check_system_state() {
    print_status "Checking current system state..."
    
    # Check Laravel Mix version
    if [ -f "package.json" ]; then
        local mix_version=$(grep '"laravel-mix"' package.json | sed 's/.*": "//;s/".*//')
        print_status "Current Laravel Mix version: $mix_version"
    fi
    
    # Check if backups exist
    if [ -f "package.json.backup" ]; then
        print_success "package.json backup found"
    else
        print_warning "package.json backup not found"
    fi
    
    if [ -f "webpack.mix.js.backup" ]; then
        print_success "webpack.mix.js backup found"
    else
        print_warning "webpack.mix.js backup not found"
    fi
    
    # Check new architecture
    if [ -d "assets/src" ]; then
        print_status "New architecture directory exists"
    else
        print_status "New architecture directory not found"
    fi
    
    # Check generated files
    if [ -f "public/css/style.css" ]; then
        print_status "New architecture CSS exists"
    fi
    
    if [ -f "public/css/style-legacy.css" ]; then
        print_status "Legacy CSS exists"
    fi
}

# Main execution
main() {
    echo ""
    print_status "VIMA Holding - Rollback Script"
    print_status "==============================="
    echo ""
    
    # Parse command line arguments
    case "${1:-help}" in
        "full")
            confirm_action "This will completely rollback to Laravel Mix 2.x and remove all new architecture."
            create_emergency_backup
            remove_new_architecture
            rollback_laravel_mix
            rebuild_legacy_assets
            verify_rollback
            print_success "Full rollback completed!"
            ;;
        "architecture")
            confirm_action "This will remove the new SCSS/JS architecture but keep Laravel Mix 6.x."
            create_emergency_backup
            remove_new_architecture
            rebuild_legacy_assets
            verify_rollback
            print_success "Architecture rollback completed!"
            ;;
        "laravel-mix")
            confirm_action "This will rollback Laravel Mix to 2.x but keep the new architecture."
            create_emergency_backup
            rollback_laravel_mix
            rebuild_legacy_assets
            verify_rollback
            print_success "Laravel Mix rollback completed!"
            ;;
        "verify")
            check_system_state
            verify_rollback
            ;;
        "help")
            show_rollback_options
            echo "Usage: $0 [option]"
            echo ""
            echo "Examples:"
            echo "  $0 full         - Complete rollback"
            echo "  $0 architecture - Remove new architecture only"
            echo "  $0 verify       - Check current state"
            echo ""
            print_warning "IMPORTANT: Always create a backup before running rollback operations!"
            echo ""
            ;;
        *)
            print_error "Unknown option: $1"
            show_rollback_options
            print_status "Run '$0 help' for detailed information."
            exit 1
            ;;
    esac
    
    echo ""
    print_status "Rollback operation completed!"
    echo ""
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Run main function with all arguments
main "$@"
