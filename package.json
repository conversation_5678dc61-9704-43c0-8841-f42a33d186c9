{"name": "firstlypop", "version": "1.0.0", "description": "Pre-configured bootstrap templating project", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "cross-env NODE_ENV=development webpack --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development webpack --watch --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "hot": "cross-env NODE_ENV=development webpack-dev-server --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "production": "cross-env NODE_ENV=production webpack --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "author": "safa gued<PERSON>", "license": "ISC", "devDependencies": {"browser-sync": "^2.18.13", "browser-sync-webpack-plugin": "^1.2.0", "cross-env": "^5.0.5", "laravel-mix": "^2.0.0"}, "dependencies": {"bootstrap": "^4.0.0", "jquery": "^3.3.1", "jquery-bootstrap-scrolling-tabs": "^2.6.1", "popper.js": "^1.14.6", "swiper": "^6.8.4"}}