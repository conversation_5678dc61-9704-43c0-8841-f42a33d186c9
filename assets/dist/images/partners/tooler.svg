<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="197" height="101" viewBox="0 0 197 101">
  <defs>
    <filter id="tooler." x="0" y="0" width="197" height="101" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#tooler.)">
    <text id="tooler.-2" data-name="tooler." transform="translate(15 69)" font-size="57" font-family="Quicksand-Medium, Quicksand" font-weight="500"><tspan x="0" y="0">tooler.</tspan></text>
  </g>
</svg>
