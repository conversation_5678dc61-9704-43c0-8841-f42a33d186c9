<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="117" height="116" viewBox="0 0 117 116">
  <defs>
    <pattern id="pattern" preserveAspectRatio="none" width="100%" height="100%" viewBox="0 0 225 224">
      <image width="225" height="224" xlink:href="data:image/jpeg;base64,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"/>
    </pattern>
    <filter id="images" x="0" y="0" width="117" height="116" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#images)">
    <rect id="images-2" data-name="images" width="87" height="86" transform="translate(15 12)" fill="url(#pattern)"/>
  </g>
</svg>
