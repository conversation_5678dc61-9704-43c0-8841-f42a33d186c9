/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkvima_holding_frontend"] = self["webpackChunkvima_holding_frontend"] || []).push([["/js/i18n"],{

/***/ "./assets/js/i18n.js":
/*!***************************!*\
  !*** ./assets/js/i18n.js ***!
  \***************************/
/***/ (() => {

eval("{document.addEventListener('DOMContentLoaded', function () {\n  var languageSelector = document.getElementById('language-selector');\n  var defaultLanguage = sessionStorage.getItem('language') || 'en'; // Check session storage for language\n\n  languageSelector.value = defaultLanguage; // Set the selector to the stored language\n\n  languageSelector.addEventListener('change', function () {\n    var selectedLanguage = languageSelector.value;\n    sessionStorage.setItem('language', selectedLanguage); // Store selected language in session storage\n    loadTranslations(selectedLanguage);\n  });\n  function loadTranslations(language) {\n    fetch(\"assets/i18n/\".concat(language, \".json\")).then(function (response) {\n      return response.json();\n    }).then(function (translations) {\n      console.log(translations); // Log the translations object\n      updateTranslations(translations);\n      console.log('Translations loaded:', translations);\n    })[\"catch\"](function (error) {\n      return console.error('Error loading translations:', error);\n    });\n  }\n  function updateTranslations(translations) {\n    var elements = document.querySelectorAll('[data-i18n]');\n    elements.forEach(function (element) {\n      var keys = element.getAttribute('data-i18n').split('.');\n      var translation = translations;\n      var keyExists = true; // Flag to check if key exists\n\n      keys.forEach(function (key) {\n        if (translation && translation[key] !== undefined) {\n          translation = translation[key];\n        } else {\n          keyExists = false; // Key does not exist\n        }\n      });\n      if (keyExists) {\n        element.textContent = translation;\n      } else {\n        console.warn(\"Translation key not found: \".concat(element.getAttribute('data-i18n')));\n      }\n    });\n  }\n\n  // Initialize with default language\n  loadTranslations(defaultLanguage);\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./assets/js/i18n.js\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ var __webpack_exports__ = (__webpack_exec__("./assets/js/i18n.js"));
/******/ }
]);