/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkvima_holding_frontend"] = self["webpackChunkvima_holding_frontend"] || []).push([["/js/scrolling-tabs"],{

/***/ "./assets/js/scrolling-tabs.js":
/*!*************************************!*\
  !*** ./assets/js/scrolling-tabs.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("{/* provided dependency */ var jQuery = __webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\");\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\n/**\r\n * bootstrap4-scrolling-tabs\r\n * @version v1.0.0\r\n * @link https://github.com/mikejacobson/bootstrap4-scrolling-tabs\r\n * <AUTHOR> Jacobson <<EMAIL>>\r\n * @license MIT License, http://www.opensource.org/licenses/MIT\r\n */\n;\n(function ($, window) {\n  'use strict';\n\n  /* jshint unused:false */\n\n  /* exported CONSTANTS */\n  var CONSTANTS = {\n    CONTINUOUS_SCROLLING_TIMEOUT_INTERVAL: 50,\n    // timeout interval for repeatedly moving the tabs container\n    // by one increment while the mouse is held down--decrease to\n    // make mousedown continous scrolling faster\n    SCROLL_OFFSET_FRACTION: 6,\n    // each click moves the container this fraction of the fixed container--decrease\n    // to make the tabs scroll farther per click\n\n    DATA_KEY_DDMENU_MODIFIED: 'scrtabsddmenumodified',\n    DATA_KEY_IS_MOUSEDOWN: 'scrtabsismousedown',\n    DATA_KEY_BOOTSTRAP_TAB: 'bs.tab',\n    CSS_CLASSES: {\n      BOOTSTRAP4: 'scrtabs-bootstrap4',\n      RTL: 'scrtabs-rtl',\n      SCROLL_ARROW_CLICK_TARGET: 'scrtabs-click-target',\n      SCROLL_ARROW_DISABLE: 'scrtabs-disable',\n      SCROLL_ARROW_WITH_CLICK_TARGET: 'scrtabs-with-click-target'\n    },\n    SLIDE_DIRECTION: {\n      LEFT: 1,\n      RIGHT: 2\n    },\n    EVENTS: {\n      CLICK: 'click.scrtabs',\n      DROPDOWN_MENU_HIDE: 'hide.bs.dropdown.scrtabs',\n      DROPDOWN_MENU_SHOW: 'show.bs.dropdown.scrtabs',\n      FORCE_REFRESH: 'forcerefresh.scrtabs',\n      MOUSEDOWN: 'mousedown.scrtabs',\n      MOUSEUP: 'mouseup.scrtabs',\n      TABS_READY: 'ready.scrtabs',\n      TOUCH_END: 'touchend.scrtabs',\n      TOUCH_MOVE: 'touchmove.scrtabs',\n      TOUCH_START: 'touchstart.scrtabs',\n      WINDOW_RESIZE: 'resize.scrtabs'\n    }\n  };\n\n  // smartresize from Paul Irish (debounced window resize)\n  (function (sr) {\n    var debounce = function debounce(func, threshold, execAsap) {\n      var timeout;\n      return function debounced() {\n        var obj = this,\n          args = arguments;\n        function delayed() {\n          if (!execAsap) {\n            func.apply(obj, args);\n          }\n          timeout = null;\n        }\n        if (timeout) {\n          clearTimeout(timeout);\n        } else if (execAsap) {\n          func.apply(obj, args);\n        }\n        timeout = setTimeout(delayed, threshold || 100);\n      };\n    };\n    $.fn[sr] = function (fn, customEventName) {\n      var eventName = customEventName || CONSTANTS.EVENTS.WINDOW_RESIZE;\n      return fn ? this.bind(eventName, debounce(fn)) : this.trigger(sr);\n    };\n  })('smartresizeScrtabs');\n\n  /* ***********************************************************************************\r\n   * ElementsHandler - Class that each instance of ScrollingTabsControl will instantiate\r\n   * **********************************************************************************/\n  function ElementsHandler(scrollingTabsControl) {\n    var ehd = this;\n    ehd.stc = scrollingTabsControl;\n  }\n\n  // ElementsHandler prototype methods\n  (function (p) {\n    p.initElements = function (options) {\n      var ehd = this;\n      ehd.setElementReferences(options);\n      ehd.setEventListeners(options);\n    };\n    p.listenForTouchEvents = function () {\n      var ehd = this,\n        stc = ehd.stc,\n        smv = stc.scrollMovement,\n        ev = CONSTANTS.EVENTS;\n      var touching = false;\n      var touchStartX;\n      var startingContainerLeftPos;\n      var newLeftPos;\n      stc.$movableContainer.on(ev.TOUCH_START, function (e) {\n        touching = true;\n        startingContainerLeftPos = stc.movableContainerLeftPos;\n        touchStartX = e.originalEvent.changedTouches[0].pageX;\n      }).on(ev.TOUCH_END, function () {\n        touching = false;\n      }).on(ev.TOUCH_MOVE, function (e) {\n        if (!touching) {\n          return;\n        }\n        var touchPageX = e.originalEvent.changedTouches[0].pageX;\n        var diff = touchPageX - touchStartX;\n        if (stc.rtl) {\n          diff = -diff;\n        }\n        var minPos;\n        newLeftPos = startingContainerLeftPos + diff;\n        if (newLeftPos > 0) {\n          newLeftPos = 0;\n        } else {\n          minPos = smv.getMinPos();\n          if (newLeftPos < minPos) {\n            newLeftPos = minPos;\n          }\n        }\n        stc.movableContainerLeftPos = newLeftPos;\n        var leftOrRight = stc.rtl ? 'right' : 'left';\n        stc.$movableContainer.css(leftOrRight, smv.getMovableContainerCssLeftVal());\n        smv.refreshScrollArrowsDisabledState();\n      });\n    };\n    p.refreshAllElementSizes = function () {\n      var ehd = this,\n        stc = ehd.stc,\n        smv = stc.scrollMovement,\n        scrollArrowsWereVisible = stc.scrollArrowsVisible,\n        actionsTaken = {\n          didScrollToActiveTab: false\n        },\n        isPerformingSlideAnim = false,\n        minPos;\n      ehd.setElementWidths();\n      ehd.setScrollArrowVisibility();\n\n      // this could have been a window resize or the removal of a\n      // dynamic tab, so make sure the movable container is positioned\n      // correctly because, if it is far to the left and we increased the\n      // window width, it's possible that the tabs will be too far left,\n      // beyond the min pos.\n      if (stc.scrollArrowsVisible) {\n        // make sure container not too far left\n        minPos = smv.getMinPos();\n        isPerformingSlideAnim = smv.scrollToActiveTab({\n          isOnWindowResize: true\n        });\n        if (!isPerformingSlideAnim) {\n          smv.refreshScrollArrowsDisabledState();\n          if (stc.rtl) {\n            if (stc.movableContainerRightPos < minPos) {\n              smv.incrementMovableContainerLeft(minPos);\n            }\n          } else {\n            if (stc.movableContainerLeftPos < minPos) {\n              smv.incrementMovableContainerRight(minPos);\n            }\n          }\n        }\n        actionsTaken.didScrollToActiveTab = true;\n      } else if (scrollArrowsWereVisible) {\n        // scroll arrows went away after resize, so position movable container at 0\n        stc.movableContainerLeftPos = 0;\n        smv.slideMovableContainerToLeftPos();\n      }\n      return actionsTaken;\n    };\n    p.setElementReferences = function (settings) {\n      var ehd = this,\n        stc = ehd.stc,\n        $tabsContainer = stc.$tabsContainer,\n        $leftArrow,\n        $rightArrow,\n        $leftArrowClickTarget,\n        $rightArrowClickTarget;\n      stc.isNavPills = false;\n      if (stc.rtl) {\n        $tabsContainer.addClass(CONSTANTS.CSS_CLASSES.RTL);\n      }\n      if (stc.usingBootstrap4) {\n        $tabsContainer.addClass(CONSTANTS.CSS_CLASSES.BOOTSTRAP4);\n      }\n      stc.$fixedContainer = $tabsContainer.find('.scrtabs-tabs-fixed-container');\n      $leftArrow = stc.$fixedContainer.prev();\n      $rightArrow = stc.$fixedContainer.next();\n\n      // if we have custom arrow content, we might have a click target defined\n      if (settings.leftArrowContent) {\n        $leftArrowClickTarget = $leftArrow.find('.' + CONSTANTS.CSS_CLASSES.SCROLL_ARROW_CLICK_TARGET);\n      }\n      if (settings.rightArrowContent) {\n        $rightArrowClickTarget = $rightArrow.find('.' + CONSTANTS.CSS_CLASSES.SCROLL_ARROW_CLICK_TARGET);\n      }\n      if ($leftArrowClickTarget && $leftArrowClickTarget.length) {\n        $leftArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_WITH_CLICK_TARGET);\n      } else {\n        $leftArrowClickTarget = $leftArrow;\n      }\n      if ($rightArrowClickTarget && $rightArrowClickTarget.length) {\n        $rightArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_WITH_CLICK_TARGET);\n      } else {\n        $rightArrowClickTarget = $rightArrow;\n      }\n      stc.$movableContainer = $tabsContainer.find('.scrtabs-tabs-movable-container');\n      stc.$tabsUl = $tabsContainer.find('.nav-tabs');\n\n      // check for pills\n      if (!stc.$tabsUl.length) {\n        stc.$tabsUl = $tabsContainer.find('.nav-pills');\n        if (stc.$tabsUl.length) {\n          stc.isNavPills = true;\n        }\n      }\n      stc.$tabsLiCollection = stc.$tabsUl.find('> li');\n      stc.$slideLeftArrow = stc.reverseScroll ? $leftArrow : $rightArrow;\n      stc.$slideLeftArrowClickTarget = stc.reverseScroll ? $leftArrowClickTarget : $rightArrowClickTarget;\n      stc.$slideRightArrow = stc.reverseScroll ? $rightArrow : $leftArrow;\n      stc.$slideRightArrowClickTarget = stc.reverseScroll ? $rightArrowClickTarget : $leftArrowClickTarget;\n      stc.$scrollArrows = stc.$slideLeftArrow.add(stc.$slideRightArrow);\n      stc.$win = $(window);\n    };\n    p.setElementWidths = function () {\n      var ehd = this,\n        stc = ehd.stc;\n      stc.winWidth = stc.$win.width();\n      stc.scrollArrowsCombinedWidth = stc.$slideLeftArrow.outerWidth() + stc.$slideRightArrow.outerWidth();\n      ehd.setFixedContainerWidth();\n      ehd.setMovableContainerWidth();\n    };\n    p.setEventListeners = function (settings) {\n      var ehd = this,\n        stc = ehd.stc,\n        evh = stc.eventHandlers,\n        ev = CONSTANTS.EVENTS,\n        resizeEventName = ev.WINDOW_RESIZE + stc.instanceId;\n      if (settings.enableSwiping) {\n        ehd.listenForTouchEvents();\n      }\n      stc.$slideLeftArrowClickTarget.off('.scrtabs').on(ev.MOUSEDOWN, function (e) {\n        evh.handleMousedownOnSlideMovContainerLeftArrow.call(evh, e);\n      }).on(ev.MOUSEUP, function (e) {\n        evh.handleMouseupOnSlideMovContainerLeftArrow.call(evh, e);\n      }).on(ev.CLICK, function (e) {\n        evh.handleClickOnSlideMovContainerLeftArrow.call(evh, e);\n      });\n      stc.$slideRightArrowClickTarget.off('.scrtabs').on(ev.MOUSEDOWN, function (e) {\n        evh.handleMousedownOnSlideMovContainerRightArrow.call(evh, e);\n      }).on(ev.MOUSEUP, function (e) {\n        evh.handleMouseupOnSlideMovContainerRightArrow.call(evh, e);\n      }).on(ev.CLICK, function (e) {\n        evh.handleClickOnSlideMovContainerRightArrow.call(evh, e);\n      });\n      if (stc.tabClickHandler) {\n        stc.$tabsLiCollection.find('a[data-toggle=\"tab\"]').off(ev.CLICK).on(ev.CLICK, stc.tabClickHandler);\n      }\n      if (settings.handleDelayedScrollbar) {\n        ehd.listenForDelayedScrollbar();\n      }\n      stc.$win.off(resizeEventName).smartresizeScrtabs(function (e) {\n        evh.handleWindowResize.call(evh, e);\n      }, resizeEventName);\n      $('body').on(CONSTANTS.EVENTS.FORCE_REFRESH, stc.elementsHandler.refreshAllElementSizes.bind(stc.elementsHandler));\n    };\n    p.listenForDelayedScrollbar = function () {\n      var iframe = document.createElement('iframe');\n      iframe.id = \"scrtabs-scrollbar-resize-listener\";\n      iframe.style.cssText = 'height: 0; background-color: transparent; margin: 0; padding: 0; overflow: hidden; border-width: 0; position: absolute; width: 100%;';\n      iframe.onload = function () {\n        var timeout;\n        function handleResize() {\n          try {\n            $(window).trigger('resize');\n            timeout = null;\n          } catch (e) {}\n        }\n        iframe.contentWindow.addEventListener('resize', function () {\n          if (timeout) {\n            clearTimeout(timeout);\n          }\n          timeout = setTimeout(handleResize, 100);\n        });\n      };\n      document.body.appendChild(iframe);\n    };\n    p.setFixedContainerWidth = function () {\n      var ehd = this,\n        stc = ehd.stc,\n        tabsContainerRect = stc.$tabsContainer.get(0).getBoundingClientRect();\n      /**\r\n       * <AUTHOR>       * It solves problem with rounding by jQuery.outerWidth\r\n       * If we have real width 100.5 px, jQuery.outerWidth returns us 101 px and we get layout's fail\r\n       */\n      stc.fixedContainerWidth = tabsContainerRect.width || tabsContainerRect.right - tabsContainerRect.left;\n      stc.fixedContainerWidth = stc.fixedContainerWidth * stc.widthMultiplier;\n      stc.$fixedContainer.width(stc.fixedContainerWidth);\n    };\n    p.setFixedContainerWidthForHiddenScrollArrows = function () {\n      var ehd = this,\n        stc = ehd.stc;\n      stc.$fixedContainer.width(stc.fixedContainerWidth);\n    };\n    p.setFixedContainerWidthForVisibleScrollArrows = function () {\n      var ehd = this,\n        stc = ehd.stc;\n      stc.$fixedContainer.width(stc.fixedContainerWidth - stc.scrollArrowsCombinedWidth);\n    };\n    p.setMovableContainerWidth = function () {\n      var ehd = this,\n        stc = ehd.stc,\n        $tabLi = stc.$tabsUl.find('> li');\n      stc.movableContainerWidth = 0;\n      if ($tabLi.length) {\n        $tabLi.each(function () {\n          var $li = $(this),\n            totalMargin = 0;\n          if (stc.isNavPills) {\n            // pills have a margin-left, tabs have no margin\n            totalMargin = parseInt($li.css('margin-left'), 10) + parseInt($li.css('margin-right'), 10);\n          }\n          stc.movableContainerWidth += $li.outerWidth() + totalMargin;\n        });\n        stc.movableContainerWidth += 1;\n\n        // if the tabs don't span the width of the page, force the\n        // movable container width to full page width so the bottom\n        // border spans the page width instead of just spanning the\n        // width of the tabs\n        if (stc.movableContainerWidth < stc.fixedContainerWidth) {\n          stc.movableContainerWidth = stc.fixedContainerWidth;\n        }\n      }\n      stc.$movableContainer.width(stc.movableContainerWidth);\n    };\n    p.setScrollArrowVisibility = function () {\n      var ehd = this,\n        stc = ehd.stc,\n        shouldBeVisible = stc.movableContainerWidth > stc.fixedContainerWidth;\n      if (shouldBeVisible && !stc.scrollArrowsVisible) {\n        stc.$scrollArrows.show();\n        stc.scrollArrowsVisible = true;\n      } else if (!shouldBeVisible && stc.scrollArrowsVisible) {\n        stc.$scrollArrows.hide();\n        stc.scrollArrowsVisible = false;\n      }\n      if (stc.scrollArrowsVisible) {\n        ehd.setFixedContainerWidthForVisibleScrollArrows();\n      } else {\n        ehd.setFixedContainerWidthForHiddenScrollArrows();\n      }\n    };\n  })(ElementsHandler.prototype);\n\n  /* ***********************************************************************************\r\n   * EventHandlers - Class that each instance of ScrollingTabsControl will instantiate\r\n   * **********************************************************************************/\n  function EventHandlers(scrollingTabsControl) {\n    var evh = this;\n    evh.stc = scrollingTabsControl;\n  }\n\n  // prototype methods\n  (function (p) {\n    p.handleClickOnSlideMovContainerLeftArrow = function () {\n      var evh = this,\n        stc = evh.stc;\n      stc.scrollMovement.incrementMovableContainerLeft();\n    };\n    p.handleClickOnSlideMovContainerRightArrow = function () {\n      var evh = this,\n        stc = evh.stc;\n      stc.scrollMovement.incrementMovableContainerRight();\n    };\n    p.handleMousedownOnSlideMovContainerLeftArrow = function () {\n      var evh = this,\n        stc = evh.stc;\n      stc.$slideLeftArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, true);\n      stc.scrollMovement.continueSlideMovableContainerLeft();\n    };\n    p.handleMousedownOnSlideMovContainerRightArrow = function () {\n      var evh = this,\n        stc = evh.stc;\n      stc.$slideRightArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, true);\n      stc.scrollMovement.continueSlideMovableContainerRight();\n    };\n    p.handleMouseupOnSlideMovContainerLeftArrow = function () {\n      var evh = this,\n        stc = evh.stc;\n      stc.$slideLeftArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, false);\n    };\n    p.handleMouseupOnSlideMovContainerRightArrow = function () {\n      var evh = this,\n        stc = evh.stc;\n      stc.$slideRightArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, false);\n    };\n    p.handleWindowResize = function () {\n      var evh = this,\n        stc = evh.stc,\n        newWinWidth = stc.$win.width();\n      if (newWinWidth === stc.winWidth) {\n        return false;\n      }\n      stc.winWidth = newWinWidth;\n      stc.elementsHandler.refreshAllElementSizes();\n    };\n  })(EventHandlers.prototype);\n\n  /* ***********************************************************************************\r\n   * ScrollMovement - Class that each instance of ScrollingTabsControl will instantiate\r\n   * **********************************************************************************/\n  function ScrollMovement(scrollingTabsControl) {\n    var smv = this;\n    smv.stc = scrollingTabsControl;\n  }\n\n  // prototype methods\n  (function (p) {\n    p.continueSlideMovableContainerLeft = function () {\n      var smv = this,\n        stc = smv.stc;\n      setTimeout(function () {\n        if (stc.movableContainerLeftPos <= smv.getMinPos() || !stc.$slideLeftArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN)) {\n          return;\n        }\n        if (!smv.incrementMovableContainerLeft()) {\n          // haven't reached max left\n          smv.continueSlideMovableContainerLeft();\n        }\n      }, CONSTANTS.CONTINUOUS_SCROLLING_TIMEOUT_INTERVAL);\n    };\n    p.continueSlideMovableContainerRight = function () {\n      var smv = this,\n        stc = smv.stc;\n      setTimeout(function () {\n        if (stc.movableContainerLeftPos >= 0 || !stc.$slideRightArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN)) {\n          return;\n        }\n        if (!smv.incrementMovableContainerRight()) {\n          // haven't reached max right\n          smv.continueSlideMovableContainerRight();\n        }\n      }, CONSTANTS.CONTINUOUS_SCROLLING_TIMEOUT_INTERVAL);\n    };\n    p.decrementMovableContainerLeftPos = function (minPos) {\n      var smv = this,\n        stc = smv.stc;\n      stc.movableContainerLeftPos -= stc.fixedContainerWidth / CONSTANTS.SCROLL_OFFSET_FRACTION;\n      if (stc.movableContainerLeftPos < minPos) {\n        stc.movableContainerLeftPos = minPos;\n      } else if (stc.scrollToTabEdge) {\n        smv.setMovableContainerLeftPosToTabEdge(CONSTANTS.SLIDE_DIRECTION.LEFT);\n        if (stc.movableContainerLeftPos < minPos) {\n          stc.movableContainerLeftPos = minPos;\n        }\n      }\n    };\n    p.disableSlideLeftArrow = function () {\n      var smv = this,\n        stc = smv.stc;\n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n      stc.$slideLeftArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n    p.disableSlideRightArrow = function () {\n      var smv = this,\n        stc = smv.stc;\n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n      stc.$slideRightArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n    p.enableSlideLeftArrow = function () {\n      var smv = this,\n        stc = smv.stc;\n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n      stc.$slideLeftArrow.removeClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n    p.enableSlideRightArrow = function () {\n      var smv = this,\n        stc = smv.stc;\n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n      stc.$slideRightArrow.removeClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n    p.getMinPos = function () {\n      var smv = this,\n        stc = smv.stc;\n      return stc.scrollArrowsVisible ? stc.fixedContainerWidth - stc.movableContainerWidth - stc.scrollArrowsCombinedWidth : 0;\n    };\n    p.getMovableContainerCssLeftVal = function () {\n      var smv = this,\n        stc = smv.stc;\n      return stc.movableContainerLeftPos === 0 ? '0' : stc.movableContainerLeftPos + 'px';\n    };\n    p.incrementMovableContainerLeft = function () {\n      var smv = this,\n        stc = smv.stc,\n        minPos = smv.getMinPos();\n      smv.decrementMovableContainerLeftPos(minPos);\n      smv.slideMovableContainerToLeftPos();\n      smv.enableSlideRightArrow();\n\n      // return true if we're fully left, false otherwise\n      return stc.movableContainerLeftPos === minPos;\n    };\n    p.incrementMovableContainerRight = function (minPos) {\n      var smv = this,\n        stc = smv.stc;\n\n      // if minPos passed in, the movable container was beyond the minPos\n      if (minPos) {\n        stc.movableContainerLeftPos = minPos;\n      } else {\n        stc.movableContainerLeftPos += stc.fixedContainerWidth / CONSTANTS.SCROLL_OFFSET_FRACTION;\n        if (stc.movableContainerLeftPos > 0) {\n          stc.movableContainerLeftPos = 0;\n        } else if (stc.scrollToTabEdge) {\n          smv.setMovableContainerLeftPosToTabEdge(CONSTANTS.SLIDE_DIRECTION.RIGHT);\n        }\n      }\n      smv.slideMovableContainerToLeftPos();\n      smv.enableSlideLeftArrow();\n\n      // return true if we're fully right, false otherwise\n      // left pos of 0 is the movable container's max position (farthest right)\n      return stc.movableContainerLeftPos === 0;\n    };\n    p.refreshScrollArrowsDisabledState = function () {\n      var smv = this,\n        stc = smv.stc;\n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n      if (stc.movableContainerLeftPos >= 0) {\n        // movable container fully right\n        smv.disableSlideRightArrow();\n        smv.enableSlideLeftArrow();\n        return;\n      }\n      if (stc.movableContainerLeftPos <= smv.getMinPos()) {\n        // fully left\n        smv.disableSlideLeftArrow();\n        smv.enableSlideRightArrow();\n        return;\n      }\n      smv.enableSlideLeftArrow();\n      smv.enableSlideRightArrow();\n    };\n    p.scrollToActiveTab = function () {\n      var smv = this,\n        stc = smv.stc,\n        $activeTab,\n        $activeTabAnchor,\n        activeTabLeftPos,\n        activeTabRightPos,\n        rightArrowLeftPos,\n        activeTabWidth,\n        leftPosOffset,\n        offsetToMiddle,\n        leftScrollArrowWidth,\n        rightScrollArrowWidth;\n      if (!stc.scrollArrowsVisible) {\n        return;\n      }\n      $activeTabAnchor = stc.$tabsUl.find('li > .nav-link.active');\n      if ($activeTabAnchor.length) {\n        $activeTab = $activeTabAnchor.parent();\n      }\n      if (!$activeTab || !$activeTab.length) {\n        return;\n      }\n      rightScrollArrowWidth = stc.$slideRightArrow.outerWidth();\n      activeTabWidth = $activeTab.outerWidth();\n\n      /**\r\n       * <AUTHOR>       * We need relative offset (depends on $fixedContainer), don't absolute\r\n       */\n      activeTabLeftPos = $activeTab.offset().left - stc.$fixedContainer.offset().left;\n      activeTabRightPos = activeTabLeftPos + activeTabWidth;\n      rightArrowLeftPos = stc.fixedContainerWidth - rightScrollArrowWidth;\n      if (stc.rtl) {\n        leftScrollArrowWidth = stc.$slideLeftArrow.outerWidth();\n        if (activeTabLeftPos < 0) {\n          // active tab off left side\n          stc.movableContainerLeftPos += activeTabLeftPos;\n          smv.slideMovableContainerToLeftPos();\n          return true;\n        } else {\n          // active tab off right side\n          if (activeTabRightPos > rightArrowLeftPos) {\n            stc.movableContainerLeftPos += activeTabRightPos - rightArrowLeftPos + 2 * rightScrollArrowWidth;\n            smv.slideMovableContainerToLeftPos();\n            return true;\n          }\n        }\n      } else {\n        if (activeTabRightPos > rightArrowLeftPos) {\n          // active tab off right side\n          leftPosOffset = activeTabRightPos - rightArrowLeftPos + rightScrollArrowWidth;\n          offsetToMiddle = stc.fixedContainerWidth / 2;\n          leftPosOffset += offsetToMiddle - activeTabWidth / 2;\n          stc.movableContainerLeftPos -= leftPosOffset;\n          smv.slideMovableContainerToLeftPos();\n          return true;\n        } else {\n          leftScrollArrowWidth = stc.$slideLeftArrow.outerWidth();\n          if (activeTabLeftPos < 0) {\n            // active tab off left side\n            offsetToMiddle = stc.fixedContainerWidth / 2;\n            stc.movableContainerLeftPos += -activeTabLeftPos + offsetToMiddle - activeTabWidth / 2;\n            smv.slideMovableContainerToLeftPos();\n            return true;\n          }\n        }\n      }\n      return false;\n    };\n    p.setMovableContainerLeftPosToTabEdge = function (slideDirection) {\n      var smv = this,\n        stc = smv.stc,\n        offscreenWidth = -stc.movableContainerLeftPos,\n        totalTabWidth = 0;\n\n      // make sure LeftPos is set so that a tab edge will be against the\n      // left scroll arrow so we won't have a partial, cut-off tab\n      stc.$tabsLiCollection.each(function () {\n        var tabWidth = $(this).width();\n        totalTabWidth += tabWidth;\n        if (totalTabWidth > offscreenWidth) {\n          stc.movableContainerLeftPos = slideDirection === CONSTANTS.SLIDE_DIRECTION.RIGHT ? -(totalTabWidth - tabWidth) : -totalTabWidth;\n          return false; // exit .each() loop\n        }\n      });\n    };\n    p.slideMovableContainerToLeftPos = function () {\n      var smv = this,\n        stc = smv.stc,\n        minPos = smv.getMinPos(),\n        leftOrRightVal;\n      if (stc.movableContainerLeftPos > 0) {\n        stc.movableContainerLeftPos = 0;\n      } else if (stc.movableContainerLeftPos < minPos) {\n        stc.movableContainerLeftPos = minPos;\n      }\n      stc.movableContainerLeftPos = stc.movableContainerLeftPos / 1;\n      leftOrRightVal = smv.getMovableContainerCssLeftVal();\n      smv.performingSlideAnim = true;\n      stc.$movableContainer.css({\n        transform: 'translateX(' + leftOrRightVal + ')'\n      });\n      stc.$movableContainer.on('transitionend.scrtabs', function () {\n        stc.$movableContainer.off('transitionend.scrtabs');\n        smv.performingSlideAnim = false;\n        smv.refreshScrollArrowsDisabledState();\n      });\n    };\n  })(ScrollMovement.prototype);\n\n  /* **********************************************************************\r\n   * ScrollingTabsControl - Class that each directive will instantiate\r\n   * **********************************************************************/\n  function ScrollingTabsControl($tabsContainer) {\n    var stc = this;\n    stc.$tabsContainer = $tabsContainer;\n    stc.instanceId = $.fn.scrollingTabs.nextInstanceId++;\n    stc.movableContainerLeftPos = 0;\n    stc.scrollArrowsVisible = false;\n    stc.scrollToTabEdge = false;\n    stc.disableScrollArrowsOnFullyScrolled = false;\n    stc.reverseScroll = false;\n    stc.widthMultiplier = 1;\n    stc.scrollMovement = new ScrollMovement(stc);\n    stc.eventHandlers = new EventHandlers(stc);\n    stc.elementsHandler = new ElementsHandler(stc);\n  }\n\n  // prototype methods\n  (function (p) {\n    p.initTabs = function (options, $scroller, readyCallback, attachTabContentToDomCallback) {\n      var stc = this,\n        elementsHandler = stc.elementsHandler,\n        num;\n      if (options.enableRtlSupport && $('html').attr('dir') === 'rtl') {\n        stc.rtl = true;\n      }\n      if (options.scrollToTabEdge) {\n        stc.scrollToTabEdge = true;\n      }\n      if (options.disableScrollArrowsOnFullyScrolled) {\n        stc.disableScrollArrowsOnFullyScrolled = true;\n      }\n      if (options.reverseScroll) {\n        stc.reverseScroll = true;\n      }\n      if (options.widthMultiplier !== 1) {\n        num = Number(options.widthMultiplier); // handle string value\n\n        if (!isNaN(num)) {\n          stc.widthMultiplier = num;\n        }\n      }\n      if (options.bootstrapVersion.toString().charAt(0) === '4') {\n        stc.usingBootstrap4 = true;\n      }\n      setTimeout(initTabsAfterTimeout, 100);\n      function initTabsAfterTimeout() {\n        var actionsTaken;\n\n        // if we're just wrapping non-data-driven tabs, the user might\n        // have the .nav-tabs hidden to prevent the clunky flash of\n        // multi-line tabs on page refresh, so we need to make sure\n        // they're visible before trying to wrap them\n        $scroller.find('.nav-tabs').show();\n        elementsHandler.initElements(options);\n        actionsTaken = elementsHandler.refreshAllElementSizes();\n        $scroller.css('visibility', 'visible');\n        if (attachTabContentToDomCallback) {\n          attachTabContentToDomCallback();\n        }\n        if (readyCallback) {\n          readyCallback();\n        }\n      }\n    };\n    p.scrollToActiveTab = function (options) {\n      var stc = this,\n        smv = stc.scrollMovement;\n      smv.scrollToActiveTab(options);\n    };\n  })(ScrollingTabsControl.prototype);\n\n  /* exported buildNavTabsAndTabContentForTargetElementInstance */\n  var tabElements = function () {\n    return {\n      getElTabPaneForLi: getElTabPaneForLi,\n      getNewElNavTabs: getNewElNavTabs,\n      getNewElScrollerElementWrappingNavTabsInstance: getNewElScrollerElementWrappingNavTabsInstance,\n      getNewElTabAnchor: getNewElTabAnchor,\n      getNewElTabContent: getNewElTabContent,\n      getNewElTabLi: getNewElTabLi,\n      getNewElTabPane: getNewElTabPane\n    };\n\n    ///////////////////\n\n    // ---- retrieve existing elements from the DOM ----------\n    function getElTabPaneForLi($li) {\n      return $($li.find('a').attr('href'));\n    }\n\n    // ---- create new elements ----------\n    function getNewElNavTabs() {\n      return $('<ul class=\"nav nav-tabs\" role=\"tablist\"></ul>');\n    }\n    function getDefaultLeftArrow(settings) {\n      return ['<div class=\"scrtabs-tab-scroll-arrow scrtabs-tab-scroll-arrow-left\">', '  <span class=\"' + settings.cssClassLeftArrow + '\"></span>', '</div>'].join('');\n    }\n    function getDefaultRightArrow(settings) {\n      return ['<div class=\"scrtabs-tab-scroll-arrow scrtabs-tab-scroll-arrow-right\">', '  <span class=\"' + settings.cssClassRightArrow + '\"></span>', '</div>'].join('');\n    }\n    function getTabContainerHtml() {\n      return '<div class=\"scrtabs-tab-container\"></div>';\n    }\n    function getFixedContainerHtml() {\n      return '<div class=\"scrtabs-tabs-fixed-container\"></div>';\n    }\n    function getMovableContainerHtml() {\n      return '<div class=\"scrtabs-tabs-movable-container\"></div>';\n    }\n    function getNewElScrollerElementWrappingNavTabsInstance($navTabsInstance, settings) {\n      var $tabsContainer = $(getTabContainerHtml());\n      var leftArrowContent = settings.leftArrowContent || getDefaultLeftArrow(settings);\n      var $leftArrow = $(leftArrowContent);\n      var rightArrowContent = settings.rightArrowContent || getDefaultRightArrow(settings);\n      var $rightArrow = $(rightArrowContent);\n      var $fixedContainer = $(getFixedContainerHtml());\n      var $movableContainer = $(getMovableContainerHtml());\n      if (settings.disableScrollArrowsOnFullyScrolled) {\n        $leftArrow.add($rightArrow).addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n      }\n      return $tabsContainer.append($leftArrow, $fixedContainer.append($movableContainer.append($navTabsInstance)), $rightArrow);\n    }\n    function getTabAnchorHtml() {\n      return '<a class=\"nav-link\" role=\"tab\" data-toggle=\"tab\"></a>';\n    }\n    function getNewElTabAnchor(tab, propNames) {\n      return $(getTabAnchorHtml()).attr('href', '#' + tab[propNames.paneId]).html(tab[propNames.title]);\n    }\n    function getNewElTabContent() {\n      return $('<div class=\"tab-content\"></div>');\n    }\n    function getDefaultTabLiHtml() {\n      return '<li class=\"nav-item\"></li>';\n    }\n    function getNewElTabLi(tab, propNames, options) {\n      var liContent = options.tabLiContent || getDefaultTabLiHtml();\n      var $li = $(liContent);\n      var $a = getNewElTabAnchor(tab, propNames).appendTo($li);\n      if (tab[propNames.disabled]) {\n        $a.addClass('disabled');\n        $a.attr('data-toggle', '');\n      } else if (options.forceActiveTab && tab[propNames.active]) {\n        $a.addClass('active');\n      }\n      if (options.tabPostProcessor) {\n        options.tabPostProcessor($li, $a);\n      }\n      return $li;\n    }\n    function getNewElTabPane(tab, propNames, options) {\n      var $pane = $('<div role=\"tabpanel\" class=\"tab-pane fade show\"></div>').attr('id', tab[propNames.paneId]).html(tab[propNames.content]);\n      if (options.forceActiveTab && tab[propNames.active]) {\n        $pane.addClass('active');\n      }\n      return $pane;\n    }\n  }(); // tabElements\n\n  var tabUtils = function () {\n    return {\n      didTabOrderChange: didTabOrderChange,\n      getIndexOfClosestEnabledTab: getIndexOfClosestEnabledTab,\n      getTabIndexByPaneId: getTabIndexByPaneId,\n      storeDataOnLiEl: storeDataOnLiEl\n    };\n\n    ///////////////////\n\n    function didTabOrderChange($currTabLis, updatedTabs, propNames) {\n      var isTabOrderChanged = false;\n      $currTabLis.each(function (currDomIdx) {\n        var newIdx = getTabIndexByPaneId(updatedTabs, propNames.paneId, $(this).data('tab')[propNames.paneId]);\n        if (newIdx > -1 && newIdx !== currDomIdx) {\n          // tab moved\n          isTabOrderChanged = true;\n          return false; // exit .each() loop\n        }\n      });\n      return isTabOrderChanged;\n    }\n    function getIndexOfClosestEnabledTab($currTabLis, startIndex) {\n      var lastIndex = $currTabLis.length - 1;\n      var closestIdx = -1;\n      var incrementFromStartIndex = 0;\n      var testIdx = 0;\n\n      // expand out from the current tab looking for an enabled tab;\n      // we prefer the tab after us over the tab before\n      while (closestIdx === -1 && testIdx >= 0) {\n        if ((testIdx = startIndex + ++incrementFromStartIndex) <= lastIndex && !$currTabLis.eq(testIdx).hasClass('disabled') || (testIdx = startIndex - incrementFromStartIndex) >= 0 && !$currTabLis.eq(testIdx).hasClass('disabled')) {\n          closestIdx = testIdx;\n        }\n      }\n      return closestIdx;\n    }\n    function getTabIndexByPaneId(tabs, paneIdPropName, paneId) {\n      var idx = -1;\n      tabs.some(function (tab, i) {\n        if (tab[paneIdPropName] === paneId) {\n          idx = i;\n          return true; // exit loop\n        }\n      });\n      return idx;\n    }\n    function storeDataOnLiEl($li, tabs, index) {\n      $li.data({\n        tab: $.extend({}, tabs[index]),\n        // store a clone so we can check for changes\n        index: index\n      });\n    }\n  }(); // tabUtils\n\n  function buildNavTabsAndTabContentForTargetElementInstance($targetElInstance, settings, readyCallback) {\n    var tabs = settings.tabs;\n    var propNames = {\n      paneId: settings.propPaneId,\n      title: settings.propTitle,\n      active: settings.propActive,\n      disabled: settings.propDisabled,\n      content: settings.propContent\n    };\n    var ignoreTabPanes = settings.ignoreTabPanes;\n    var hasTabContent = tabs.length && tabs[0][propNames.content] !== undefined;\n    var $navTabs = tabElements.getNewElNavTabs();\n    var $tabContent = tabElements.getNewElTabContent();\n    var $scroller;\n    var attachTabContentToDomCallback = ignoreTabPanes ? null : function () {\n      $scroller.after($tabContent);\n    };\n    if (!tabs.length) {\n      return;\n    }\n    tabs.forEach(function (tab, index) {\n      var options = {\n        forceActiveTab: true,\n        tabLiContent: settings.tabsLiContent && settings.tabsLiContent[index],\n        tabPostProcessor: settings.tabsPostProcessors && settings.tabsPostProcessors[index]\n      };\n      tabElements.getNewElTabLi(tab, propNames, options).appendTo($navTabs);\n\n      // build the tab panes if we weren't told to ignore them and there's\n      // tab content data available\n      if (!ignoreTabPanes && hasTabContent) {\n        tabElements.getNewElTabPane(tab, propNames, options).appendTo($tabContent);\n      }\n    });\n    $scroller = wrapNavTabsInstanceInScroller($navTabs, settings, readyCallback, attachTabContentToDomCallback);\n    $scroller.appendTo($targetElInstance);\n    $targetElInstance.data({\n      scrtabs: {\n        tabs: tabs,\n        propNames: propNames,\n        ignoreTabPanes: ignoreTabPanes,\n        hasTabContent: hasTabContent,\n        tabsLiContent: settings.tabsLiContent,\n        tabsPostProcessors: settings.tabsPostProcessors,\n        scroller: $scroller\n      }\n    });\n\n    // once the nav-tabs are wrapped in the scroller, attach each tab's\n    // data to it for reference later; we need to wait till they're\n    // wrapped in the scroller because we wrap a *clone* of the nav-tabs\n    // we built above, not the original nav-tabs\n    $scroller.find('.nav-tabs > li').each(function (index) {\n      tabUtils.storeDataOnLiEl($(this), tabs, index);\n    });\n    return $targetElInstance;\n  }\n  function wrapNavTabsInstanceInScroller($navTabsInstance, settings, readyCallback, attachTabContentToDomCallback) {\n    // Remove tab data stored by Bootstrap in order to fix tabs that were already visited\n    $navTabsInstance.find('a[data-toggle=\"tab\"]').removeData(CONSTANTS.DATA_KEY_BOOTSTRAP_TAB);\n    var $scroller = tabElements.getNewElScrollerElementWrappingNavTabsInstance($navTabsInstance.clone(true), settings); // use clone because we replaceWith later\n    var scrollingTabsControl = new ScrollingTabsControl($scroller);\n    var navTabsInstanceData = $navTabsInstance.data('scrtabs');\n    if (!navTabsInstanceData) {\n      $navTabsInstance.data('scrtabs', {\n        scroller: $scroller\n      });\n    } else {\n      navTabsInstanceData.scroller = $scroller;\n    }\n    $navTabsInstance.replaceWith($scroller.css('visibility', 'hidden'));\n    if (settings.tabClickHandler && typeof settings.tabClickHandler === 'function') {\n      $scroller.hasTabClickHandler = true;\n      scrollingTabsControl.tabClickHandler = settings.tabClickHandler;\n    }\n    $scroller.initTabs = function () {\n      scrollingTabsControl.initTabs(settings, $scroller, readyCallback, attachTabContentToDomCallback);\n    };\n    $scroller.scrollToActiveTab = function () {\n      scrollingTabsControl.scrollToActiveTab(settings);\n    };\n    $scroller.initTabs();\n    listenForDropdownMenuTabs($scroller, scrollingTabsControl);\n    return $scroller;\n  }\n\n  /* exported listenForDropdownMenuTabs,\r\n              refreshTargetElementInstance,\r\n              scrollToActiveTab */\n  function checkForTabAdded(refreshData) {\n    var updatedTabsArray = refreshData.updatedTabsArray,\n      updatedTabsLiContent = refreshData.updatedTabsLiContent || [],\n      updatedTabsPostProcessors = refreshData.updatedTabsPostProcessors || [],\n      propNames = refreshData.propNames,\n      ignoreTabPanes = refreshData.ignoreTabPanes,\n      options = refreshData.options,\n      $currTabLis = refreshData.$currTabLis,\n      $navTabs = refreshData.$navTabs,\n      $currTabContentPanesContainer = ignoreTabPanes ? null : refreshData.$currTabContentPanesContainer,\n      $currTabContentPanes = ignoreTabPanes ? null : refreshData.$currTabContentPanes,\n      isInitTabsRequired = false;\n\n    // make sure each tab in the updated tabs array has a corresponding DOM element\n    updatedTabsArray.forEach(function (tab, idx) {\n      var $li = $currTabLis.find('a[href=\"#' + tab[propNames.paneId] + '\"]'),\n        isTabIdxPastCurrTabs = idx >= $currTabLis.length,\n        $pane;\n      if (!$li.length) {\n        // new tab\n        isInitTabsRequired = true;\n\n        // add the tab, add its pane (if necessary), and refresh the scroller\n        options.tabLiContent = updatedTabsLiContent[idx];\n        options.tabPostProcessor = updatedTabsPostProcessors[idx];\n        $li = tabElements.getNewElTabLi(tab, propNames, options);\n        tabUtils.storeDataOnLiEl($li, updatedTabsArray, idx);\n        if (isTabIdxPastCurrTabs) {\n          // append to end of current tabs\n          $li.appendTo($navTabs);\n        } else {\n          // insert in middle of current tabs\n          $li.insertBefore($currTabLis.eq(idx));\n        }\n        if (!ignoreTabPanes && tab[propNames.content] !== undefined) {\n          $pane = tabElements.getNewElTabPane(tab, propNames, options);\n          if (isTabIdxPastCurrTabs) {\n            // append to end of current tabs\n            $pane.appendTo($currTabContentPanesContainer);\n          } else {\n            // insert in middle of current tabs\n            $pane.insertBefore($currTabContentPanes.eq(idx));\n          }\n        }\n      }\n    });\n    return isInitTabsRequired;\n  }\n  function getTabAnchor($li) {\n    return $li.find('a[role=\"tab\"]');\n  }\n  function checkForTabPropertiesUpdated(refreshData) {\n    var tabLiData = refreshData.tabLi,\n      ignoreTabPanes = refreshData.ignoreTabPanes,\n      $li = tabLiData.$li,\n      $contentPane = tabLiData.$contentPane,\n      origTabData = tabLiData.origTabData,\n      newTabData = tabLiData.newTabData,\n      propNames = refreshData.propNames,\n      isInitTabsRequired = false;\n\n    // update tab title if necessary\n    if (origTabData[propNames.title] !== newTabData[propNames.title]) {\n      getTabAnchor($li).html(origTabData[propNames.title] = newTabData[propNames.title]);\n      isInitTabsRequired = true;\n    }\n\n    // update tab disabled state if necessary\n    if (origTabData[propNames.disabled] !== newTabData[propNames.disabled]) {\n      if (newTabData[propNames.disabled]) {\n        // enabled -> disabled\n        getTabAnchor($li).addClass('disabled').attr('data-toggle', '');\n      } else {\n        // disabled -> enabled\n        getTabAnchor($li).removeClass('disabled').attr('data-toggle', 'tab');\n      }\n      origTabData[propNames.disabled] = newTabData[propNames.disabled];\n      isInitTabsRequired = true;\n    }\n\n    // update tab active state if necessary\n    if (refreshData.options.forceActiveTab) {\n      // set the active tab based on the tabs array regardless of the current\n      // DOM state, which could have been changed by the user clicking a tab\n      // without those changes being reflected back to the tab data\n      getTabAnchor($li)[newTabData[propNames.active] ? 'addClass' : 'removeClass']('active');\n      $contentPane[newTabData[propNames.active] ? 'addClass' : 'removeClass']('active');\n      origTabData[propNames.active] = newTabData[propNames.active];\n      isInitTabsRequired = true;\n    }\n\n    // update tab content pane if necessary\n    if (!ignoreTabPanes && origTabData[propNames.content] !== newTabData[propNames.content]) {\n      $contentPane.html(origTabData[propNames.content] = newTabData[propNames.content]);\n      isInitTabsRequired = true;\n    }\n    return isInitTabsRequired;\n  }\n  function checkForTabRemoved(refreshData) {\n    var tabLiData = refreshData.tabLi,\n      ignoreTabPanes = refreshData.ignoreTabPanes,\n      $li = tabLiData.$li,\n      idxToMakeActive;\n    if (tabLiData.newIdx !== -1) {\n      // tab was not removed--it has a valid index\n      return false;\n    }\n\n    // if this was the active tab, make the closest enabled tab active\n    if (getTabAnchor($li).hasClass('active')) {\n      idxToMakeActive = tabUtils.getIndexOfClosestEnabledTab(refreshData.$currTabLis, tabLiData.currDomIdx);\n      if (idxToMakeActive > -1) {\n        refreshData.$currTabLis.eq(idxToMakeActive).find('a[role=\"tab\"]').addClass('active');\n        if (!ignoreTabPanes) {\n          refreshData.$currTabContentPanes.eq(idxToMakeActive).find('a[role=\"tab\"]').addClass('active');\n        }\n      }\n    }\n    $li.remove();\n    if (!ignoreTabPanes) {\n      tabLiData.$contentPane.remove();\n    }\n    return true;\n  }\n  function checkForTabsOrderChanged(refreshData) {\n    var $currTabLis = refreshData.$currTabLis,\n      updatedTabsArray = refreshData.updatedTabsArray,\n      propNames = refreshData.propNames,\n      ignoreTabPanes = refreshData.ignoreTabPanes,\n      newTabsCollection = [],\n      newTabPanesCollection = ignoreTabPanes ? null : [];\n    if (!tabUtils.didTabOrderChange($currTabLis, updatedTabsArray, propNames)) {\n      return false;\n    }\n\n    // the tab order changed...\n    updatedTabsArray.forEach(function (t) {\n      var paneId = t[propNames.paneId];\n      newTabsCollection.push($currTabLis.find('a[role=\"tab\"][href=\"#' + paneId + '\"]').parent('li'));\n      if (!ignoreTabPanes) {\n        newTabPanesCollection.push($('#' + paneId));\n      }\n    });\n    refreshData.$navTabs.append(newTabsCollection);\n    if (!ignoreTabPanes) {\n      refreshData.$currTabContentPanesContainer.append(newTabPanesCollection);\n    }\n    return true;\n  }\n  function checkForTabsRemovedOrUpdated(refreshData) {\n    var $currTabLis = refreshData.$currTabLis,\n      updatedTabsArray = refreshData.updatedTabsArray,\n      propNames = refreshData.propNames,\n      isInitTabsRequired = false;\n    $currTabLis.each(function (currDomIdx) {\n      var $li = $(this),\n        origTabData = $li.data('tab'),\n        newIdx = tabUtils.getTabIndexByPaneId(updatedTabsArray, propNames.paneId, origTabData[propNames.paneId]),\n        newTabData = newIdx > -1 ? updatedTabsArray[newIdx] : null;\n      refreshData.tabLi = {\n        $li: $li,\n        currDomIdx: currDomIdx,\n        newIdx: newIdx,\n        $contentPane: tabElements.getElTabPaneForLi($li),\n        origTabData: origTabData,\n        newTabData: newTabData\n      };\n      if (checkForTabRemoved(refreshData)) {\n        isInitTabsRequired = true;\n        return; // continue to next $li in .each() since we removed this tab\n      }\n      if (checkForTabPropertiesUpdated(refreshData)) {\n        isInitTabsRequired = true;\n      }\n    });\n    return isInitTabsRequired;\n  }\n  function listenForDropdownMenuTabs($scroller, stc) {\n    var $ddMenu;\n\n    // for dropdown menus to show, we need to move them out of the\n    // scroller and append them to the body\n    $scroller.on(CONSTANTS.EVENTS.DROPDOWN_MENU_SHOW, handleDropdownShow).on(CONSTANTS.EVENTS.DROPDOWN_MENU_HIDE, handleDropdownHide);\n    function handleDropdownHide(e) {\n      // move the dropdown menu back into its tab\n      $(e.target).append($ddMenu.off(CONSTANTS.EVENTS.CLICK));\n    }\n    function handleDropdownShow(e) {\n      var $ddParentTabLi = $(e.target),\n        ddLiOffset = $ddParentTabLi.offset(),\n        $currActiveTab = $scroller.find('li.nav-item > a.active').parent(),\n        ddMenuRightX,\n        tabsContainerMaxX,\n        ddMenuTargetLeft;\n      $ddMenu = $ddParentTabLi.find('.dropdown-menu').attr('data-' + CONSTANTS.DATA_KEY_DDMENU_MODIFIED, true);\n\n      // if the dropdown's parent tab li isn't already active,\n      // we need to deactivate any active menu item in the dropdown\n      if ($currActiveTab[0] !== $ddParentTabLi[0]) {\n        $ddMenu.find('a.nav-link.active').removeClass('active');\n      }\n\n      // we need to do our own click handling because the built-in\n      // bootstrap handlers won't work since we moved the dropdown\n      // menu outside the tabs container\n      $ddMenu.on(CONSTANTS.EVENTS.CLICK, 'a[role=\"tab\"]', handleClickOnDropdownMenuItem);\n      $('body').append($ddMenu);\n\n      // make sure the menu doesn't go off the right side of the page\n      ddMenuRightX = $ddMenu.width() + ddLiOffset.left;\n      tabsContainerMaxX = $scroller.width() - (stc.$slideRightArrow.outerWidth() + 1);\n      ddMenuTargetLeft = ddLiOffset.left;\n      if (ddMenuRightX > tabsContainerMaxX) {\n        ddMenuTargetLeft -= ddMenuRightX - tabsContainerMaxX;\n      }\n      $ddMenu.css({\n        'display': 'block',\n        'top': ddLiOffset.top + $ddParentTabLi.outerHeight() - 2,\n        'left': ddMenuTargetLeft\n      });\n      function handleClickOnDropdownMenuItem(e) {\n        /* jshint validthis: true */\n        var $selectedMenuItemAnc = $(e.target),\n          $selectedMenuItemDropdownMenu = $selectedMenuItemAnc.parent('.dropdown-menu'),\n          targetPaneId = $selectedMenuItemAnc.attr('href');\n        if ($selectedMenuItemAnc.find('a').hasClass('active')) {\n          return;\n        }\n\n        // once we select a menu item from the dropdown, deactivate\n        // the current tab (unless it's our parent tab), deactivate\n        // any active dropdown menu item, make our parent tab active\n        // (if it's not already), and activate the selected menu item\n        $scroller.find('li > a.active').not($ddParentTabLi.find('> a.nav-link')).add($selectedMenuItemDropdownMenu.find('li > a.nav-link.active')).removeClass('active');\n        $ddParentTabLi.find('> a.nav-link').addClass('active');\n\n        // manually deactivate current active pane and activate our pane\n        $('.tab-content .tab-pane.active').removeClass('active');\n        $(targetPaneId).addClass('active show');\n      }\n    }\n  }\n  function refreshDataDrivenTabs($container, options) {\n    var instanceData = $container.data().scrtabs,\n      scroller = instanceData.scroller,\n      $navTabs = $container.find('.scrtabs-tab-container .nav-tabs'),\n      $currTabContentPanesContainer = $container.find('.tab-content'),\n      isInitTabsRequired = false,\n      refreshData = {\n        options: options,\n        updatedTabsArray: instanceData.tabs,\n        updatedTabsLiContent: instanceData.tabsLiContent,\n        updatedTabsPostProcessors: instanceData.tabsPostProcessors,\n        propNames: instanceData.propNames,\n        ignoreTabPanes: instanceData.ignoreTabPanes,\n        $navTabs: $navTabs,\n        $currTabLis: $navTabs.find('> li'),\n        $currTabContentPanesContainer: $currTabContentPanesContainer,\n        $currTabContentPanes: $currTabContentPanesContainer.find('.tab-pane')\n      };\n\n    // to preserve the tab positions if we're just adding or removing\n    // a tab, don't completely rebuild the tab structure, but check\n    // for differences between the new tabs array and the old\n    if (checkForTabAdded(refreshData)) {\n      isInitTabsRequired = true;\n    }\n    if (checkForTabsOrderChanged(refreshData)) {\n      isInitTabsRequired = true;\n    }\n    if (checkForTabsRemovedOrUpdated(refreshData)) {\n      isInitTabsRequired = true;\n    }\n    if (isInitTabsRequired) {\n      scroller.initTabs();\n    }\n    return isInitTabsRequired;\n  }\n  function refreshTargetElementInstance($container, options) {\n    if (!$container.data('scrtabs')) {\n      // target element doesn't have plugin on it\n      return;\n    }\n\n    // force a refresh if the tabs are static html or they're data-driven\n    // but the data didn't change so we didn't call initTabs()\n    if ($container.data('scrtabs').isWrapperOnly || !refreshDataDrivenTabs($container, options)) {\n      $('body').trigger(CONSTANTS.EVENTS.FORCE_REFRESH);\n    }\n  }\n  function _scrollToActiveTab() {\n    /* jshint validthis: true */\n    var $targetElInstance = $(this),\n      scrtabsData = $targetElInstance.data('scrtabs');\n    if (!scrtabsData) {\n      return;\n    }\n    scrtabsData.scroller.scrollToActiveTab();\n  }\n  var methods = {\n    destroy: function destroy() {\n      var $targetEls = this;\n      return $targetEls.each(destroyPlugin);\n    },\n    init: function init(options) {\n      var $targetEls = this,\n        targetElsLastIndex = $targetEls.length - 1,\n        settings = $.extend({}, $.fn.scrollingTabs.defaults, options || {});\n\n      // ---- tabs NOT data-driven -------------------------\n      if (!settings.tabs) {\n        // just wrap the selected .nav-tabs element(s) in the scroller\n        return $targetEls.each(function (index) {\n          var dataObj = {\n              isWrapperOnly: true\n            },\n            $targetEl = $(this).data({\n              scrtabs: dataObj\n            }),\n            readyCallback = index < targetElsLastIndex ? null : function () {\n              $targetEls.trigger(CONSTANTS.EVENTS.TABS_READY);\n            };\n          wrapNavTabsInstanceInScroller($targetEl, settings, readyCallback);\n        });\n      }\n\n      // ---- tabs data-driven -------------------------\n      return $targetEls.each(function (index) {\n        var $targetEl = $(this),\n          readyCallback = index < targetElsLastIndex ? null : function () {\n            $targetEls.trigger(CONSTANTS.EVENTS.TABS_READY);\n          };\n        buildNavTabsAndTabContentForTargetElementInstance($targetEl, settings, readyCallback);\n      });\n    },\n    refresh: function refresh(options) {\n      var $targetEls = this,\n        settings = $.extend({}, $.fn.scrollingTabs.defaults, options || {});\n      return $targetEls.each(function () {\n        refreshTargetElementInstance($(this), settings);\n      });\n    },\n    scrollToActiveTab: function scrollToActiveTab() {\n      return this.each(_scrollToActiveTab);\n    }\n  };\n  function destroyPlugin() {\n    /* jshint validthis: true */\n    var $targetElInstance = $(this),\n      scrtabsData = $targetElInstance.data('scrtabs'),\n      $tabsContainer;\n    if (!scrtabsData) {\n      return;\n    }\n    if (scrtabsData.enableSwipingElement === 'self') {\n      $targetElInstance.removeClass(CONSTANTS.CSS_CLASSES.ALLOW_SCROLLBAR);\n    } else if (scrtabsData.enableSwipingElement === 'parent') {\n      $targetElInstance.closest('.scrtabs-tab-container').parent().removeClass(CONSTANTS.CSS_CLASSES.ALLOW_SCROLLBAR);\n    }\n    scrtabsData.scroller.off(CONSTANTS.EVENTS.DROPDOWN_MENU_SHOW).off(CONSTANTS.EVENTS.DROPDOWN_MENU_HIDE);\n\n    // if there were any dropdown menus opened, remove the css we added to\n    // them so they would display correctly\n    scrtabsData.scroller.find('[data-' + CONSTANTS.DATA_KEY_DDMENU_MODIFIED + ']').css({\n      display: '',\n      left: '',\n      top: ''\n    }).off(CONSTANTS.EVENTS.CLICK).removeAttr('data-' + CONSTANTS.DATA_KEY_DDMENU_MODIFIED);\n    if (scrtabsData.scroller.hasTabClickHandler) {\n      $targetElInstance.find('a[data-toggle=\"tab\"]').off('.scrtabs');\n    }\n    if (scrtabsData.isWrapperOnly) {\n      // we just wrapped nav-tabs markup, so restore it\n      // $targetElInstance is the ul.nav-tabs\n      $tabsContainer = $targetElInstance.parents('.scrtabs-tab-container');\n      if ($tabsContainer.length) {\n        $tabsContainer.replaceWith($targetElInstance);\n      }\n    } else {\n      // we generated the tabs from data so destroy everything we created\n      if (scrtabsData.scroller && scrtabsData.scroller.initTabs) {\n        scrtabsData.scroller.initTabs = null;\n      }\n\n      // $targetElInstance is the container for the ul.nav-tabs we generated\n      $targetElInstance.find('.scrtabs-tab-container').add('.tab-content').remove();\n    }\n    $targetElInstance.removeData('scrtabs');\n    while (--$.fn.scrollingTabs.nextInstanceId >= 0) {\n      $(window).off(CONSTANTS.EVENTS.WINDOW_RESIZE + $.fn.scrollingTabs.nextInstanceId);\n    }\n    $('body').off(CONSTANTS.EVENTS.FORCE_REFRESH);\n  }\n  $.fn.scrollingTabs = function (methodOrOptions) {\n    if (methods[methodOrOptions]) {\n      return methods[methodOrOptions].apply(this, Array.prototype.slice.call(arguments, 1));\n    } else if (!methodOrOptions || _typeof(methodOrOptions) === 'object') {\n      return methods.init.apply(this, arguments);\n    } else {\n      $.error('Method ' + methodOrOptions + ' does not exist on $.scrollingTabs.');\n    }\n  };\n  $.fn.scrollingTabs.nextInstanceId = 0;\n  $.fn.scrollingTabs.defaults = {\n    tabs: null,\n    propPaneId: 'paneId',\n    propTitle: 'title',\n    propActive: 'active',\n    propDisabled: 'disabled',\n    propContent: 'content',\n    ignoreTabPanes: false,\n    scrollToTabEdge: false,\n    disableScrollArrowsOnFullyScrolled: false,\n    forceActiveTab: false,\n    reverseScroll: false,\n    widthMultiplier: 1,\n    tabClickHandler: null,\n    cssClassLeftArrow: '',\n    cssClassRightArrow: '',\n    leftArrowContent: '',\n    rightArrowContent: '',\n    tabsLiContent: null,\n    tabsPostProcessors: null,\n    enableSwiping: false,\n    enableRtlSupport: false,\n    handleDelayedScrollbar: false,\n    bootstrapVersion: 3\n  };\n})(jQuery, window);\n;\n(function ($) {\n  'use strict';\n\n  $(activate);\n  function activate() {\n    $('.nav-tabs').scrollingTabs({\n      enableSwiping: true,\n      scrollToTabEdge: true,\n      disableScrollArrowsOnFullyScrolled: true\n    }).on('ready.scrtabs', function () {\n      $('.tab-content').show();\n    });\n  }\n})(jQuery);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./assets/js/scrolling-tabs.js\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["/js/vendor"], () => (__webpack_exec__("./assets/js/scrolling-tabs.js")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);