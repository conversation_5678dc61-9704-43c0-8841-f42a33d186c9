/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunkvima_holding_frontend"] = self["webpackChunkvima_holding_frontend"] || []).push([["/js/app"],{

/***/ "./assets/js/app.js":
/*!**************************!*\
  !*** ./assets/js/app.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var swiper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swiper */ \"./node_modules/swiper/swiper.esm.js\");\n/* provided dependency */ var __webpack_provided_window_dot_$ = __webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\");\n/* provided dependency */ var __webpack_provided_window_dot_jQuery = __webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\");\n__webpack_provided_window_dot_$ = __webpack_provided_window_dot_jQuery = __webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\");\n__webpack_require__(/*! @popperjs/core */ \"./node_modules/@popperjs/core/lib/index.js\");\n__webpack_require__(/*! bootstrap */ \"./node_modules/bootstrap/dist/js/bootstrap.esm.js\");\n__webpack_require__(/*! jquery-bootstrap-scrolling-tabs */ \"./node_modules/jquery-bootstrap-scrolling-tabs/dist/jquery.scrolling-tabs.js\");\n\nswiper__WEBPACK_IMPORTED_MODULE_0__.Swiper.use([swiper__WEBPACK_IMPORTED_MODULE_0__.Navigation, swiper__WEBPACK_IMPORTED_MODULE_0__.Pagination, swiper__WEBPACK_IMPORTED_MODULE_0__.Scrollbar, swiper__WEBPACK_IMPORTED_MODULE_0__.EffectCoverflow, swiper__WEBPACK_IMPORTED_MODULE_0__.Autoplay]);\n\n// Example starter JavaScript for disabling form submissions if there are invalid fields\n(function () {\n  'use strict';\n\n  window.addEventListener('load', function () {\n    // Fetch all the forms we want to apply custom Bootstrap validation styles to\n    var forms = document.getElementsByClassName('needs-validation');\n    // Loop over them and prevent submission\n    var validation = Array.prototype.filter.call(forms, function (form) {\n      form.addEventListener('submit', function (event) {\n        if (form.checkValidity() === false) {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        form.classList.add('was-validated');\n      }, false);\n    });\n  }, false);\n  document.querySelector('#toggle').addEventListener('click', function () {\n    this.classList.toggle('active');\n    document.querySelector('#overlay').classList.toggle('open');\n    document.querySelector('body').classList.toggle('open-menu');\n  });\n  document.querySelector('.nav-button').addEventListener('click', function () {\n    /*  Toggle the CSS closed class which reduces the height of the UL thus\n          hiding all LI apart from the first */\n    this.parentNode.parentNode.classList.toggle('closed');\n  }, false);\n\n  // Swiper: Slider\n  new swiper__WEBPACK_IMPORTED_MODULE_0__.Swiper('.swiper-container', {\n    loop: true,\n    slidesPerView: 2,\n    paginationClickable: true,\n    navigation: {\n      nextEl: '.swiper-button-next',\n      prevEl: '.swiper-button-prev'\n    },\n    spaceBetween: 20,\n    autoplay: {\n      delay: 3000\n    },\n    breakpoints: {\n      1920: {\n        slidesPerView: 3,\n        spaceBetween: 30\n      },\n      1028: {\n        slidesPerView: 3,\n        spaceBetween: 30\n      },\n      572: {\n        slidesPerView: 2,\n        spaceBetween: 10\n      }\n    }\n  });\n})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./assets/js/app.js\n\n}");

/***/ }),

/***/ "./assets/sass/style.scss":
/*!********************************!*\
  !*** ./assets/sass/style.scss ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hc3NldHMvc2Fzcy9zdHlsZS5zY3NzIiwibWFwcGluZ3MiOiI7QUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZpbWEtaG9sZGluZy1mcm9udGVuZC8uL2Fzc2V0cy9zYXNzL3N0eWxlLnNjc3M/MWY4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbmV4cG9ydCB7fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./assets/sass/style.scss\n\n}");

/***/ }),

/***/ "./assets/src/scss/main.scss":
/*!***********************************!*\
  !*** ./assets/src/scss/main.scss ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n// extracted by mini-css-extract-plugin\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9hc3NldHMvc3JjL3Njc3MvbWFpbi5zY3NzIiwibWFwcGluZ3MiOiI7QUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZpbWEtaG9sZGluZy1mcm9udGVuZC8uL2Fzc2V0cy9zcmMvc2Nzcy9tYWluLnNjc3M/ZmI5MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbmV4cG9ydCB7fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./assets/src/scss/main.scss\n\n}");

/***/ }),

/***/ "./node_modules/dom7/dom7.esm.js":
/*!***************************************!*\
  !*** ./node_modules/dom7/dom7.esm.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $: () => (/* binding */ $),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   addClass: () => (/* binding */ addClass),\n/* harmony export */   animate: () => (/* binding */ animate),\n/* harmony export */   animationEnd: () => (/* binding */ animationEnd),\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendTo: () => (/* binding */ appendTo),\n/* harmony export */   attr: () => (/* binding */ attr),\n/* harmony export */   blur: () => (/* binding */ blur),\n/* harmony export */   change: () => (/* binding */ change),\n/* harmony export */   children: () => (/* binding */ children),\n/* harmony export */   click: () => (/* binding */ click),\n/* harmony export */   closest: () => (/* binding */ closest),\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   data: () => (/* binding */ data),\n/* harmony export */   dataset: () => (/* binding */ dataset),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   detach: () => (/* binding */ detach),\n/* harmony export */   each: () => (/* binding */ each),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   eq: () => (/* binding */ eq),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   focus: () => (/* binding */ focus),\n/* harmony export */   focusin: () => (/* binding */ focusin),\n/* harmony export */   focusout: () => (/* binding */ focusout),\n/* harmony export */   hasClass: () => (/* binding */ hasClass),\n/* harmony export */   height: () => (/* binding */ height),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   index: () => (/* binding */ index),\n/* harmony export */   insertAfter: () => (/* binding */ insertAfter),\n/* harmony export */   insertBefore: () => (/* binding */ insertBefore),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   keydown: () => (/* binding */ keydown),\n/* harmony export */   keypress: () => (/* binding */ keypress),\n/* harmony export */   keyup: () => (/* binding */ keyup),\n/* harmony export */   mousedown: () => (/* binding */ mousedown),\n/* harmony export */   mouseenter: () => (/* binding */ mouseenter),\n/* harmony export */   mouseleave: () => (/* binding */ mouseleave),\n/* harmony export */   mousemove: () => (/* binding */ mousemove),\n/* harmony export */   mouseout: () => (/* binding */ mouseout),\n/* harmony export */   mouseover: () => (/* binding */ mouseover),\n/* harmony export */   mouseup: () => (/* binding */ mouseup),\n/* harmony export */   next: () => (/* binding */ next),\n/* harmony export */   nextAll: () => (/* binding */ nextAll),\n/* harmony export */   off: () => (/* binding */ off),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   on: () => (/* binding */ on),\n/* harmony export */   once: () => (/* binding */ once),\n/* harmony export */   outerHeight: () => (/* binding */ outerHeight),\n/* harmony export */   outerWidth: () => (/* binding */ outerWidth),\n/* harmony export */   parent: () => (/* binding */ parent),\n/* harmony export */   parents: () => (/* binding */ parents),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependTo: () => (/* binding */ prependTo),\n/* harmony export */   prev: () => (/* binding */ prev),\n/* harmony export */   prevAll: () => (/* binding */ prevAll),\n/* harmony export */   prop: () => (/* binding */ prop),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   removeAttr: () => (/* binding */ removeAttr),\n/* harmony export */   removeClass: () => (/* binding */ removeClass),\n/* harmony export */   removeData: () => (/* binding */ removeData),\n/* harmony export */   resize: () => (/* binding */ resize),\n/* harmony export */   scroll: () => (/* binding */ scroll),\n/* harmony export */   scrollLeft: () => (/* binding */ scrollLeft),\n/* harmony export */   scrollTo: () => (/* binding */ scrollTo),\n/* harmony export */   scrollTop: () => (/* binding */ scrollTop),\n/* harmony export */   show: () => (/* binding */ show),\n/* harmony export */   siblings: () => (/* binding */ siblings),\n/* harmony export */   stop: () => (/* binding */ stop),\n/* harmony export */   styles: () => (/* binding */ styles),\n/* harmony export */   submit: () => (/* binding */ submit),\n/* harmony export */   text: () => (/* binding */ text),\n/* harmony export */   toggleClass: () => (/* binding */ toggleClass),\n/* harmony export */   touchend: () => (/* binding */ touchend),\n/* harmony export */   touchmove: () => (/* binding */ touchmove),\n/* harmony export */   touchstart: () => (/* binding */ touchstart),\n/* harmony export */   transform: () => (/* binding */ transform),\n/* harmony export */   transition: () => (/* binding */ transition),\n/* harmony export */   transitionEnd: () => (/* binding */ transitionEnd),\n/* harmony export */   transitionStart: () => (/* binding */ transitionStart),\n/* harmony export */   trigger: () => (/* binding */ trigger),\n/* harmony export */   val: () => (/* binding */ val),\n/* harmony export */   value: () => (/* binding */ value),\n/* harmony export */   width: () => (/* binding */ width)\n/* harmony export */ });\n/* harmony import */ var ssr_window__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ssr-window */ \"./node_modules/ssr-window/ssr-window.esm.js\");\n/**\n * Dom7 4.0.6\n * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n * https://framework7.io/docs/dom7.html\n *\n * Copyright 2023, Vladimir Kharlampidi\n *\n * Licensed under MIT\n *\n * Released on: February 2, 2023\n */\n\n\n/* eslint-disable no-proto */\nfunction makeReactive(obj) {\n  const proto = obj.__proto__;\n  Object.defineProperty(obj, '__proto__', {\n    get() {\n      return proto;\n    },\n\n    set(value) {\n      proto.__proto__ = value;\n    }\n\n  });\n}\n\nclass Dom7 extends Array {\n  constructor(items) {\n    if (typeof items === 'number') {\n      super(items);\n    } else {\n      super(...(items || []));\n      makeReactive(this);\n    }\n  }\n\n}\n\nfunction arrayFlat(arr = []) {\n  const res = [];\n  arr.forEach(el => {\n    if (Array.isArray(el)) {\n      res.push(...arrayFlat(el));\n    } else {\n      res.push(el);\n    }\n  });\n  return res;\n}\nfunction arrayFilter(arr, callback) {\n  return Array.prototype.filter.call(arr, callback);\n}\nfunction arrayUnique(arr) {\n  const uniqueArray = [];\n\n  for (let i = 0; i < arr.length; i += 1) {\n    if (uniqueArray.indexOf(arr[i]) === -1) uniqueArray.push(arr[i]);\n  }\n\n  return uniqueArray;\n}\nfunction toCamelCase(string) {\n  return string.toLowerCase().replace(/-(.)/g, (match, group) => group.toUpperCase());\n}\n\n// eslint-disable-next-line\n\nfunction qsa(selector, context) {\n  if (typeof selector !== 'string') {\n    return [selector];\n  }\n\n  const a = [];\n  const res = context.querySelectorAll(selector);\n\n  for (let i = 0; i < res.length; i += 1) {\n    a.push(res[i]);\n  }\n\n  return a;\n}\n\nfunction $(selector, context) {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  const document = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getDocument)();\n  let arr = [];\n\n  if (!context && selector instanceof Dom7) {\n    return selector;\n  }\n\n  if (!selector) {\n    return new Dom7(arr);\n  }\n\n  if (typeof selector === 'string') {\n    const html = selector.trim();\n\n    if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n      let toCreate = 'div';\n      if (html.indexOf('<li') === 0) toCreate = 'ul';\n      if (html.indexOf('<tr') === 0) toCreate = 'tbody';\n      if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) toCreate = 'tr';\n      if (html.indexOf('<tbody') === 0) toCreate = 'table';\n      if (html.indexOf('<option') === 0) toCreate = 'select';\n      const tempParent = document.createElement(toCreate);\n      tempParent.innerHTML = html;\n\n      for (let i = 0; i < tempParent.childNodes.length; i += 1) {\n        arr.push(tempParent.childNodes[i]);\n      }\n    } else {\n      arr = qsa(selector.trim(), context || document);\n    } // arr = qsa(selector, document);\n\n  } else if (selector.nodeType || selector === window || selector === document) {\n    arr.push(selector);\n  } else if (Array.isArray(selector)) {\n    if (selector instanceof Dom7) return selector;\n    arr = selector;\n  }\n\n  return new Dom7(arrayUnique(arr));\n}\n\n$.fn = Dom7.prototype;\n\n// eslint-disable-next-line\n\nfunction addClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  this.forEach(el => {\n    el.classList.add(...classNames);\n  });\n  return this;\n}\n\nfunction removeClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  this.forEach(el => {\n    el.classList.remove(...classNames);\n  });\n  return this;\n}\n\nfunction toggleClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  this.forEach(el => {\n    classNames.forEach(className => {\n      el.classList.toggle(className);\n    });\n  });\n}\n\nfunction hasClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  return arrayFilter(this, el => {\n    return classNames.filter(className => el.classList.contains(className)).length > 0;\n  }).length > 0;\n}\n\nfunction attr(attrs, value) {\n  if (arguments.length === 1 && typeof attrs === 'string') {\n    // Get attr\n    if (this[0]) return this[0].getAttribute(attrs);\n    return undefined;\n  } // Set attrs\n\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (arguments.length === 2) {\n      // String\n      this[i].setAttribute(attrs, value);\n    } else {\n      // Object\n      for (const attrName in attrs) {\n        this[i][attrName] = attrs[attrName];\n        this[i].setAttribute(attrName, attrs[attrName]);\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction removeAttr(attr) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].removeAttribute(attr);\n  }\n\n  return this;\n}\n\nfunction prop(props, value) {\n  if (arguments.length === 1 && typeof props === 'string') {\n    // Get prop\n    if (this[0]) return this[0][props];\n  } else {\n    // Set props\n    for (let i = 0; i < this.length; i += 1) {\n      if (arguments.length === 2) {\n        // String\n        this[i][props] = value;\n      } else {\n        // Object\n        for (const propName in props) {\n          this[i][propName] = props[propName];\n        }\n      }\n    }\n\n    return this;\n  }\n\n  return this;\n}\n\nfunction data(key, value) {\n  let el;\n\n  if (typeof value === 'undefined') {\n    el = this[0];\n    if (!el) return undefined; // Get value\n\n    if (el.dom7ElementDataStorage && key in el.dom7ElementDataStorage) {\n      return el.dom7ElementDataStorage[key];\n    }\n\n    const dataKey = el.getAttribute(`data-${key}`);\n\n    if (dataKey) {\n      return dataKey;\n    }\n\n    return undefined;\n  } // Set value\n\n\n  for (let i = 0; i < this.length; i += 1) {\n    el = this[i];\n    if (!el.dom7ElementDataStorage) el.dom7ElementDataStorage = {};\n    el.dom7ElementDataStorage[key] = value;\n  }\n\n  return this;\n}\n\nfunction removeData(key) {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (el.dom7ElementDataStorage && el.dom7ElementDataStorage[key]) {\n      el.dom7ElementDataStorage[key] = null;\n      delete el.dom7ElementDataStorage[key];\n    }\n  }\n}\n\nfunction dataset() {\n  const el = this[0];\n  if (!el) return undefined;\n  const dataset = {}; // eslint-disable-line\n\n  if (el.dataset) {\n    for (const dataKey in el.dataset) {\n      dataset[dataKey] = el.dataset[dataKey];\n    }\n  } else {\n    for (let i = 0; i < el.attributes.length; i += 1) {\n      const attr = el.attributes[i];\n\n      if (attr.name.indexOf('data-') >= 0) {\n        dataset[toCamelCase(attr.name.split('data-')[1])] = attr.value;\n      }\n    }\n  }\n\n  for (const key in dataset) {\n    if (dataset[key] === 'false') dataset[key] = false;else if (dataset[key] === 'true') dataset[key] = true;else if (parseFloat(dataset[key]) === dataset[key] * 1) dataset[key] *= 1;\n  }\n\n  return dataset;\n}\n\nfunction val(value) {\n  if (typeof value === 'undefined') {\n    // get value\n    const el = this[0];\n    if (!el) return undefined;\n\n    if (el.multiple && el.nodeName.toLowerCase() === 'select') {\n      const values = [];\n\n      for (let i = 0; i < el.selectedOptions.length; i += 1) {\n        values.push(el.selectedOptions[i].value);\n      }\n\n      return values;\n    }\n\n    return el.value;\n  } // set value\n\n\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (Array.isArray(value) && el.multiple && el.nodeName.toLowerCase() === 'select') {\n      for (let j = 0; j < el.options.length; j += 1) {\n        el.options[j].selected = value.indexOf(el.options[j].value) >= 0;\n      }\n    } else {\n      el.value = value;\n    }\n  }\n\n  return this;\n}\n\nfunction value(value) {\n  return this.val(value);\n}\n\nfunction transform(transform) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.transform = transform;\n  }\n\n  return this;\n}\n\nfunction transition(duration) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.transitionDuration = typeof duration !== 'string' ? `${duration}ms` : duration;\n  }\n\n  return this;\n}\n\nfunction on(...args) {\n  let [eventType, targetSelector, listener, capture] = args;\n\n  if (typeof args[1] === 'function') {\n    [eventType, listener, capture] = args;\n    targetSelector = undefined;\n  }\n\n  if (!capture) capture = false;\n\n  function handleLiveEvent(e) {\n    const target = e.target;\n    if (!target) return;\n    const eventData = e.target.dom7EventData || [];\n\n    if (eventData.indexOf(e) < 0) {\n      eventData.unshift(e);\n    }\n\n    if ($(target).is(targetSelector)) listener.apply(target, eventData);else {\n      const parents = $(target).parents(); // eslint-disable-line\n\n      for (let k = 0; k < parents.length; k += 1) {\n        if ($(parents[k]).is(targetSelector)) listener.apply(parents[k], eventData);\n      }\n    }\n  }\n\n  function handleEvent(e) {\n    const eventData = e && e.target ? e.target.dom7EventData || [] : [];\n\n    if (eventData.indexOf(e) < 0) {\n      eventData.unshift(e);\n    }\n\n    listener.apply(this, eventData);\n  }\n\n  const events = eventType.split(' ');\n  let j;\n\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (!targetSelector) {\n      for (j = 0; j < events.length; j += 1) {\n        const event = events[j];\n        if (!el.dom7Listeners) el.dom7Listeners = {};\n        if (!el.dom7Listeners[event]) el.dom7Listeners[event] = [];\n        el.dom7Listeners[event].push({\n          listener,\n          proxyListener: handleEvent\n        });\n        el.addEventListener(event, handleEvent, capture);\n      }\n    } else {\n      // Live events\n      for (j = 0; j < events.length; j += 1) {\n        const event = events[j];\n        if (!el.dom7LiveListeners) el.dom7LiveListeners = {};\n        if (!el.dom7LiveListeners[event]) el.dom7LiveListeners[event] = [];\n        el.dom7LiveListeners[event].push({\n          listener,\n          proxyListener: handleLiveEvent\n        });\n        el.addEventListener(event, handleLiveEvent, capture);\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction off(...args) {\n  let [eventType, targetSelector, listener, capture] = args;\n\n  if (typeof args[1] === 'function') {\n    [eventType, listener, capture] = args;\n    targetSelector = undefined;\n  }\n\n  if (!capture) capture = false;\n  const events = eventType.split(' ');\n\n  for (let i = 0; i < events.length; i += 1) {\n    const event = events[i];\n\n    for (let j = 0; j < this.length; j += 1) {\n      const el = this[j];\n      let handlers;\n\n      if (!targetSelector && el.dom7Listeners) {\n        handlers = el.dom7Listeners[event];\n      } else if (targetSelector && el.dom7LiveListeners) {\n        handlers = el.dom7LiveListeners[event];\n      }\n\n      if (handlers && handlers.length) {\n        for (let k = handlers.length - 1; k >= 0; k -= 1) {\n          const handler = handlers[k];\n\n          if (listener && handler.listener === listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          } else if (listener && handler.listener && handler.listener.dom7proxy && handler.listener.dom7proxy === listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          } else if (!listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          }\n        }\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction once(...args) {\n  const dom = this;\n  let [eventName, targetSelector, listener, capture] = args;\n\n  if (typeof args[1] === 'function') {\n    [eventName, listener, capture] = args;\n    targetSelector = undefined;\n  }\n\n  function onceHandler(...eventArgs) {\n    listener.apply(this, eventArgs);\n    dom.off(eventName, targetSelector, onceHandler, capture);\n\n    if (onceHandler.dom7proxy) {\n      delete onceHandler.dom7proxy;\n    }\n  }\n\n  onceHandler.dom7proxy = listener;\n  return dom.on(eventName, targetSelector, onceHandler, capture);\n}\n\nfunction trigger(...args) {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  const events = args[0].split(' ');\n  const eventData = args[1];\n\n  for (let i = 0; i < events.length; i += 1) {\n    const event = events[i];\n\n    for (let j = 0; j < this.length; j += 1) {\n      const el = this[j];\n\n      if (window.CustomEvent) {\n        const evt = new window.CustomEvent(event, {\n          detail: eventData,\n          bubbles: true,\n          cancelable: true\n        });\n        el.dom7EventData = args.filter((data, dataIndex) => dataIndex > 0);\n        el.dispatchEvent(evt);\n        el.dom7EventData = [];\n        delete el.dom7EventData;\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction transitionStart(callback) {\n  const dom = this;\n\n  function fireCallBack(e) {\n    if (e.target !== this) return;\n    callback.call(this, e);\n    dom.off('transitionstart', fireCallBack);\n  }\n\n  if (callback) {\n    dom.on('transitionstart', fireCallBack);\n  }\n\n  return this;\n}\n\nfunction transitionEnd(callback) {\n  const dom = this;\n\n  function fireCallBack(e) {\n    if (e.target !== this) return;\n    callback.call(this, e);\n    dom.off('transitionend', fireCallBack);\n  }\n\n  if (callback) {\n    dom.on('transitionend', fireCallBack);\n  }\n\n  return this;\n}\n\nfunction animationEnd(callback) {\n  const dom = this;\n\n  function fireCallBack(e) {\n    if (e.target !== this) return;\n    callback.call(this, e);\n    dom.off('animationend', fireCallBack);\n  }\n\n  if (callback) {\n    dom.on('animationend', fireCallBack);\n  }\n\n  return this;\n}\n\nfunction width() {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n\n  if (this[0] === window) {\n    return window.innerWidth;\n  }\n\n  if (this.length > 0) {\n    return parseFloat(this.css('width'));\n  }\n\n  return null;\n}\n\nfunction outerWidth(includeMargins) {\n  if (this.length > 0) {\n    if (includeMargins) {\n      const styles = this.styles();\n      return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n    }\n\n    return this[0].offsetWidth;\n  }\n\n  return null;\n}\n\nfunction height() {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n\n  if (this[0] === window) {\n    return window.innerHeight;\n  }\n\n  if (this.length > 0) {\n    return parseFloat(this.css('height'));\n  }\n\n  return null;\n}\n\nfunction outerHeight(includeMargins) {\n  if (this.length > 0) {\n    if (includeMargins) {\n      const styles = this.styles();\n      return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n    }\n\n    return this[0].offsetHeight;\n  }\n\n  return null;\n}\n\nfunction offset() {\n  if (this.length > 0) {\n    const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n    const document = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getDocument)();\n    const el = this[0];\n    const box = el.getBoundingClientRect();\n    const body = document.body;\n    const clientTop = el.clientTop || body.clientTop || 0;\n    const clientLeft = el.clientLeft || body.clientLeft || 0;\n    const scrollTop = el === window ? window.scrollY : el.scrollTop;\n    const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n    return {\n      top: box.top + scrollTop - clientTop,\n      left: box.left + scrollLeft - clientLeft\n    };\n  }\n\n  return null;\n}\n\nfunction hide() {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.display = 'none';\n  }\n\n  return this;\n}\n\nfunction show() {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (el.style.display === 'none') {\n      el.style.display = '';\n    }\n\n    if (window.getComputedStyle(el, null).getPropertyValue('display') === 'none') {\n      // Still not visible\n      el.style.display = 'block';\n    }\n  }\n\n  return this;\n}\n\nfunction styles() {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  if (this[0]) return window.getComputedStyle(this[0], null);\n  return {};\n}\n\nfunction css(props, value) {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  let i;\n\n  if (arguments.length === 1) {\n    if (typeof props === 'string') {\n      // .css('width')\n      if (this[0]) return window.getComputedStyle(this[0], null).getPropertyValue(props);\n    } else {\n      // .css({ width: '100px' })\n      for (i = 0; i < this.length; i += 1) {\n        for (const prop in props) {\n          this[i].style[prop] = props[prop];\n        }\n      }\n\n      return this;\n    }\n  }\n\n  if (arguments.length === 2 && typeof props === 'string') {\n    // .css('width', '100px')\n    for (i = 0; i < this.length; i += 1) {\n      this[i].style[props] = value;\n    }\n\n    return this;\n  }\n\n  return this;\n}\n\nfunction each(callback) {\n  if (!callback) return this;\n  this.forEach((el, index) => {\n    callback.apply(el, [el, index]);\n  });\n  return this;\n}\n\nfunction filter(callback) {\n  const result = arrayFilter(this, callback);\n  return $(result);\n}\n\nfunction html(html) {\n  if (typeof html === 'undefined') {\n    return this[0] ? this[0].innerHTML : null;\n  }\n\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].innerHTML = html;\n  }\n\n  return this;\n}\n\nfunction text(text) {\n  if (typeof text === 'undefined') {\n    return this[0] ? this[0].textContent.trim() : null;\n  }\n\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].textContent = text;\n  }\n\n  return this;\n}\n\nfunction is(selector) {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  const document = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getDocument)();\n  const el = this[0];\n  let compareWith;\n  let i;\n  if (!el || typeof selector === 'undefined') return false;\n\n  if (typeof selector === 'string') {\n    if (el.matches) return el.matches(selector);\n    if (el.webkitMatchesSelector) return el.webkitMatchesSelector(selector);\n    if (el.msMatchesSelector) return el.msMatchesSelector(selector);\n    compareWith = $(selector);\n\n    for (i = 0; i < compareWith.length; i += 1) {\n      if (compareWith[i] === el) return true;\n    }\n\n    return false;\n  }\n\n  if (selector === document) {\n    return el === document;\n  }\n\n  if (selector === window) {\n    return el === window;\n  }\n\n  if (selector.nodeType || selector instanceof Dom7) {\n    compareWith = selector.nodeType ? [selector] : selector;\n\n    for (i = 0; i < compareWith.length; i += 1) {\n      if (compareWith[i] === el) return true;\n    }\n\n    return false;\n  }\n\n  return false;\n}\n\nfunction index() {\n  let child = this[0];\n  let i;\n\n  if (child) {\n    i = 0; // eslint-disable-next-line\n\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n\n    return i;\n  }\n\n  return undefined;\n}\n\nfunction eq(index) {\n  if (typeof index === 'undefined') return this;\n  const length = this.length;\n\n  if (index > length - 1) {\n    return $([]);\n  }\n\n  if (index < 0) {\n    const returnIndex = length + index;\n    if (returnIndex < 0) return $([]);\n    return $([this[returnIndex]]);\n  }\n\n  return $([this[index]]);\n}\n\nfunction append(...els) {\n  let newChild;\n  const document = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getDocument)();\n\n  for (let k = 0; k < els.length; k += 1) {\n    newChild = els[k];\n\n    for (let i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = newChild;\n\n        while (tempDiv.firstChild) {\n          this[i].appendChild(tempDiv.firstChild);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (let j = 0; j < newChild.length; j += 1) {\n          this[i].appendChild(newChild[j]);\n        }\n      } else {\n        this[i].appendChild(newChild);\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction appendTo(parent) {\n  $(parent).append(this);\n  return this;\n}\n\nfunction prepend(newChild) {\n  const document = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getDocument)();\n  let i;\n  let j;\n\n  for (i = 0; i < this.length; i += 1) {\n    if (typeof newChild === 'string') {\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = newChild;\n\n      for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n        this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n      }\n    } else if (newChild instanceof Dom7) {\n      for (j = 0; j < newChild.length; j += 1) {\n        this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n      }\n    } else {\n      this[i].insertBefore(newChild, this[i].childNodes[0]);\n    }\n  }\n\n  return this;\n}\n\nfunction prependTo(parent) {\n  $(parent).prepend(this);\n  return this;\n}\n\nfunction insertBefore(selector) {\n  const before = $(selector);\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (before.length === 1) {\n      before[0].parentNode.insertBefore(this[i], before[0]);\n    } else if (before.length > 1) {\n      for (let j = 0; j < before.length; j += 1) {\n        before[j].parentNode.insertBefore(this[i].cloneNode(true), before[j]);\n      }\n    }\n  }\n}\n\nfunction insertAfter(selector) {\n  const after = $(selector);\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (after.length === 1) {\n      after[0].parentNode.insertBefore(this[i], after[0].nextSibling);\n    } else if (after.length > 1) {\n      for (let j = 0; j < after.length; j += 1) {\n        after[j].parentNode.insertBefore(this[i].cloneNode(true), after[j].nextSibling);\n      }\n    }\n  }\n}\n\nfunction next(selector) {\n  if (this.length > 0) {\n    if (selector) {\n      if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n        return $([this[0].nextElementSibling]);\n      }\n\n      return $([]);\n    }\n\n    if (this[0].nextElementSibling) return $([this[0].nextElementSibling]);\n    return $([]);\n  }\n\n  return $([]);\n}\n\nfunction nextAll(selector) {\n  const nextEls = [];\n  let el = this[0];\n  if (!el) return $([]);\n\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n\n    if (selector) {\n      if ($(next).is(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n\n    el = next;\n  }\n\n  return $(nextEls);\n}\n\nfunction prev(selector) {\n  if (this.length > 0) {\n    const el = this[0];\n\n    if (selector) {\n      if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n        return $([el.previousElementSibling]);\n      }\n\n      return $([]);\n    }\n\n    if (el.previousElementSibling) return $([el.previousElementSibling]);\n    return $([]);\n  }\n\n  return $([]);\n}\n\nfunction prevAll(selector) {\n  const prevEls = [];\n  let el = this[0];\n  if (!el) return $([]);\n\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n\n    if (selector) {\n      if ($(prev).is(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n\n    el = prev;\n  }\n\n  return $(prevEls);\n}\n\nfunction siblings(selector) {\n  return this.nextAll(selector).add(this.prevAll(selector));\n}\n\nfunction parent(selector) {\n  const parents = []; // eslint-disable-line\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i].parentNode !== null) {\n      if (selector) {\n        if ($(this[i].parentNode).is(selector)) parents.push(this[i].parentNode);\n      } else {\n        parents.push(this[i].parentNode);\n      }\n    }\n  }\n\n  return $(parents);\n}\n\nfunction parents(selector) {\n  const parents = []; // eslint-disable-line\n\n  for (let i = 0; i < this.length; i += 1) {\n    let parent = this[i].parentNode; // eslint-disable-line\n\n    while (parent) {\n      if (selector) {\n        if ($(parent).is(selector)) parents.push(parent);\n      } else {\n        parents.push(parent);\n      }\n\n      parent = parent.parentNode;\n    }\n  }\n\n  return $(parents);\n}\n\nfunction closest(selector) {\n  let closest = this; // eslint-disable-line\n\n  if (typeof selector === 'undefined') {\n    return $([]);\n  }\n\n  if (!closest.is(selector)) {\n    closest = closest.parents(selector).eq(0);\n  }\n\n  return closest;\n}\n\nfunction find(selector) {\n  const foundElements = [];\n\n  for (let i = 0; i < this.length; i += 1) {\n    const found = this[i].querySelectorAll(selector);\n\n    for (let j = 0; j < found.length; j += 1) {\n      foundElements.push(found[j]);\n    }\n  }\n\n  return $(foundElements);\n}\n\nfunction children(selector) {\n  const children = []; // eslint-disable-line\n\n  for (let i = 0; i < this.length; i += 1) {\n    const childNodes = this[i].children;\n\n    for (let j = 0; j < childNodes.length; j += 1) {\n      if (!selector || $(childNodes[j]).is(selector)) {\n        children.push(childNodes[j]);\n      }\n    }\n  }\n\n  return $(children);\n}\n\nfunction remove() {\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i].parentNode) this[i].parentNode.removeChild(this[i]);\n  }\n\n  return this;\n}\n\nfunction detach() {\n  return this.remove();\n}\n\nfunction add(...els) {\n  const dom = this;\n  let i;\n  let j;\n\n  for (i = 0; i < els.length; i += 1) {\n    const toAdd = $(els[i]);\n\n    for (j = 0; j < toAdd.length; j += 1) {\n      dom.push(toAdd[j]);\n    }\n  }\n\n  return dom;\n}\n\nfunction empty() {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (el.nodeType === 1) {\n      for (let j = 0; j < el.childNodes.length; j += 1) {\n        if (el.childNodes[j].parentNode) {\n          el.childNodes[j].parentNode.removeChild(el.childNodes[j]);\n        }\n      }\n\n      el.textContent = '';\n    }\n  }\n\n  return this;\n}\n\n// eslint-disable-next-line\n\nfunction scrollTo(...args) {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  let [left, top, duration, easing, callback] = args;\n\n  if (args.length === 4 && typeof easing === 'function') {\n    callback = easing;\n    [left, top, duration, callback, easing] = args;\n  }\n\n  if (typeof easing === 'undefined') easing = 'swing';\n  return this.each(function animate() {\n    const el = this;\n    let currentTop;\n    let currentLeft;\n    let maxTop;\n    let maxLeft;\n    let newTop;\n    let newLeft;\n    let scrollTop; // eslint-disable-line\n\n    let scrollLeft; // eslint-disable-line\n\n    let animateTop = top > 0 || top === 0;\n    let animateLeft = left > 0 || left === 0;\n\n    if (typeof easing === 'undefined') {\n      easing = 'swing';\n    }\n\n    if (animateTop) {\n      currentTop = el.scrollTop;\n\n      if (!duration) {\n        el.scrollTop = top;\n      }\n    }\n\n    if (animateLeft) {\n      currentLeft = el.scrollLeft;\n\n      if (!duration) {\n        el.scrollLeft = left;\n      }\n    }\n\n    if (!duration) return;\n\n    if (animateTop) {\n      maxTop = el.scrollHeight - el.offsetHeight;\n      newTop = Math.max(Math.min(top, maxTop), 0);\n    }\n\n    if (animateLeft) {\n      maxLeft = el.scrollWidth - el.offsetWidth;\n      newLeft = Math.max(Math.min(left, maxLeft), 0);\n    }\n\n    let startTime = null;\n    if (animateTop && newTop === currentTop) animateTop = false;\n    if (animateLeft && newLeft === currentLeft) animateLeft = false;\n\n    function render(time = new Date().getTime()) {\n      if (startTime === null) {\n        startTime = time;\n      }\n\n      const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n      const easeProgress = easing === 'linear' ? progress : 0.5 - Math.cos(progress * Math.PI) / 2;\n      let done;\n      if (animateTop) scrollTop = currentTop + easeProgress * (newTop - currentTop);\n      if (animateLeft) scrollLeft = currentLeft + easeProgress * (newLeft - currentLeft);\n\n      if (animateTop && newTop > currentTop && scrollTop >= newTop) {\n        el.scrollTop = newTop;\n        done = true;\n      }\n\n      if (animateTop && newTop < currentTop && scrollTop <= newTop) {\n        el.scrollTop = newTop;\n        done = true;\n      }\n\n      if (animateLeft && newLeft > currentLeft && scrollLeft >= newLeft) {\n        el.scrollLeft = newLeft;\n        done = true;\n      }\n\n      if (animateLeft && newLeft < currentLeft && scrollLeft <= newLeft) {\n        el.scrollLeft = newLeft;\n        done = true;\n      }\n\n      if (done) {\n        if (callback) callback();\n        return;\n      }\n\n      if (animateTop) el.scrollTop = scrollTop;\n      if (animateLeft) el.scrollLeft = scrollLeft;\n      window.requestAnimationFrame(render);\n    }\n\n    window.requestAnimationFrame(render);\n  });\n} // scrollTop(top, duration, easing, callback) {\n\n\nfunction scrollTop(...args) {\n  let [top, duration, easing, callback] = args;\n\n  if (args.length === 3 && typeof easing === 'function') {\n    [top, duration, callback, easing] = args;\n  }\n\n  const dom = this;\n\n  if (typeof top === 'undefined') {\n    if (dom.length > 0) return dom[0].scrollTop;\n    return null;\n  }\n\n  return dom.scrollTo(undefined, top, duration, easing, callback);\n}\n\nfunction scrollLeft(...args) {\n  let [left, duration, easing, callback] = args;\n\n  if (args.length === 3 && typeof easing === 'function') {\n    [left, duration, callback, easing] = args;\n  }\n\n  const dom = this;\n\n  if (typeof left === 'undefined') {\n    if (dom.length > 0) return dom[0].scrollLeft;\n    return null;\n  }\n\n  return dom.scrollTo(left, undefined, duration, easing, callback);\n}\n\n// eslint-disable-next-line\n\nfunction animate(initialProps, initialParams) {\n  const window = (0,ssr_window__WEBPACK_IMPORTED_MODULE_0__.getWindow)();\n  const els = this;\n  const a = {\n    props: Object.assign({}, initialProps),\n    params: Object.assign({\n      duration: 300,\n      easing: 'swing' // or 'linear'\n\n      /* Callbacks\n      begin(elements)\n      complete(elements)\n      progress(elements, complete, remaining, start, tweenValue)\n      */\n\n    }, initialParams),\n    elements: els,\n    animating: false,\n    que: [],\n\n    easingProgress(easing, progress) {\n      if (easing === 'swing') {\n        return 0.5 - Math.cos(progress * Math.PI) / 2;\n      }\n\n      if (typeof easing === 'function') {\n        return easing(progress);\n      }\n\n      return progress;\n    },\n\n    stop() {\n      if (a.frameId) {\n        window.cancelAnimationFrame(a.frameId);\n      }\n\n      a.animating = false;\n      a.elements.each(el => {\n        const element = el;\n        delete element.dom7AnimateInstance;\n      });\n      a.que = [];\n    },\n\n    done(complete) {\n      a.animating = false;\n      a.elements.each(el => {\n        const element = el;\n        delete element.dom7AnimateInstance;\n      });\n      if (complete) complete(els);\n\n      if (a.que.length > 0) {\n        const que = a.que.shift();\n        a.animate(que[0], que[1]);\n      }\n    },\n\n    animate(props, params) {\n      if (a.animating) {\n        a.que.push([props, params]);\n        return a;\n      }\n\n      const elements = []; // Define & Cache Initials & Units\n\n      a.elements.each((el, index) => {\n        let initialFullValue;\n        let initialValue;\n        let unit;\n        let finalValue;\n        let finalFullValue;\n        if (!el.dom7AnimateInstance) a.elements[index].dom7AnimateInstance = a;\n        elements[index] = {\n          container: el\n        };\n        Object.keys(props).forEach(prop => {\n          initialFullValue = window.getComputedStyle(el, null).getPropertyValue(prop).replace(',', '.');\n          initialValue = parseFloat(initialFullValue);\n          unit = initialFullValue.replace(initialValue, '');\n          finalValue = parseFloat(props[prop]);\n          finalFullValue = props[prop] + unit;\n          elements[index][prop] = {\n            initialFullValue,\n            initialValue,\n            unit,\n            finalValue,\n            finalFullValue,\n            currentValue: initialValue\n          };\n        });\n      });\n      let startTime = null;\n      let time;\n      let elementsDone = 0;\n      let propsDone = 0;\n      let done;\n      let began = false;\n      a.animating = true;\n\n      function render() {\n        time = new Date().getTime();\n        let progress;\n        let easeProgress; // let el;\n\n        if (!began) {\n          began = true;\n          if (params.begin) params.begin(els);\n        }\n\n        if (startTime === null) {\n          startTime = time;\n        }\n\n        if (params.progress) {\n          // eslint-disable-next-line\n          params.progress(els, Math.max(Math.min((time - startTime) / params.duration, 1), 0), startTime + params.duration - time < 0 ? 0 : startTime + params.duration - time, startTime);\n        }\n\n        elements.forEach(element => {\n          const el = element;\n          if (done || el.done) return;\n          Object.keys(props).forEach(prop => {\n            if (done || el.done) return;\n            progress = Math.max(Math.min((time - startTime) / params.duration, 1), 0);\n            easeProgress = a.easingProgress(params.easing, progress);\n            const {\n              initialValue,\n              finalValue,\n              unit\n            } = el[prop];\n            el[prop].currentValue = initialValue + easeProgress * (finalValue - initialValue);\n            const currentValue = el[prop].currentValue;\n\n            if (finalValue > initialValue && currentValue >= finalValue || finalValue < initialValue && currentValue <= finalValue) {\n              el.container.style[prop] = finalValue + unit;\n              propsDone += 1;\n\n              if (propsDone === Object.keys(props).length) {\n                el.done = true;\n                elementsDone += 1;\n              }\n\n              if (elementsDone === elements.length) {\n                done = true;\n              }\n            }\n\n            if (done) {\n              a.done(params.complete);\n              return;\n            }\n\n            el.container.style[prop] = currentValue + unit;\n          });\n        });\n        if (done) return; // Then call\n\n        a.frameId = window.requestAnimationFrame(render);\n      }\n\n      a.frameId = window.requestAnimationFrame(render);\n      return a;\n    }\n\n  };\n\n  if (a.elements.length === 0) {\n    return els;\n  }\n\n  let animateInstance;\n\n  for (let i = 0; i < a.elements.length; i += 1) {\n    if (a.elements[i].dom7AnimateInstance) {\n      animateInstance = a.elements[i].dom7AnimateInstance;\n    } else a.elements[i].dom7AnimateInstance = a;\n  }\n\n  if (!animateInstance) {\n    animateInstance = a;\n  }\n\n  if (initialProps === 'stop') {\n    animateInstance.stop();\n  } else {\n    animateInstance.animate(a.props, a.params);\n  }\n\n  return els;\n}\n\nfunction stop() {\n  const els = this;\n\n  for (let i = 0; i < els.length; i += 1) {\n    if (els[i].dom7AnimateInstance) {\n      els[i].dom7AnimateInstance.stop();\n    }\n  }\n}\n\nconst noTrigger = 'resize scroll'.split(' ');\n\nfunction shortcut(name) {\n  function eventHandler(...args) {\n    if (typeof args[0] === 'undefined') {\n      for (let i = 0; i < this.length; i += 1) {\n        if (noTrigger.indexOf(name) < 0) {\n          if (name in this[i]) this[i][name]();else {\n            $(this[i]).trigger(name);\n          }\n        }\n      }\n\n      return this;\n    }\n\n    return this.on(name, ...args);\n  }\n\n  return eventHandler;\n}\n\nconst click = shortcut('click');\nconst blur = shortcut('blur');\nconst focus = shortcut('focus');\nconst focusin = shortcut('focusin');\nconst focusout = shortcut('focusout');\nconst keyup = shortcut('keyup');\nconst keydown = shortcut('keydown');\nconst keypress = shortcut('keypress');\nconst submit = shortcut('submit');\nconst change = shortcut('change');\nconst mousedown = shortcut('mousedown');\nconst mousemove = shortcut('mousemove');\nconst mouseup = shortcut('mouseup');\nconst mouseenter = shortcut('mouseenter');\nconst mouseleave = shortcut('mouseleave');\nconst mouseout = shortcut('mouseout');\nconst mouseover = shortcut('mouseover');\nconst touchstart = shortcut('touchstart');\nconst touchend = shortcut('touchend');\nconst touchmove = shortcut('touchmove');\nconst resize = shortcut('resize');\nconst scroll = shortcut('scroll');\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ($);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/dom7/dom7.esm.js\n\n}");

/***/ }),

/***/ "./node_modules/jquery-bootstrap-scrolling-tabs/dist/jquery.scrolling-tabs.js":
/*!************************************************************************************!*\
  !*** ./node_modules/jquery-bootstrap-scrolling-tabs/dist/jquery.scrolling-tabs.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("{/* provided dependency */ var jQuery = __webpack_require__(/*! jquery */ \"./node_modules/jquery/dist/jquery.js\");\n/**\n * jquery-bootstrap-scrolling-tabs\n * @version v2.6.1\n * @link https://github.com/mikejacobson/jquery-bootstrap-scrolling-tabs\n * <AUTHOR> Jacobson <<EMAIL>>\n * @license MIT License, http://www.opensource.org/licenses/MIT\n */\n/**\n * jQuery plugin version of Angular directive angular-bootstrap-scrolling-tabs:\n * https://github.com/mikejacobson/angular-bootstrap-scrolling-tabs\n *\n * Usage:\n *\n *    Use case #1: HTML-defined tabs\n *    ------------------------------\n *    Demo: http://plnkr.co/edit/thyD0grCxIjyU4PoTt4x?p=preview\n *\n *      Sample HTML:\n *\n *           <!-- Nav tabs -->\n *           <ul class=\"nav nav-tabs\" role=\"tablist\">\n *             <li role=\"presentation\" class=\"active\"><a href=\"#tab1\" role=\"tab\" data-toggle=\"tab\">Tab Number 1</a></li>\n *             <li role=\"presentation\"><a href=\"#tab2\" role=\"tab\" data-toggle=\"tab\">Tab Number 2</a></li>\n *             <li role=\"presentation\"><a href=\"#tab3\" role=\"tab\" data-toggle=\"tab\">Tab Number 3</a></li>\n *             <li role=\"presentation\"><a href=\"#tab4\" role=\"tab\" data-toggle=\"tab\">Tab Number 4</a></li>\n *           </ul>\n *\n *           <!-- Tab panes -->\n *           <div class=\"tab-content\">\n *             <div role=\"tabpanel\" class=\"tab-pane active\" id=\"tab1\">Tab 1 content...</div>\n *             <div role=\"tabpanel\" class=\"tab-pane\" id=\"tab2\">Tab 2 content...</div>\n *             <div role=\"tabpanel\" class=\"tab-pane\" id=\"tab3\">Tab 3 content...</div>\n *             <div role=\"tabpanel\" class=\"tab-pane\" id=\"tab4\">Tab 4 content...</div>\n *           </div>\n *\n *\n *      JavaScript:\n *\n *            $('.nav-tabs').scrollingTabs();\n *\n *\n *    Use Case #2: Data-driven tabs\n *    -----------------------------\n *    Demo: http://plnkr.co/edit/MWBjLnTvJeetjU3NEimg?p=preview\n *\n *      Sample HTML:\n *\n *          <!-- build .nav-tabs and .tab-content in here -->\n *          <div id=\"tabs-inside-here\"></div>\n *\n *\n *      JavaScript:\n *\n *             $('#tabs-inside-here').scrollingTabs({\n *               tabs: tabs, // required\n *               propPaneId: 'paneId', // optional\n *               propTitle: 'title', // optional\n *               propActive: 'active', // optional\n *               propDisabled: 'disabled', // optional\n *               propContent: 'content', // optional\n *               ignoreTabPanes: false, // optional\n *               scrollToTabEdge: false, // optional\n *               disableScrollArrowsOnFullyScrolled: false, // optional\n *               reverseScroll: false // optional\n *             });\n *\n *      Settings/Options:\n *\n *        tabs:             tabs data array\n *        prop*:            name of your tab object's property name that\n *                          corresponds to that required tab property if\n *                          your property name is different than the\n *                          standard name (paneId, title, etc.)\n *        tabsLiContent:\n *                          optional string array used to define custom HTML\n *                          for each tab's <li> element. Each entry is an HTML\n *                          string defining the tab <li> element for the\n *                          corresponding tab in the tabs array.\n *                          The default for a tab is:\n *                          '<li role=\"presentation\" class=\"\"></li>'\n *                          So, for example, if you had 3 tabs and you needed\n *                          a custom 'tooltip' attribute on each one, your\n *                          tabsLiContent array might look like this:\n *                            [\n *                              '<li role=\"presentation\" tooltip=\"Custom TT 1\" class=\"custom-li\"></li>',\n *                              '<li role=\"presentation\" tooltip=\"Custom TT 2\" class=\"custom-li\"></li>',\n *                              '<li role=\"presentation\" tooltip=\"Custom TT 3\" class=\"custom-li\"></li>'\n *                            ]\n *                          This plunk demonstrates its usage (in conjunction\n *                          with tabsPostProcessors):\n *                          http://plnkr.co/edit/ugJLMk7lmDCuZQziQ0k0\n *        tabsPostProcessors:\n *                          optional array of functions, each one associated\n *                          with an entry in the tabs array. When a tab element\n *                          has been created, its associated post-processor\n *                          function will be called with two arguments: the\n *                          newly created $li and $a jQuery elements for that tab.\n *                          This allows you to, for example, attach a custom\n *                          event listener to each anchor tag.\n *                          This plunk demonstrates its usage (in conjunction\n *                          with tabsLiContent):\n *                          http://plnkr.co/edit/ugJLMk7lmDCuZQziQ0k0\n *        ignoreTabPanes:   relevant for data-driven tabs only--set to true if\n *                          you want the plugin to only touch the tabs\n *                          and to not generate the tab pane elements\n *                          that go in .tab-content. By default, the plugin\n *                          will generate the tab panes based on the content\n *                          property in your tab data, if a content property\n *                          is present.\n *        scrollToTabEdge:  set to true if you want to force full-width tabs\n *                          to display at the left scroll arrow. i.e., if the\n *                          scrolling stops with only half a tab showing,\n *                          it will snap the tab to its edge so the full tab\n *                          shows.\n *        disableScrollArrowsOnFullyScrolled:\n *                          set to true if you want the left scroll arrow to\n *                          disable when the tabs are scrolled fully left,\n *                          and the right scroll arrow to disable when the tabs\n *                          are scrolled fully right.\n *        reverseScroll:\n *                          set to true if you want the left scroll arrow to\n *                          slide the tabs left instead of right, and the right\n *                          scroll arrow to slide the tabs right.\n *        enableSwiping:\n *                          set to true if you want to enable horizontal swiping\n *                          for touch screens.\n *        widthMultiplier:\n *                          set to a value less than 1 if you want the tabs\n *                          container to be less than the full width of its\n *                          parent element. For example, set it to 0.5 if you\n *                          want the tabs container to be half the width of\n *                          its parent.\n *        tabClickHandler:\n *                          a callback function to execute any time a tab is clicked.\n *                          The function is simply passed as the event handler\n *                          to jQuery's .on(), so the function will receive\n *                          the jQuery event as an argument, and the 'this'\n *                          inside the function will be the clicked tab's anchor\n *                          element.\n *        cssClassLeftArrow, cssClassRightArrow:\n *                          custom values for the class attributes for the\n *                          left and right scroll arrows. The defaults are\n *                          'glyphicon glyphicon-chevron-left' and\n *                          'glyphicon glyphicon-chevron-right'.\n *                          Using different icons might require you to add\n *                          custom styling to the arrows to position the icons\n *                          correctly; the arrows can be targeted with these\n *                          selectors:\n *                          .scrtabs-tab-scroll-arrow\n *                          .scrtabs-tab-scroll-arrow-left\n *                          .scrtabs-tab-scroll-arrow-right\n *        leftArrowContent, rightArrowContent:\n *                          custom HTML string for the left and right scroll\n *                          arrows. This will override any custom cssClassLeftArrow\n *                          and cssClassRightArrow settings.\n *                          For example, if you wanted to use svg icons, you\n *                          could set them like so:\n *\n *                           leftArrowContent: [\n *                               '<div class=\"custom-arrow\">',\n *                               '  <svg class=\"icon icon-point-left\">',\n *                               '    <use xlink:href=\"#icon-point-left\"></use>',\n *                               '  </svg>',\n *                               '</div>'\n *                             ].join(''),\n *                             rightArrowContent: [\n *                               '<div class=\"custom-arrow\">',\n *                               '  <svg class=\"icon icon-point-right\">',\n *                               '    <use xlink:href=\"#icon-point-right\"></use>',\n *                               '  </svg>',\n *                               '</div>'\n *                             ].join('')\n *\n *                          You would then need to add some CSS to make them\n *                          work correctly if you don't give them the\n *                          default scrtabs-tab-scroll-arrow classes.\n *                          This plunk shows it working with svg icons:\n *                          http://plnkr.co/edit/2MdZCAnLyeU40shxaol3?p=preview\n *\n *                          When using this option, you can also mark a child\n *                          element within the arrow content as the click target\n *                          if you don't want the entire content to be\n *                          clickable. You do that my adding the CSS class\n *                          'scrtabs-click-target' to the element that should\n *                          be clickable, like so:\n *\n *                           leftArrowContent: [\n *                               '<div class=\"scrtabs-tab-scroll-arrow scrtabs-tab-scroll-arrow-left\">',\n *                               '  <button class=\"scrtabs-click-target\" type=\"button\">',\n *                               '    <i class=\"custom-chevron-left\"></i>',\n *                               '  </button>',\n *                               '</div>'\n *                             ].join(''),\n *                             rightArrowContent: [\n *                               '<div class=\"scrtabs-tab-scroll-arrow scrtabs-tab-scroll-arrow-right\">',\n *                               '  <button class=\"scrtabs-click-target\" type=\"button\">',\n *                               '    <i class=\"custom-chevron-right\"></i>',\n *                               '  </button>',\n *                               '</div>'\n *                             ].join('')\n *\n *        enableRtlSupport:\n *                          set to true if you want your site to support\n *                          right-to-left languages. If true, the plugin will\n *                          check the page's <html> tag for attribute dir=\"rtl\"\n *                          and will adjust its behavior accordingly.\n *        handleDelayedScrollbar:\n *                          set to true if you experience a situation where the\n *                          right scroll arrow wraps to the next line due to a\n *                          vertical scrollbar coming into existence on the page\n *                          after the plugin already calculated its width without\n *                          a scrollbar present. This would occur if, for example,\n *                          the bulk of the page's content loaded after a delay, \n *                          and only then did a vertical scrollbar become necessary.\n *                          It would also occur if a vertical scrollbar only appeared \n *                          on selection of a particular tab that had more content \n *                          than the default tab.\n *        bootstrapVersion:\n *                          set to 4 if you're using Boostrap 4. Default is 3.\n *                          Bootstrap 4 handles some things differently than 3\n *                          (e.g., the 'active' class gets applied to the tab's\n *                          'li > a' element rather than the 'li' itself).\n *\n *\n *      On tabs data change:\n *\n *            $('#tabs-inside-here').scrollingTabs('refresh');\n *\n *      On tabs data change, if you want the active tab to be set based on\n *      the updated tabs data (i.e., you want to override the current\n *      active tab setting selected by the user), for example, if you\n *      added a new tab and you want it to be the active tab:\n *\n *             $('#tabs-inside-here').scrollingTabs('refresh', {\n *               forceActiveTab: true\n *             });\n *\n *      Any options that can be passed into the plugin can be set on the\n *      plugin's 'defaults' object instead so you don't have to pass them in:\n *\n *             $.fn.scrollingTabs.defaults.tabs = tabs;\n *             $.fn.scrollingTabs.defaults.forceActiveTab = true;\n *             $.fn.scrollingTabs.defaults.scrollToTabEdge = true;\n *             $.fn.scrollingTabs.defaults.disableScrollArrowsOnFullyScrolled = true;\n *             $.fn.scrollingTabs.defaults.reverseScroll = true;\n *             $.fn.scrollingTabs.defaults.widthMultiplier = 0.5;\n *             $.fn.scrollingTabs.defaults.tabClickHandler = function () { };\n *\n *\n *    Methods\n *    -----------------------------\n *    - refresh\n *    On window resize, the tabs should refresh themselves, but to force a refresh:\n *\n *      $('.nav-tabs').scrollingTabs('refresh');\n *\n *    - scrollToActiveTab\n *    On window resize, the active tab will automatically be scrolled to\n *    if it ends up offscreen, but you can also programmatically force a\n *    scroll to the active tab any time (if, for example, you're\n *    programmatically setting the active tab) by calling the\n *    'scrollToActiveTab' method:\n *\n *    $('.nav-tabs').scrollingTabs('scrollToActiveTab');\n *\n *\n *    Events\n *    -----------------------------\n *    The plugin triggers event 'ready.scrtabs' when the tabs have\n *    been wrapped in the scroller and are ready for viewing:\n *\n *      $('.nav-tabs')\n *        .scrollingTabs()\n *        .on('ready.scrtabs', function() {\n *          // tabs ready, do my other stuff...\n *        });\n *\n *      $('#tabs-inside-here')\n *        .scrollingTabs({ tabs: tabs })\n *        .on('ready.scrtabs', function() {\n *          // tabs ready, do my other stuff...\n *        });\n *\n *\n *    Destroying\n *    -----------------------------\n *    To destroy:\n *\n *      $('.nav-tabs').scrollingTabs('destroy');\n *\n *      $('#tabs-inside-here').scrollingTabs('destroy');\n *\n *    If you were wrapping markup, the markup will be restored; if your tabs\n *    were data-driven, the tabs will be destroyed along with the plugin.\n *\n */\n\n;(function ($, window) {\n  'use strict';\n  /* jshint unused:false */\n\n  /* exported CONSTANTS */\n  var CONSTANTS = {\n    CONTINUOUS_SCROLLING_TIMEOUT_INTERVAL: 50, // timeout interval for repeatedly moving the tabs container\n                                                // by one increment while the mouse is held down--decrease to\n                                                // make mousedown continous scrolling faster\n    SCROLL_OFFSET_FRACTION: 6, // each click moves the container this fraction of the fixed container--decrease\n                               // to make the tabs scroll farther per click\n  \n    DATA_KEY_DDMENU_MODIFIED: 'scrtabsddmenumodified',\n    DATA_KEY_IS_MOUSEDOWN: 'scrtabsismousedown',\n    DATA_KEY_BOOTSTRAP_TAB: 'bs.tab',\n  \n    CSS_CLASSES: {\n      BOOTSTRAP4: 'scrtabs-bootstrap4',\n      RTL: 'scrtabs-rtl',\n      SCROLL_ARROW_CLICK_TARGET: 'scrtabs-click-target',\n      SCROLL_ARROW_DISABLE: 'scrtabs-disable',\n      SCROLL_ARROW_WITH_CLICK_TARGET: 'scrtabs-with-click-target'\n    },\n  \n    SLIDE_DIRECTION: {\n      LEFT: 1,\n      RIGHT: 2\n    },\n  \n    EVENTS: {\n      CLICK: 'click.scrtabs',\n      DROPDOWN_MENU_HIDE: 'hide.bs.dropdown.scrtabs',\n      DROPDOWN_MENU_SHOW: 'show.bs.dropdown.scrtabs',\n      FORCE_REFRESH: 'forcerefresh.scrtabs',\n      MOUSEDOWN: 'mousedown.scrtabs',\n      MOUSEUP: 'mouseup.scrtabs',\n      TABS_READY: 'ready.scrtabs',\n      TOUCH_END: 'touchend.scrtabs',\n      TOUCH_MOVE: 'touchmove.scrtabs',\n      TOUCH_START: 'touchstart.scrtabs',\n      WINDOW_RESIZE: 'resize.scrtabs'\n    }\n  };\n  \n  // smartresize from Paul Irish (debounced window resize)\n  (function (sr) {\n    var debounce = function (func, threshold, execAsap) {\n      var timeout;\n  \n      return function debounced() {\n        var obj = this, args = arguments;\n        function delayed() {\n          if (!execAsap) {\n            func.apply(obj, args);\n          }\n          timeout = null;\n        }\n  \n        if (timeout) {\n          clearTimeout(timeout);\n        } else if (execAsap) {\n          func.apply(obj, args);\n        }\n  \n        timeout = setTimeout(delayed, threshold || 100);\n      };\n    };\n    $.fn[sr] = function (fn, customEventName) {\n      var eventName = customEventName || CONSTANTS.EVENTS.WINDOW_RESIZE;\n      return fn ? this.bind(eventName, debounce(fn)) : this.trigger(sr);\n    };\n  \n  })('smartresizeScrtabs');\n  \n  /* ***********************************************************************************\n   * ElementsHandler - Class that each instance of ScrollingTabsControl will instantiate\n   * **********************************************************************************/\n  function ElementsHandler(scrollingTabsControl) {\n    var ehd = this;\n  \n    ehd.stc = scrollingTabsControl;\n  }\n  \n  // ElementsHandler prototype methods\n  (function (p) {\n      p.initElements = function (options) {\n        var ehd = this;\n  \n        ehd.setElementReferences(options);\n        ehd.setEventListeners(options);\n      };\n  \n      p.listenForTouchEvents = function () {\n        var ehd = this,\n            stc = ehd.stc,\n            smv = stc.scrollMovement,\n            ev = CONSTANTS.EVENTS;\n  \n        var touching = false;\n        var touchStartX;\n        var startingContainerLeftPos;\n        var newLeftPos;\n  \n        stc.$movableContainer\n          .on(ev.TOUCH_START, function (e) {\n            touching = true;\n            startingContainerLeftPos = stc.movableContainerLeftPos;\n            touchStartX = e.originalEvent.changedTouches[0].pageX;\n          })\n          .on(ev.TOUCH_END, function () {\n            touching = false;\n          })\n          .on(ev.TOUCH_MOVE, function (e) {\n            if (!touching) {\n              return;\n            }\n  \n            var touchPageX = e.originalEvent.changedTouches[0].pageX;\n            var diff = touchPageX - touchStartX;\n            if (stc.rtl) {\n              diff = -diff;\n            }\n            var minPos;\n  \n            newLeftPos = startingContainerLeftPos + diff;\n            if (newLeftPos > 0) {\n              newLeftPos = 0;\n            } else {\n              minPos = smv.getMinPos();\n              if (newLeftPos < minPos) {\n                newLeftPos = minPos;\n              }\n            }\n            stc.movableContainerLeftPos = newLeftPos;\n  \n            var leftOrRight = stc.rtl ? 'right' : 'left';\n            stc.$movableContainer.css(leftOrRight, smv.getMovableContainerCssLeftVal());\n            smv.refreshScrollArrowsDisabledState();\n          });\n      };\n  \n      p.refreshAllElementSizes = function () {\n        var ehd = this,\n            stc = ehd.stc,\n            smv = stc.scrollMovement,\n            scrollArrowsWereVisible = stc.scrollArrowsVisible,\n            actionsTaken = {\n              didScrollToActiveTab: false\n            },\n            isPerformingSlideAnim = false,\n            minPos;\n  \n        ehd.setElementWidths();\n        ehd.setScrollArrowVisibility();\n  \n        // this could have been a window resize or the removal of a\n        // dynamic tab, so make sure the movable container is positioned\n        // correctly because, if it is far to the left and we increased the\n        // window width, it's possible that the tabs will be too far left,\n        // beyond the min pos.\n        if (stc.scrollArrowsVisible) {\n          // make sure container not too far left\n          minPos = smv.getMinPos();\n  \n          isPerformingSlideAnim = smv.scrollToActiveTab({\n            isOnWindowResize: true\n          });\n  \n          if (!isPerformingSlideAnim) {\n            smv.refreshScrollArrowsDisabledState();\n  \n            if (stc.rtl) {\n              if (stc.movableContainerRightPos < minPos) {\n                smv.incrementMovableContainerLeft(minPos);\n              }\n            } else {\n              if (stc.movableContainerLeftPos < minPos) {\n                smv.incrementMovableContainerRight(minPos);\n              }\n            }\n          }\n  \n          actionsTaken.didScrollToActiveTab = true;\n  \n        } else if (scrollArrowsWereVisible) {\n          // scroll arrows went away after resize, so position movable container at 0\n          stc.movableContainerLeftPos = 0;\n          smv.slideMovableContainerToLeftPos();\n        }\n  \n        return actionsTaken;\n      };\n  \n      p.setElementReferences = function (settings) {\n        var ehd = this,\n            stc = ehd.stc,\n            $tabsContainer = stc.$tabsContainer,\n            $leftArrow,\n            $rightArrow,\n            $leftArrowClickTarget,\n            $rightArrowClickTarget;\n  \n        stc.isNavPills = false;\n  \n        if (stc.rtl) {\n          $tabsContainer.addClass(CONSTANTS.CSS_CLASSES.RTL);\n        }\n  \n        if (stc.usingBootstrap4) {\n          $tabsContainer.addClass(CONSTANTS.CSS_CLASSES.BOOTSTRAP4);\n        }\n  \n        stc.$fixedContainer = $tabsContainer.find('.scrtabs-tabs-fixed-container');\n        $leftArrow = stc.$fixedContainer.prev();\n        $rightArrow = stc.$fixedContainer.next();\n  \n        // if we have custom arrow content, we might have a click target defined\n        if (settings.leftArrowContent) {\n          $leftArrowClickTarget = $leftArrow.find('.' + CONSTANTS.CSS_CLASSES.SCROLL_ARROW_CLICK_TARGET);\n        }\n  \n        if (settings.rightArrowContent) {\n          $rightArrowClickTarget = $rightArrow.find('.' + CONSTANTS.CSS_CLASSES.SCROLL_ARROW_CLICK_TARGET);\n        }\n  \n        if ($leftArrowClickTarget && $leftArrowClickTarget.length) {\n          $leftArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_WITH_CLICK_TARGET);\n        } else {\n          $leftArrowClickTarget = $leftArrow;\n        }\n  \n        if ($rightArrowClickTarget && $rightArrowClickTarget.length) {\n          $rightArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_WITH_CLICK_TARGET);\n        } else {\n          $rightArrowClickTarget = $rightArrow;\n        }\n  \n        stc.$movableContainer = $tabsContainer.find('.scrtabs-tabs-movable-container');\n        stc.$tabsUl = $tabsContainer.find('.nav-tabs');\n  \n        // check for pills\n        if (!stc.$tabsUl.length) {\n          stc.$tabsUl = $tabsContainer.find('.nav-pills');\n  \n          if (stc.$tabsUl.length) {\n            stc.isNavPills = true;\n          }\n        }\n  \n        stc.$tabsLiCollection = stc.$tabsUl.find('> li');\n  \n        stc.$slideLeftArrow = stc.reverseScroll ? $leftArrow : $rightArrow;\n        stc.$slideLeftArrowClickTarget = stc.reverseScroll ? $leftArrowClickTarget : $rightArrowClickTarget;\n        stc.$slideRightArrow = stc.reverseScroll ? $rightArrow : $leftArrow;\n        stc.$slideRightArrowClickTarget = stc.reverseScroll ? $rightArrowClickTarget : $leftArrowClickTarget;\n        stc.$scrollArrows = stc.$slideLeftArrow.add(stc.$slideRightArrow);\n  \n        stc.$win = $(window);\n      };\n  \n      p.setElementWidths = function () {\n        var ehd = this,\n            stc = ehd.stc;\n  \n        stc.winWidth = stc.$win.width();\n        stc.scrollArrowsCombinedWidth = stc.$slideLeftArrow.outerWidth() + stc.$slideRightArrow.outerWidth();\n  \n        ehd.setFixedContainerWidth();\n        ehd.setMovableContainerWidth();\n      };\n  \n      p.setEventListeners = function (settings) {\n        var ehd = this,\n            stc = ehd.stc,\n            evh = stc.eventHandlers,\n            ev = CONSTANTS.EVENTS,\n            resizeEventName = ev.WINDOW_RESIZE + stc.instanceId;\n  \n        if (settings.enableSwiping) {\n          ehd.listenForTouchEvents();\n        }\n  \n        stc.$slideLeftArrowClickTarget\n          .off('.scrtabs')\n          .on(ev.MOUSEDOWN, function (e) { evh.handleMousedownOnSlideMovContainerLeftArrow.call(evh, e); })\n          .on(ev.MOUSEUP, function (e) { evh.handleMouseupOnSlideMovContainerLeftArrow.call(evh, e); })\n          .on(ev.CLICK, function (e) { evh.handleClickOnSlideMovContainerLeftArrow.call(evh, e); });\n  \n        stc.$slideRightArrowClickTarget\n          .off('.scrtabs')\n          .on(ev.MOUSEDOWN, function (e) { evh.handleMousedownOnSlideMovContainerRightArrow.call(evh, e); })\n          .on(ev.MOUSEUP, function (e) { evh.handleMouseupOnSlideMovContainerRightArrow.call(evh, e); })\n          .on(ev.CLICK, function (e) { evh.handleClickOnSlideMovContainerRightArrow.call(evh, e); });\n  \n        if (stc.tabClickHandler) {\n          stc.$tabsLiCollection\n            .find('a[data-toggle=\"tab\"]')\n            .off(ev.CLICK)\n            .on(ev.CLICK, stc.tabClickHandler);\n        }\n  \n        if (settings.handleDelayedScrollbar) {\n          ehd.listenForDelayedScrollbar();\n        }\n  \n        stc.$win\n          .off(resizeEventName)\n          .smartresizeScrtabs(function (e) { evh.handleWindowResize.call(evh, e); }, resizeEventName);\n  \n        $('body').on(CONSTANTS.EVENTS.FORCE_REFRESH, stc.elementsHandler.refreshAllElementSizes.bind(stc.elementsHandler));\n      };\n  \n      p.listenForDelayedScrollbar = function () {\n        var iframe = document.createElement('iframe');\n        iframe.id = \"scrtabs-scrollbar-resize-listener\";\n        iframe.style.cssText = 'height: 0; background-color: transparent; margin: 0; padding: 0; overflow: hidden; border-width: 0; position: absolute; width: 100%;';\n        iframe.onload = function() {\n          var timeout;\n  \n          function handleResize() {\n            try {\n              $(window).trigger('resize');\n              timeout = null;\n            } catch(e) {}\n          }\n  \n          iframe.contentWindow.addEventListener('resize', function() {\n            if (timeout) {\n              clearTimeout(timeout);\n            }\n  \n            timeout = setTimeout(handleResize, 100);\n          });\n        };\n        \n        document.body.appendChild(iframe);\n      };\n  \n      p.setFixedContainerWidth = function () {\n        var ehd = this,\n            stc = ehd.stc,\n            tabsContainerRect = stc.$tabsContainer.get(0).getBoundingClientRect();\n        /**\n         * <AUTHOR>         * It solves problem with rounding by jQuery.outerWidth\n         * If we have real width 100.5 px, jQuery.outerWidth returns us 101 px and we get layout's fail\n         */\n        stc.fixedContainerWidth = tabsContainerRect.width || (tabsContainerRect.right - tabsContainerRect.left);\n        stc.fixedContainerWidth = stc.fixedContainerWidth * stc.widthMultiplier;\n  \n        stc.$fixedContainer.width(stc.fixedContainerWidth);\n      };\n  \n      p.setFixedContainerWidthForHiddenScrollArrows = function () {\n        var ehd = this,\n            stc = ehd.stc;\n  \n        stc.$fixedContainer.width(stc.fixedContainerWidth);\n      };\n  \n      p.setFixedContainerWidthForVisibleScrollArrows = function () {\n        var ehd = this,\n            stc = ehd.stc;\n  \n        stc.$fixedContainer.width(stc.fixedContainerWidth - stc.scrollArrowsCombinedWidth);\n      };\n  \n      p.setMovableContainerWidth = function () {\n        var ehd = this,\n            stc = ehd.stc,\n            $tabLi = stc.$tabsUl.find('> li');\n  \n        stc.movableContainerWidth = 0;\n  \n        if ($tabLi.length) {\n  \n          $tabLi.each(function () {\n            var $li = $(this),\n                totalMargin = 0;\n  \n            if (stc.isNavPills) { // pills have a margin-left, tabs have no margin\n              totalMargin = parseInt($li.css('margin-left'), 10) + parseInt($li.css('margin-right'), 10);\n            }\n  \n            stc.movableContainerWidth += ($li.outerWidth() + totalMargin);\n          });\n  \n          stc.movableContainerWidth += 1;\n  \n          // if the tabs don't span the width of the page, force the\n          // movable container width to full page width so the bottom\n          // border spans the page width instead of just spanning the\n          // width of the tabs\n          if (stc.movableContainerWidth < stc.fixedContainerWidth) {\n            stc.movableContainerWidth = stc.fixedContainerWidth;\n          }\n        }\n  \n        stc.$movableContainer.width(stc.movableContainerWidth);\n      };\n  \n      p.setScrollArrowVisibility = function () {\n        var ehd = this,\n            stc = ehd.stc,\n            shouldBeVisible = stc.movableContainerWidth > stc.fixedContainerWidth;\n  \n        if (shouldBeVisible && !stc.scrollArrowsVisible) {\n          stc.$scrollArrows.show();\n          stc.scrollArrowsVisible = true;\n        } else if (!shouldBeVisible && stc.scrollArrowsVisible) {\n          stc.$scrollArrows.hide();\n          stc.scrollArrowsVisible = false;\n        }\n  \n        if (stc.scrollArrowsVisible) {\n          ehd.setFixedContainerWidthForVisibleScrollArrows();\n        } else {\n          ehd.setFixedContainerWidthForHiddenScrollArrows();\n        }\n      };\n  \n  }(ElementsHandler.prototype));\n  \n  /* ***********************************************************************************\n   * EventHandlers - Class that each instance of ScrollingTabsControl will instantiate\n   * **********************************************************************************/\n  function EventHandlers(scrollingTabsControl) {\n    var evh = this;\n  \n    evh.stc = scrollingTabsControl;\n  }\n  \n  // prototype methods\n  (function (p){\n    p.handleClickOnSlideMovContainerLeftArrow = function () {\n      var evh = this,\n          stc = evh.stc;\n  \n      stc.scrollMovement.incrementMovableContainerLeft();\n    };\n  \n    p.handleClickOnSlideMovContainerRightArrow = function () {\n      var evh = this,\n          stc = evh.stc;\n  \n      stc.scrollMovement.incrementMovableContainerRight();\n    };\n  \n    p.handleMousedownOnSlideMovContainerLeftArrow = function () {\n      var evh = this,\n          stc = evh.stc;\n  \n      stc.$slideLeftArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, true);\n      stc.scrollMovement.continueSlideMovableContainerLeft();\n    };\n  \n    p.handleMousedownOnSlideMovContainerRightArrow = function () {\n      var evh = this,\n          stc = evh.stc;\n  \n      stc.$slideRightArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, true);\n      stc.scrollMovement.continueSlideMovableContainerRight();\n    };\n  \n    p.handleMouseupOnSlideMovContainerLeftArrow = function () {\n      var evh = this,\n          stc = evh.stc;\n  \n      stc.$slideLeftArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, false);\n    };\n  \n    p.handleMouseupOnSlideMovContainerRightArrow = function () {\n      var evh = this,\n          stc = evh.stc;\n  \n      stc.$slideRightArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN, false);\n    };\n  \n    p.handleWindowResize = function () {\n      var evh = this,\n          stc = evh.stc,\n          newWinWidth = stc.$win.width();\n  \n      if (newWinWidth === stc.winWidth) {\n        return false;\n      }\n  \n      stc.winWidth = newWinWidth;\n      stc.elementsHandler.refreshAllElementSizes();\n    };\n  \n  }(EventHandlers.prototype));\n  \n  /* ***********************************************************************************\n   * ScrollMovement - Class that each instance of ScrollingTabsControl will instantiate\n   * **********************************************************************************/\n  function ScrollMovement(scrollingTabsControl) {\n    var smv = this;\n  \n    smv.stc = scrollingTabsControl;\n  }\n  \n  // prototype methods\n  (function (p) {\n  \n    p.continueSlideMovableContainerLeft = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      setTimeout(function() {\n        if (stc.movableContainerLeftPos <= smv.getMinPos()  ||\n            !stc.$slideLeftArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN)) {\n          return;\n        }\n  \n        if (!smv.incrementMovableContainerLeft()) { // haven't reached max left\n          smv.continueSlideMovableContainerLeft();\n        }\n      }, CONSTANTS.CONTINUOUS_SCROLLING_TIMEOUT_INTERVAL);\n    };\n  \n    p.continueSlideMovableContainerRight = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      setTimeout(function() {\n        if (stc.movableContainerLeftPos >= 0  ||\n            !stc.$slideRightArrowClickTarget.data(CONSTANTS.DATA_KEY_IS_MOUSEDOWN)) {\n          return;\n        }\n  \n        if (!smv.incrementMovableContainerRight()) { // haven't reached max right\n          smv.continueSlideMovableContainerRight();\n        }\n      }, CONSTANTS.CONTINUOUS_SCROLLING_TIMEOUT_INTERVAL);\n    };\n  \n    p.decrementMovableContainerLeftPos = function (minPos) {\n      var smv = this,\n          stc = smv.stc;\n  \n      stc.movableContainerLeftPos -= (stc.fixedContainerWidth / CONSTANTS.SCROLL_OFFSET_FRACTION);\n      if (stc.movableContainerLeftPos < minPos) {\n        stc.movableContainerLeftPos = minPos;\n      } else if (stc.scrollToTabEdge) {\n        smv.setMovableContainerLeftPosToTabEdge(CONSTANTS.SLIDE_DIRECTION.LEFT);\n  \n        if (stc.movableContainerLeftPos < minPos) {\n          stc.movableContainerLeftPos = minPos;\n        }\n      }\n    };\n  \n    p.disableSlideLeftArrow = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n  \n      stc.$slideLeftArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n  \n    p.disableSlideRightArrow = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n  \n      stc.$slideRightArrow.addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n  \n    p.enableSlideLeftArrow = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n  \n      stc.$slideLeftArrow.removeClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n  \n    p.enableSlideRightArrow = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n  \n      stc.$slideRightArrow.removeClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n    };\n  \n    p.getMinPos = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      return stc.scrollArrowsVisible ? (stc.fixedContainerWidth - stc.movableContainerWidth - stc.scrollArrowsCombinedWidth) : 0;\n    };\n  \n    p.getMovableContainerCssLeftVal = function () {\n      var smv = this,\n          stc = smv.stc;\n  \n      return (stc.movableContainerLeftPos === 0) ? '0' : stc.movableContainerLeftPos + 'px';\n    };\n  \n    p.incrementMovableContainerLeft = function () {\n      var smv = this,\n          stc = smv.stc,\n          minPos = smv.getMinPos();\n  \n      smv.decrementMovableContainerLeftPos(minPos);\n      smv.slideMovableContainerToLeftPos();\n      smv.enableSlideRightArrow();\n  \n      // return true if we're fully left, false otherwise\n      return (stc.movableContainerLeftPos === minPos);\n    };\n  \n    p.incrementMovableContainerRight = function (minPos) {\n      var smv = this,\n          stc = smv.stc;\n  \n      // if minPos passed in, the movable container was beyond the minPos\n      if (minPos) {\n        stc.movableContainerLeftPos = minPos;\n      } else {\n        stc.movableContainerLeftPos += (stc.fixedContainerWidth / CONSTANTS.SCROLL_OFFSET_FRACTION);\n  \n        if (stc.movableContainerLeftPos > 0) {\n          stc.movableContainerLeftPos = 0;\n        } else if (stc.scrollToTabEdge) {\n          smv.setMovableContainerLeftPosToTabEdge(CONSTANTS.SLIDE_DIRECTION.RIGHT);\n        }\n      }\n  \n      smv.slideMovableContainerToLeftPos();\n      smv.enableSlideLeftArrow();\n  \n      // return true if we're fully right, false otherwise\n      // left pos of 0 is the movable container's max position (farthest right)\n      return (stc.movableContainerLeftPos === 0);\n    };\n  \n    p.refreshScrollArrowsDisabledState = function() {\n      var smv = this,\n          stc = smv.stc;\n  \n      if (!stc.disableScrollArrowsOnFullyScrolled || !stc.scrollArrowsVisible) {\n        return;\n      }\n  \n      if (stc.movableContainerLeftPos >= 0) { // movable container fully right\n        smv.disableSlideRightArrow();\n        smv.enableSlideLeftArrow();\n        return;\n      }\n  \n      if (stc.movableContainerLeftPos <= smv.getMinPos()) { // fully left\n        smv.disableSlideLeftArrow();\n        smv.enableSlideRightArrow();\n        return;\n      }\n  \n      smv.enableSlideLeftArrow();\n      smv.enableSlideRightArrow();\n    };\n  \n    p.scrollToActiveTab = function () {\n      var smv = this,\n          stc = smv.stc,\n          $activeTab,\n          $activeTabAnchor,\n          activeTabLeftPos,\n          activeTabRightPos,\n          rightArrowLeftPos,\n          activeTabWidth,\n          leftPosOffset,\n          offsetToMiddle,\n          leftScrollArrowWidth,\n          rightScrollArrowWidth;\n  \n      if (!stc.scrollArrowsVisible) {\n        return;\n      }\n  \n      if (stc.usingBootstrap4) {\n        $activeTabAnchor = stc.$tabsUl.find('li > .nav-link.active');\n        if ($activeTabAnchor.length) {\n          $activeTab = $activeTabAnchor.parent();\n        }\n      } else {\n        $activeTab = stc.$tabsUl.find('li.active');\n      }\n  \n      if (!$activeTab || !$activeTab.length) {\n        return;\n      }\n  \n      rightScrollArrowWidth = stc.$slideRightArrow.outerWidth();\n      activeTabWidth = $activeTab.outerWidth();\n  \n      /**\n       * <AUTHOR>       * We need relative offset (depends on $fixedContainer), don't absolute\n       */\n      activeTabLeftPos = $activeTab.offset().left - stc.$fixedContainer.offset().left;\n      activeTabRightPos = activeTabLeftPos + activeTabWidth;\n  \n      rightArrowLeftPos = stc.fixedContainerWidth - rightScrollArrowWidth;\n  \n      if (stc.rtl) {\n        leftScrollArrowWidth = stc.$slideLeftArrow.outerWidth();\n  \n        if (activeTabLeftPos < 0) { // active tab off left side\n          stc.movableContainerLeftPos += activeTabLeftPos;\n          smv.slideMovableContainerToLeftPos();\n          return true;\n        } else { // active tab off right side\n          if (activeTabRightPos > rightArrowLeftPos) {\n            stc.movableContainerLeftPos += (activeTabRightPos - rightArrowLeftPos) + (2 * rightScrollArrowWidth);\n            smv.slideMovableContainerToLeftPos();\n            return true;\n          }\n        }\n      } else {\n        if (activeTabRightPos > rightArrowLeftPos) { // active tab off right side\n          leftPosOffset = activeTabRightPos - rightArrowLeftPos + rightScrollArrowWidth;\n          offsetToMiddle = stc.fixedContainerWidth / 2;\n          leftPosOffset += offsetToMiddle - (activeTabWidth / 2);\n          stc.movableContainerLeftPos -= leftPosOffset;\n          smv.slideMovableContainerToLeftPos();\n          return true;\n        } else {\n          leftScrollArrowWidth = stc.$slideLeftArrow.outerWidth();\n          if (activeTabLeftPos < 0) { // active tab off left side\n            offsetToMiddle = stc.fixedContainerWidth / 2;\n            stc.movableContainerLeftPos += (-activeTabLeftPos) + offsetToMiddle - (activeTabWidth / 2);\n            smv.slideMovableContainerToLeftPos();\n            return true;\n          }\n        }\n      }\n  \n      return false;\n    };\n  \n    p.setMovableContainerLeftPosToTabEdge = function (slideDirection) {\n      var smv = this,\n          stc = smv.stc,\n          offscreenWidth = -stc.movableContainerLeftPos,\n          totalTabWidth = 0;\n  \n        // make sure LeftPos is set so that a tab edge will be against the\n        // left scroll arrow so we won't have a partial, cut-off tab\n        stc.$tabsLiCollection.each(function () {\n          var tabWidth = $(this).width();\n  \n          totalTabWidth += tabWidth;\n  \n          if (totalTabWidth > offscreenWidth) {\n            stc.movableContainerLeftPos = (slideDirection === CONSTANTS.SLIDE_DIRECTION.RIGHT) ? -(totalTabWidth - tabWidth) : -totalTabWidth;\n            return false; // exit .each() loop\n          }\n  \n        });\n    };\n  \n    p.slideMovableContainerToLeftPos = function () {\n      var smv = this,\n          stc = smv.stc,\n          minPos = smv.getMinPos(),\n          leftOrRightVal;\n  \n      if (stc.movableContainerLeftPos > 0) {\n        stc.movableContainerLeftPos = 0;\n      } else if (stc.movableContainerLeftPos < minPos) {\n        stc.movableContainerLeftPos = minPos;\n      }\n  \n      stc.movableContainerLeftPos = stc.movableContainerLeftPos / 1;\n      leftOrRightVal = smv.getMovableContainerCssLeftVal();\n  \n      smv.performingSlideAnim = true;\n  \n      var targetPos = stc.rtl ? { right: leftOrRightVal } : { left: leftOrRightVal };\n  \n      stc.$movableContainer.stop().animate(targetPos, 'slow', function __slideAnimComplete() {\n        var newMinPos = smv.getMinPos();\n  \n        smv.performingSlideAnim = false;\n  \n        // if we slid past the min pos--which can happen if you resize the window\n        // quickly--move back into position\n        if (stc.movableContainerLeftPos < newMinPos) {\n          smv.decrementMovableContainerLeftPos(newMinPos);\n  \n          targetPos = stc.rtl ? { right: smv.getMovableContainerCssLeftVal() } : { left: smv.getMovableContainerCssLeftVal() };\n  \n          stc.$movableContainer.stop().animate(targetPos, 'fast', function() {\n            smv.refreshScrollArrowsDisabledState();\n          });\n        } else {\n          smv.refreshScrollArrowsDisabledState();\n        }\n      });\n    };\n  \n  }(ScrollMovement.prototype));\n  \n  /* **********************************************************************\n   * ScrollingTabsControl - Class that each directive will instantiate\n   * **********************************************************************/\n  function ScrollingTabsControl($tabsContainer) {\n    var stc = this;\n  \n    stc.$tabsContainer = $tabsContainer;\n    stc.instanceId = $.fn.scrollingTabs.nextInstanceId++;\n  \n    stc.movableContainerLeftPos = 0;\n    stc.scrollArrowsVisible = false;\n    stc.scrollToTabEdge = false;\n    stc.disableScrollArrowsOnFullyScrolled = false;\n    stc.reverseScroll = false;\n    stc.widthMultiplier = 1;\n  \n    stc.scrollMovement = new ScrollMovement(stc);\n    stc.eventHandlers = new EventHandlers(stc);\n    stc.elementsHandler = new ElementsHandler(stc);\n  }\n  \n  // prototype methods\n  (function (p) {\n    p.initTabs = function (options, $scroller, readyCallback, attachTabContentToDomCallback) {\n      var stc = this,\n          elementsHandler = stc.elementsHandler,\n          num;\n  \n      if (options.enableRtlSupport && $('html').attr('dir') === 'rtl') {\n        stc.rtl = true;\n      }\n  \n      if (options.scrollToTabEdge) {\n        stc.scrollToTabEdge = true;\n      }\n  \n      if (options.disableScrollArrowsOnFullyScrolled) {\n        stc.disableScrollArrowsOnFullyScrolled = true;\n      }\n  \n      if (options.reverseScroll) {\n        stc.reverseScroll = true;\n      }\n  \n      if (options.widthMultiplier !== 1) {\n        num = Number(options.widthMultiplier); // handle string value\n  \n        if (!isNaN(num)) {\n          stc.widthMultiplier = num;\n        }\n      }\n  \n      if (options.bootstrapVersion.toString().charAt(0) === '4') {\n        stc.usingBootstrap4 = true;\n      }\n  \n      setTimeout(initTabsAfterTimeout, 100);\n  \n      function initTabsAfterTimeout() {\n        var actionsTaken;\n  \n        // if we're just wrapping non-data-driven tabs, the user might\n        // have the .nav-tabs hidden to prevent the clunky flash of\n        // multi-line tabs on page refresh, so we need to make sure\n        // they're visible before trying to wrap them\n        $scroller.find('.nav-tabs').show();\n  \n        elementsHandler.initElements(options);\n        actionsTaken = elementsHandler.refreshAllElementSizes();\n  \n        $scroller.css('visibility', 'visible');\n  \n        if (attachTabContentToDomCallback) {\n          attachTabContentToDomCallback();\n        }\n  \n        if (readyCallback) {\n          readyCallback();\n        }\n      }\n    };\n  \n    p.scrollToActiveTab = function(options) {\n      var stc = this,\n          smv = stc.scrollMovement;\n  \n      smv.scrollToActiveTab(options);\n    };\n  }(ScrollingTabsControl.prototype));\n  \n\n  /* exported buildNavTabsAndTabContentForTargetElementInstance */\n  var tabElements = (function () {\n  \n    return {\n      getElTabPaneForLi: getElTabPaneForLi,\n      getNewElNavTabs: getNewElNavTabs,\n      getNewElScrollerElementWrappingNavTabsInstance: getNewElScrollerElementWrappingNavTabsInstance,\n      getNewElTabAnchor: getNewElTabAnchor,\n      getNewElTabContent: getNewElTabContent,\n      getNewElTabLi: getNewElTabLi,\n      getNewElTabPane: getNewElTabPane\n    };\n  \n    ///////////////////\n  \n    // ---- retrieve existing elements from the DOM ----------\n    function getElTabPaneForLi($li) {\n      return $($li.find('a').attr('href'));\n    }\n  \n  \n    // ---- create new elements ----------\n    function getNewElNavTabs() {\n      return $('<ul class=\"nav nav-tabs\" role=\"tablist\"></ul>');\n    }\n  \n    function getNewElScrollerElementWrappingNavTabsInstance($navTabsInstance, settings) {\n      var $tabsContainer = $('<div class=\"scrtabs-tab-container\"></div>'),\n          leftArrowContent = settings.leftArrowContent || '<div class=\"scrtabs-tab-scroll-arrow scrtabs-tab-scroll-arrow-left\"><span class=\"' + settings.cssClassLeftArrow + '\"></span></div>',\n          $leftArrow = $(leftArrowContent),\n          rightArrowContent = settings.rightArrowContent || '<div class=\"scrtabs-tab-scroll-arrow scrtabs-tab-scroll-arrow-right\"><span class=\"' + settings.cssClassRightArrow + '\"></span></div>',\n          $rightArrow = $(rightArrowContent),\n          $fixedContainer = $('<div class=\"scrtabs-tabs-fixed-container\"></div>'),\n          $movableContainer = $('<div class=\"scrtabs-tabs-movable-container\"></div>');\n  \n      if (settings.disableScrollArrowsOnFullyScrolled) {\n        $leftArrow.add($rightArrow).addClass(CONSTANTS.CSS_CLASSES.SCROLL_ARROW_DISABLE);\n      }\n  \n      return $tabsContainer\n                .append($leftArrow,\n                        $fixedContainer.append($movableContainer.append($navTabsInstance)),\n                        $rightArrow);\n    }\n  \n    function getNewElTabAnchor(tab, propNames) {\n      return $('<a role=\"tab\" data-toggle=\"tab\"></a>')\n              .attr('href', '#' + tab[propNames.paneId])\n              .html(tab[propNames.title]);\n    }\n  \n    function getNewElTabContent() {\n      return $('<div class=\"tab-content\"></div>');\n    }\n  \n    function getNewElTabLi(tab, propNames, options) {\n      var liContent = options.tabLiContent || '<li role=\"presentation\" class=\"\"></li>',\n          $li = $(liContent),\n          $a = getNewElTabAnchor(tab, propNames).appendTo($li);\n  \n      if (tab[propNames.disabled]) {\n        $li.addClass('disabled');\n        $a.attr('data-toggle', '');\n      } else if (options.forceActiveTab && tab[propNames.active]) {\n        $li.addClass('active');\n      }\n  \n      if (options.tabPostProcessor) {\n        options.tabPostProcessor($li, $a);\n      }\n  \n      return $li;\n    }\n  \n    function getNewElTabPane(tab, propNames, options) {\n      var $pane = $('<div role=\"tabpanel\" class=\"tab-pane\"></div>')\n                  .attr('id', tab[propNames.paneId])\n                  .html(tab[propNames.content]);\n  \n      if (options.forceActiveTab && tab[propNames.active]) {\n        $pane.addClass('active');\n      }\n  \n      return $pane;\n    }\n  \n  \n  }()); // tabElements\n  \n  var tabUtils = (function () {\n  \n    return {\n      didTabOrderChange: didTabOrderChange,\n      getIndexOfClosestEnabledTab: getIndexOfClosestEnabledTab,\n      getTabIndexByPaneId: getTabIndexByPaneId,\n      storeDataOnLiEl: storeDataOnLiEl\n    };\n  \n    ///////////////////\n  \n    function didTabOrderChange($currTabLis, updatedTabs, propNames) {\n      var isTabOrderChanged = false;\n  \n      $currTabLis.each(function (currDomIdx) {\n        var newIdx = getTabIndexByPaneId(updatedTabs, propNames.paneId, $(this).data('tab')[propNames.paneId]);\n  \n        if ((newIdx > -1) && (newIdx !== currDomIdx)) { // tab moved\n          isTabOrderChanged = true;\n          return false; // exit .each() loop\n        }\n      });\n  \n      return isTabOrderChanged;\n    }\n  \n    function getIndexOfClosestEnabledTab($currTabLis, startIndex) {\n      var lastIndex = $currTabLis.length - 1,\n          closestIdx = -1,\n          incrementFromStartIndex = 0,\n          testIdx = 0;\n  \n      // expand out from the current tab looking for an enabled tab;\n      // we prefer the tab after us over the tab before\n      while ((closestIdx === -1) && (testIdx >= 0)) {\n  \n        if ( (((testIdx = startIndex + (++incrementFromStartIndex)) <= lastIndex) &&\n              !$currTabLis.eq(testIdx).hasClass('disabled')) ||\n              (((testIdx = startIndex - incrementFromStartIndex) >= 0) &&\n               !$currTabLis.eq(testIdx).hasClass('disabled')) ) {\n  \n          closestIdx = testIdx;\n  \n        }\n      }\n  \n      return closestIdx;\n    }\n  \n    function getTabIndexByPaneId(tabs, paneIdPropName, paneId) {\n      var idx = -1;\n  \n      tabs.some(function (tab, i) {\n        if (tab[paneIdPropName] === paneId) {\n          idx = i;\n          return true; // exit loop\n        }\n      });\n  \n      return idx;\n    }\n  \n    function storeDataOnLiEl($li, tabs, index) {\n      $li.data({\n        tab: $.extend({}, tabs[index]), // store a clone so we can check for changes\n        index: index\n      });\n    }\n  \n  }()); // tabUtils\n  \n  function buildNavTabsAndTabContentForTargetElementInstance($targetElInstance, settings, readyCallback) {\n    var tabs = settings.tabs,\n        propNames = {\n          paneId: settings.propPaneId,\n          title: settings.propTitle,\n          active: settings.propActive,\n          disabled: settings.propDisabled,\n          content: settings.propContent\n        },\n        ignoreTabPanes = settings.ignoreTabPanes,\n        hasTabContent = tabs.length && tabs[0][propNames.content] !== undefined,\n        $navTabs = tabElements.getNewElNavTabs(),\n        $tabContent = tabElements.getNewElTabContent(),\n        $scroller,\n        attachTabContentToDomCallback = ignoreTabPanes ? null : function() {\n          $scroller.after($tabContent);\n        };\n  \n    if (!tabs.length) {\n      return;\n    }\n  \n    tabs.forEach(function(tab, index) {\n      var options = {\n        forceActiveTab: true,\n        tabLiContent: settings.tabsLiContent && settings.tabsLiContent[index],\n        tabPostProcessor: settings.tabsPostProcessors && settings.tabsPostProcessors[index]\n      };\n  \n      tabElements\n        .getNewElTabLi(tab, propNames, options)\n        .appendTo($navTabs);\n  \n      // build the tab panes if we weren't told to ignore them and there's\n      // tab content data available\n      if (!ignoreTabPanes && hasTabContent) {\n        tabElements\n          .getNewElTabPane(tab, propNames, options)\n          .appendTo($tabContent);\n      }\n    });\n  \n    $scroller = wrapNavTabsInstanceInScroller($navTabs,\n                                              settings,\n                                              readyCallback,\n                                              attachTabContentToDomCallback);\n  \n    $scroller.appendTo($targetElInstance);\n  \n    $targetElInstance.data({\n      scrtabs: {\n        tabs: tabs,\n        propNames: propNames,\n        ignoreTabPanes: ignoreTabPanes,\n        hasTabContent: hasTabContent,\n        tabsLiContent: settings.tabsLiContent,\n        tabsPostProcessors: settings.tabsPostProcessors,\n        scroller: $scroller\n      }\n    });\n  \n    // once the nav-tabs are wrapped in the scroller, attach each tab's\n    // data to it for reference later; we need to wait till they're\n    // wrapped in the scroller because we wrap a *clone* of the nav-tabs\n    // we built above, not the original nav-tabs\n    $scroller.find('.nav-tabs > li').each(function (index) {\n      tabUtils.storeDataOnLiEl($(this), tabs, index);\n    });\n  \n    return $targetElInstance;\n  }\n  \n  \n  function wrapNavTabsInstanceInScroller($navTabsInstance, settings, readyCallback, attachTabContentToDomCallback) {\n    // Remove tab data stored by Bootstrap in order to fix tabs that were already visited\n    $navTabsInstance\n      .find('a[data-toggle=\"tab\"]')\n      .removeData(CONSTANTS.DATA_KEY_BOOTSTRAP_TAB);\n    \n    var $scroller = tabElements.getNewElScrollerElementWrappingNavTabsInstance($navTabsInstance.clone(true), settings), // use clone because we replaceWith later\n        scrollingTabsControl = new ScrollingTabsControl($scroller),\n        navTabsInstanceData = $navTabsInstance.data('scrtabs');\n  \n    if (!navTabsInstanceData) {\n      $navTabsInstance.data('scrtabs', {\n        scroller: $scroller\n      });\n    } else {\n      navTabsInstanceData.scroller = $scroller;\n    }\n  \n    $navTabsInstance.replaceWith($scroller.css('visibility', 'hidden'));\n  \n    if (settings.tabClickHandler && (typeof settings.tabClickHandler === 'function')) {\n      $scroller.hasTabClickHandler = true;\n      scrollingTabsControl.tabClickHandler = settings.tabClickHandler;\n    }\n  \n    $scroller.initTabs = function () {\n      scrollingTabsControl.initTabs(settings,\n                                    $scroller,\n                                    readyCallback,\n                                    attachTabContentToDomCallback);\n    };\n  \n    $scroller.scrollToActiveTab = function() {\n      scrollingTabsControl.scrollToActiveTab(settings);\n    };\n  \n    $scroller.initTabs();\n  \n    listenForDropdownMenuTabs($scroller, scrollingTabsControl);\n  \n    return $scroller;\n  }\n  \n  /* exported listenForDropdownMenuTabs,\n              refreshTargetElementInstance,\n              scrollToActiveTab */\n  function checkForTabAdded(refreshData) {\n    var updatedTabsArray = refreshData.updatedTabsArray,\n        updatedTabsLiContent = refreshData.updatedTabsLiContent || [],\n        updatedTabsPostProcessors = refreshData.updatedTabsPostProcessors || [],\n        propNames = refreshData.propNames,\n        ignoreTabPanes = refreshData.ignoreTabPanes,\n        options = refreshData.options,\n        $currTabLis = refreshData.$currTabLis,\n        $navTabs = refreshData.$navTabs,\n        $currTabContentPanesContainer = ignoreTabPanes ? null : refreshData.$currTabContentPanesContainer,\n        $currTabContentPanes = ignoreTabPanes ? null : refreshData.$currTabContentPanes,\n        isInitTabsRequired = false;\n  \n    // make sure each tab in the updated tabs array has a corresponding DOM element\n    updatedTabsArray.forEach(function (tab, idx) {\n      var $li = $currTabLis.find('a[href=\"#' + tab[propNames.paneId] + '\"]'),\n          isTabIdxPastCurrTabs = (idx >= $currTabLis.length),\n          $pane;\n  \n      if (!$li.length) { // new tab\n        isInitTabsRequired = true;\n  \n        // add the tab, add its pane (if necessary), and refresh the scroller\n        options.tabLiContent = updatedTabsLiContent[idx];\n        options.tabPostProcessor = updatedTabsPostProcessors[idx];\n        $li = tabElements.getNewElTabLi(tab, propNames, options);\n        tabUtils.storeDataOnLiEl($li, updatedTabsArray, idx);\n  \n        if (isTabIdxPastCurrTabs) { // append to end of current tabs\n          $li.appendTo($navTabs);\n        } else {                        // insert in middle of current tabs\n          $li.insertBefore($currTabLis.eq(idx));\n        }\n  \n        if (!ignoreTabPanes && tab[propNames.content] !== undefined) {\n          $pane = tabElements.getNewElTabPane(tab, propNames, options);\n          if (isTabIdxPastCurrTabs) { // append to end of current tabs\n            $pane.appendTo($currTabContentPanesContainer);\n          } else {                        // insert in middle of current tabs\n            $pane.insertBefore($currTabContentPanes.eq(idx));\n          }\n        }\n  \n      }\n  \n    });\n  \n    return isInitTabsRequired;\n  }\n  \n  function checkForTabPropertiesUpdated(refreshData) {\n    var tabLiData = refreshData.tabLi,\n        ignoreTabPanes = refreshData.ignoreTabPanes,\n        $li = tabLiData.$li,\n        $contentPane = tabLiData.$contentPane,\n        origTabData = tabLiData.origTabData,\n        newTabData = tabLiData.newTabData,\n        propNames = refreshData.propNames,\n        isInitTabsRequired = false;\n  \n    // update tab title if necessary\n    if (origTabData[propNames.title] !== newTabData[propNames.title]) {\n      $li.find('a[role=\"tab\"]')\n          .html(origTabData[propNames.title] = newTabData[propNames.title]);\n  \n      isInitTabsRequired = true;\n    }\n  \n    // update tab disabled state if necessary\n    if (origTabData[propNames.disabled] !== newTabData[propNames.disabled]) {\n      if (newTabData[propNames.disabled]) { // enabled -> disabled\n        $li.addClass('disabled');\n        $li.find('a[role=\"tab\"]').attr('data-toggle', '');\n      } else { // disabled -> enabled\n        $li.removeClass('disabled');\n        $li.find('a[role=\"tab\"]').attr('data-toggle', 'tab');\n      }\n  \n      origTabData[propNames.disabled] = newTabData[propNames.disabled];\n      isInitTabsRequired = true;\n    }\n  \n    // update tab active state if necessary\n    if (refreshData.options.forceActiveTab) {\n      // set the active tab based on the tabs array regardless of the current\n      // DOM state, which could have been changed by the user clicking a tab\n      // without those changes being reflected back to the tab data\n      $li[newTabData[propNames.active] ? 'addClass' : 'removeClass']('active');\n  \n      $contentPane[newTabData[propNames.active] ? 'addClass' : 'removeClass']('active');\n  \n      origTabData[propNames.active] = newTabData[propNames.active];\n  \n      isInitTabsRequired = true;\n    }\n  \n    // update tab content pane if necessary\n    if (!ignoreTabPanes && origTabData[propNames.content] !== newTabData[propNames.content]) {\n      $contentPane.html(origTabData[propNames.content] = newTabData[propNames.content]);\n      isInitTabsRequired = true;\n    }\n  \n    return isInitTabsRequired;\n  }\n  \n  function checkForTabRemoved(refreshData) {\n    var tabLiData = refreshData.tabLi,\n        ignoreTabPanes = refreshData.ignoreTabPanes,\n        $li = tabLiData.$li,\n        idxToMakeActive;\n  \n    if (tabLiData.newIdx !== -1) { // tab was not removed--it has a valid index\n      return false;\n    }\n  \n    // if this was the active tab, make the closest enabled tab active\n    if ($li.hasClass('active')) {\n  \n      idxToMakeActive = tabUtils.getIndexOfClosestEnabledTab(refreshData.$currTabLis, tabLiData.currDomIdx);\n      if (idxToMakeActive > -1) {\n        refreshData.$currTabLis\n          .eq(idxToMakeActive)\n          .addClass('active');\n  \n        if (!ignoreTabPanes) {\n          refreshData.$currTabContentPanes\n            .eq(idxToMakeActive)\n            .addClass('active');\n        }\n      }\n    }\n  \n    $li.remove();\n  \n    if (!ignoreTabPanes) {\n      tabLiData.$contentPane.remove();\n    }\n  \n    return true;\n  }\n  \n  function checkForTabsOrderChanged(refreshData) {\n    var $currTabLis = refreshData.$currTabLis,\n        updatedTabsArray = refreshData.updatedTabsArray,\n        propNames = refreshData.propNames,\n        ignoreTabPanes = refreshData.ignoreTabPanes,\n        newTabsCollection = [],\n        newTabPanesCollection = ignoreTabPanes ? null : [];\n  \n    if (!tabUtils.didTabOrderChange($currTabLis, updatedTabsArray, propNames)) {\n      return false;\n    }\n  \n    // the tab order changed...\n    updatedTabsArray.forEach(function (t) {\n      var paneId = t[propNames.paneId];\n  \n      newTabsCollection.push(\n          $currTabLis\n            .find('a[role=\"tab\"][href=\"#' + paneId + '\"]')\n            .parent('li')\n          );\n  \n      if (!ignoreTabPanes) {\n        newTabPanesCollection.push($('#' + paneId));\n      }\n    });\n  \n    refreshData.$navTabs.append(newTabsCollection);\n  \n    if (!ignoreTabPanes) {\n      refreshData.$currTabContentPanesContainer.append(newTabPanesCollection);\n    }\n  \n    return true;\n  }\n  \n  function checkForTabsRemovedOrUpdated(refreshData) {\n    var $currTabLis = refreshData.$currTabLis,\n        updatedTabsArray = refreshData.updatedTabsArray,\n        propNames = refreshData.propNames,\n        isInitTabsRequired = false;\n  \n  \n    $currTabLis.each(function (currDomIdx) {\n      var $li = $(this),\n          origTabData = $li.data('tab'),\n          newIdx = tabUtils.getTabIndexByPaneId(updatedTabsArray, propNames.paneId, origTabData[propNames.paneId]),\n          newTabData = (newIdx > -1) ? updatedTabsArray[newIdx] : null;\n  \n      refreshData.tabLi = {\n        $li: $li,\n        currDomIdx: currDomIdx,\n        newIdx: newIdx,\n        $contentPane: tabElements.getElTabPaneForLi($li),\n        origTabData: origTabData,\n        newTabData: newTabData\n      };\n  \n      if (checkForTabRemoved(refreshData)) {\n        isInitTabsRequired = true;\n        return; // continue to next $li in .each() since we removed this tab\n      }\n  \n      if (checkForTabPropertiesUpdated(refreshData)) {\n        isInitTabsRequired = true;\n      }\n    });\n  \n    return isInitTabsRequired;\n  }\n  \n  function listenForDropdownMenuTabs($scroller, stc) {\n    var $ddMenu;\n  \n    // for dropdown menus to show, we need to move them out of the\n    // scroller and append them to the body\n    $scroller\n      .on(CONSTANTS.EVENTS.DROPDOWN_MENU_SHOW, handleDropdownShow)\n      .on(CONSTANTS.EVENTS.DROPDOWN_MENU_HIDE, handleDropdownHide);\n  \n    function handleDropdownHide(e) {\n      // move the dropdown menu back into its tab\n      $(e.target).append($ddMenu.off(CONSTANTS.EVENTS.CLICK));\n    }\n  \n    function handleDropdownShow(e) {\n      var $ddParentTabLi = $(e.target),\n          ddLiOffset = $ddParentTabLi.offset(),\n          $currActiveTab = $scroller.find('li[role=\"presentation\"].active'),\n          ddMenuRightX,\n          tabsContainerMaxX,\n          ddMenuTargetLeft;\n  \n      $ddMenu = $ddParentTabLi\n                  .find('.dropdown-menu')\n                  .attr('data-' + CONSTANTS.DATA_KEY_DDMENU_MODIFIED, true);\n  \n      // if the dropdown's parent tab li isn't already active,\n      // we need to deactivate any active menu item in the dropdown\n      if ($currActiveTab[0] !== $ddParentTabLi[0]) {\n        $ddMenu.find('li.active').removeClass('active');\n      }\n  \n      // we need to do our own click handling because the built-in\n      // bootstrap handlers won't work since we moved the dropdown\n      // menu outside the tabs container\n      $ddMenu.on(CONSTANTS.EVENTS.CLICK, 'a[role=\"tab\"]', handleClickOnDropdownMenuItem);\n  \n      $('body').append($ddMenu);\n  \n      // make sure the menu doesn't go off the right side of the page\n      ddMenuRightX = $ddMenu.width() + ddLiOffset.left;\n      tabsContainerMaxX = $scroller.width() - (stc.$slideRightArrow.outerWidth() + 1);\n      ddMenuTargetLeft = ddLiOffset.left;\n  \n      if (ddMenuRightX > tabsContainerMaxX) {\n        ddMenuTargetLeft -= (ddMenuRightX - tabsContainerMaxX);\n      }\n  \n      $ddMenu.css({\n        'display': 'block',\n        'top': ddLiOffset.top + $ddParentTabLi.outerHeight() - 2,\n        'left': ddMenuTargetLeft\n      });\n  \n      function handleClickOnDropdownMenuItem() {\n        /* jshint validthis: true */\n        var $selectedMenuItemAnc = $(this),\n            $selectedMenuItemLi = $selectedMenuItemAnc.parent('li'),\n            $selectedMenuItemDropdownMenu = $selectedMenuItemLi.parent('.dropdown-menu'),\n            targetPaneId = $selectedMenuItemAnc.attr('href');\n  \n        if ($selectedMenuItemLi.hasClass('active')) {\n          return;\n        }\n  \n        // once we select a menu item from the dropdown, deactivate\n        // the current tab (unless it's our parent tab), deactivate\n        // any active dropdown menu item, make our parent tab active\n        // (if it's not already), and activate the selected menu item\n        $scroller\n          .find('li.active')\n          .not($ddParentTabLi)\n          .add($selectedMenuItemDropdownMenu.find('li.active'))\n          .removeClass('active');\n  \n        $ddParentTabLi\n          .add($selectedMenuItemLi)\n          .addClass('active');\n  \n        // manually deactivate current active pane and activate our pane\n        $('.tab-content .tab-pane.active').removeClass('active');\n        $(targetPaneId).addClass('active');\n      }\n  \n    }\n  }\n  \n  function refreshDataDrivenTabs($container, options) {\n    var instanceData = $container.data().scrtabs,\n        scroller = instanceData.scroller,\n        $navTabs = $container.find('.scrtabs-tab-container .nav-tabs'),\n        $currTabContentPanesContainer = $container.find('.tab-content'),\n        isInitTabsRequired = false,\n        refreshData = {\n          options: options,\n          updatedTabsArray: instanceData.tabs,\n          updatedTabsLiContent: instanceData.tabsLiContent,\n          updatedTabsPostProcessors: instanceData.tabsPostProcessors,\n          propNames: instanceData.propNames,\n          ignoreTabPanes: instanceData.ignoreTabPanes,\n          $navTabs: $navTabs,\n          $currTabLis: $navTabs.find('> li'),\n          $currTabContentPanesContainer: $currTabContentPanesContainer,\n          $currTabContentPanes: $currTabContentPanesContainer.find('.tab-pane')\n        };\n  \n    // to preserve the tab positions if we're just adding or removing\n    // a tab, don't completely rebuild the tab structure, but check\n    // for differences between the new tabs array and the old\n    if (checkForTabAdded(refreshData)) {\n      isInitTabsRequired = true;\n    }\n  \n    if (checkForTabsOrderChanged(refreshData)) {\n      isInitTabsRequired = true;\n    }\n  \n    if (checkForTabsRemovedOrUpdated(refreshData)) {\n      isInitTabsRequired = true;\n    }\n  \n    if (isInitTabsRequired) {\n      scroller.initTabs();\n    }\n  \n    return isInitTabsRequired;\n  }\n  \n  function refreshTargetElementInstance($container, options) {\n    if (!$container.data('scrtabs')) { // target element doesn't have plugin on it\n      return;\n    }\n  \n    // force a refresh if the tabs are static html or they're data-driven\n    // but the data didn't change so we didn't call initTabs()\n    if ($container.data('scrtabs').isWrapperOnly || !refreshDataDrivenTabs($container, options)) {\n      $('body').trigger(CONSTANTS.EVENTS.FORCE_REFRESH);\n    }\n  }\n  \n  function scrollToActiveTab() {\n    /* jshint validthis: true */\n    var $targetElInstance = $(this),\n        scrtabsData = $targetElInstance.data('scrtabs');\n  \n    if (!scrtabsData) {\n      return;\n    }\n  \n    scrtabsData.scroller.scrollToActiveTab();\n  }\n  \n  var methods = {\n    destroy: function() {\n      var $targetEls = this;\n  \n      return $targetEls.each(destroyPlugin);\n    },\n  \n    init: function(options) {\n      var $targetEls = this,\n          targetElsLastIndex = $targetEls.length - 1,\n          settings = $.extend({}, $.fn.scrollingTabs.defaults, options || {});\n  \n      // ---- tabs NOT data-driven -------------------------\n      if (!settings.tabs) {\n  \n        // just wrap the selected .nav-tabs element(s) in the scroller\n        return $targetEls.each(function(index) {\n          var dataObj = {\n                isWrapperOnly: true\n              },\n              $targetEl = $(this).data({ scrtabs: dataObj }),\n              readyCallback = (index < targetElsLastIndex) ? null : function() {\n                $targetEls.trigger(CONSTANTS.EVENTS.TABS_READY);\n              };\n  \n          wrapNavTabsInstanceInScroller($targetEl, settings, readyCallback);\n        });\n  \n      }\n  \n      // ---- tabs data-driven -------------------------\n      return $targetEls.each(function (index) {\n        var $targetEl = $(this),\n            readyCallback = (index < targetElsLastIndex) ? null : function() {\n              $targetEls.trigger(CONSTANTS.EVENTS.TABS_READY);\n            };\n  \n        buildNavTabsAndTabContentForTargetElementInstance($targetEl, settings, readyCallback);\n      });\n    },\n  \n    refresh: function(options) {\n      var $targetEls = this,\n          settings = $.extend({}, $.fn.scrollingTabs.defaults, options || {});\n  \n      return $targetEls.each(function () {\n        refreshTargetElementInstance($(this), settings);\n      });\n    },\n  \n    scrollToActiveTab: function() {\n      return this.each(scrollToActiveTab);\n    }\n  };\n  \n  function destroyPlugin() {\n    /* jshint validthis: true */\n    var $targetElInstance = $(this),\n        scrtabsData = $targetElInstance.data('scrtabs'),\n        $tabsContainer;\n  \n    if (!scrtabsData) {\n      return;\n    }\n  \n    if (scrtabsData.enableSwipingElement === 'self') {\n      $targetElInstance.removeClass(CONSTANTS.CSS_CLASSES.ALLOW_SCROLLBAR);\n    } else if (scrtabsData.enableSwipingElement === 'parent') {\n      $targetElInstance.closest('.scrtabs-tab-container').parent().removeClass(CONSTANTS.CSS_CLASSES.ALLOW_SCROLLBAR);\n    }\n  \n    scrtabsData.scroller\n      .off(CONSTANTS.EVENTS.DROPDOWN_MENU_SHOW)\n      .off(CONSTANTS.EVENTS.DROPDOWN_MENU_HIDE);\n  \n    // if there were any dropdown menus opened, remove the css we added to\n    // them so they would display correctly\n    scrtabsData.scroller\n      .find('[data-' + CONSTANTS.DATA_KEY_DDMENU_MODIFIED + ']')\n      .css({\n        display: '',\n        left: '',\n        top: ''\n      })\n      .off(CONSTANTS.EVENTS.CLICK)\n      .removeAttr('data-' + CONSTANTS.DATA_KEY_DDMENU_MODIFIED);\n  \n    if (scrtabsData.scroller.hasTabClickHandler) {\n      $targetElInstance\n        .find('a[data-toggle=\"tab\"]')\n        .off('.scrtabs');\n    }\n  \n    if (scrtabsData.isWrapperOnly) { // we just wrapped nav-tabs markup, so restore it\n      // $targetElInstance is the ul.nav-tabs\n      $tabsContainer = $targetElInstance.parents('.scrtabs-tab-container');\n  \n      if ($tabsContainer.length) {\n        $tabsContainer.replaceWith($targetElInstance);\n      }\n  \n    } else { // we generated the tabs from data so destroy everything we created\n      if (scrtabsData.scroller && scrtabsData.scroller.initTabs) {\n        scrtabsData.scroller.initTabs = null;\n      }\n  \n      // $targetElInstance is the container for the ul.nav-tabs we generated\n      $targetElInstance\n        .find('.scrtabs-tab-container')\n        .add('.tab-content')\n        .remove();\n    }\n  \n    $targetElInstance.removeData('scrtabs');\n  \n    while(--$.fn.scrollingTabs.nextInstanceId >= 0) {\n      $(window).off(CONSTANTS.EVENTS.WINDOW_RESIZE + $.fn.scrollingTabs.nextInstanceId);\n    }\n  \n    $('body').off(CONSTANTS.EVENTS.FORCE_REFRESH);\n  }\n  \n  \n  $.fn.scrollingTabs = function(methodOrOptions) {\n  \n    if (methods[methodOrOptions]) {\n      return methods[methodOrOptions].apply(this, Array.prototype.slice.call(arguments, 1));\n    } else if (!methodOrOptions || (typeof methodOrOptions === 'object')) {\n      return methods.init.apply(this, arguments);\n    } else {\n      $.error('Method ' + methodOrOptions + ' does not exist on $.scrollingTabs.');\n    }\n  };\n  \n  $.fn.scrollingTabs.nextInstanceId = 0;\n  \n  $.fn.scrollingTabs.defaults = {\n    tabs: null,\n    propPaneId: 'paneId',\n    propTitle: 'title',\n    propActive: 'active',\n    propDisabled: 'disabled',\n    propContent: 'content',\n    ignoreTabPanes: false,\n    scrollToTabEdge: false,\n    disableScrollArrowsOnFullyScrolled: false,\n    forceActiveTab: false,\n    reverseScroll: false,\n    widthMultiplier: 1,\n    tabClickHandler: null,\n    cssClassLeftArrow: 'glyphicon glyphicon-chevron-left',\n    cssClassRightArrow: 'glyphicon glyphicon-chevron-right',\n    leftArrowContent: '',\n    rightArrowContent: '',\n    tabsLiContent: null,\n    tabsPostProcessors: null,\n    enableSwiping: false,\n    enableRtlSupport: false,\n    handleDelayedScrollbar: false,\n    bootstrapVersion: 3\n  };\n  \n\n\n}(jQuery, window));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/jquery-bootstrap-scrolling-tabs/dist/jquery.scrolling-tabs.js\n\n}");

/***/ }),

/***/ "./node_modules/ssr-window/ssr-window.esm.js":
/*!***************************************************!*\
  !*** ./node_modules/ssr-window/ssr-window.esm.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extend: () => (/* binding */ extend),\n/* harmony export */   getDocument: () => (/* binding */ getDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   ssrDocument: () => (/* binding */ ssrDocument),\n/* harmony export */   ssrWindow: () => (/* binding */ ssrWindow)\n/* harmony export */ });\n/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, Vladimir Kharlampidi\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target = {}, src = {}) {\n    Object.keys(src).forEach((key) => {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nconst ssrDocument = {\n    body: {},\n    addEventListener() { },\n    removeEventListener() { },\n    activeElement: {\n        blur() { },\n        nodeName: '',\n    },\n    querySelector() {\n        return null;\n    },\n    querySelectorAll() {\n        return [];\n    },\n    getElementById() {\n        return null;\n    },\n    createEvent() {\n        return {\n            initEvent() { },\n        };\n    },\n    createElement() {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute() { },\n            getElementsByTagName() {\n                return [];\n            },\n        };\n    },\n    createElementNS() {\n        return {};\n    },\n    importNode() {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nfunction getDocument() {\n    const doc = typeof document !== 'undefined' ? document : {};\n    extend(doc, ssrDocument);\n    return doc;\n}\n\nconst ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState() { },\n        pushState() { },\n        go() { },\n        back() { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener() { },\n    removeEventListener() { },\n    getComputedStyle() {\n        return {\n            getPropertyValue() {\n                return '';\n            },\n        };\n    },\n    Image() { },\n    Date() { },\n    screen: {},\n    setTimeout() { },\n    clearTimeout() { },\n    matchMedia() {\n        return {};\n    },\n    requestAnimationFrame(callback) {\n        if (typeof setTimeout === 'undefined') {\n            callback();\n            return null;\n        }\n        return setTimeout(callback, 0);\n    },\n    cancelAnimationFrame(id) {\n        if (typeof setTimeout === 'undefined') {\n            return;\n        }\n        clearTimeout(id);\n    },\n};\nfunction getWindow() {\n    const win = typeof window !== 'undefined' ? window : {};\n    extend(win, ssrWindow);\n    return win;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/ssr-window/ssr-window.esm.js\n\n}");

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["css/style","css/style-legacy","/js/vendor"], () => (__webpack_exec__("./assets/js/app.js"), __webpack_exec__("./assets/sass/style.scss"), __webpack_exec__("./assets/src/scss/main.scss")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);