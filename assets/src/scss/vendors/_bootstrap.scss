// =============================================================================
// BOOTSTRAP OVERRIDES
// =============================================================================
// Import Bootstrap with simplified configuration

// For now, import the complete Bootstrap CSS to avoid variable conflicts
// We'll optimize this later once the basic architecture is working
@import '~bootstrap/scss/bootstrap';

// ==========================================================================
// BOOTSTRAP CUSTOMIZATIONS
// ==========================================================================

// Button customizations
.btn {
  font-weight: var(--font-weight-semibold);
  border-radius: var(--border-radius-md);
  transition: var(--transition-all);

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--color-primary-500-rgb), 0.25);
  }
}

// Form customizations
.form-control {
  border-radius: var(--border-radius-md);
  border-color: var(--color-gray-300);

  &:focus {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 0.2rem rgba(var(--color-primary-500-rgb), 0.25);
  }
}

// Card customizations
.card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-base);
  border-color: var(--color-gray-200);
}

// Navigation customizations
.navbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav-link {
  font-weight: var(--font-weight-medium);
  transition: var(--transition-colors);
}

// Modal customizations
.modal-content {
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
}

// Alert customizations
.alert {
  border-radius: var(--border-radius-lg);
  border: none;
}

// Badge customizations
.badge {
  border-radius: var(--border-radius-full);
  font-weight: var(--font-weight-semibold);
}
