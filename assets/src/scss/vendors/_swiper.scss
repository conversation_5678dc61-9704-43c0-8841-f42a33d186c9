// =============================================================================
// SWIPER OVERRIDES
// =============================================================================
// Import Swiper CSS and customize for VIMA design system

// Import Swiper CSS
@import '~swiper/swiper-bundle.css';

// ==========================================================================
// SWIPER CUSTOMIZATIONS
// ==========================================================================

.swiper {
  // Custom styling for VIMA sliders
  
  .swiper-slide {
    transition: var(--transition-transform);
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  // Navigation buttons
  .swiper-button-next,
  .swiper-button-prev {
    color: var(--color-primary-500);
    background: var(--color-white);
    border-radius: var(--border-radius-full);
    box-shadow: var(--shadow-lg);
    width: 44px;
    height: 44px;
    margin-top: -22px;
    transition: var(--transition-all);
    
    &:hover {
      background: var(--color-primary-500);
      color: var(--color-white);
      transform: scale(1.1);
    }
    
    &::after {
      font-size: 16px;
      font-weight: var(--font-weight-bold);
    }
    
    @include mobile-only {
      display: none;
    }
  }
  
  // Pagination
  .swiper-pagination {
    bottom: var(--spacing-4);
    
    .swiper-pagination-bullet {
      background: var(--color-gray-400);
      opacity: 1;
      transition: var(--transition-all);
      
      &.swiper-pagination-bullet-active {
        background: var(--color-primary-500);
        transform: scale(1.2);
      }
    }
  }
  
  // Scrollbar
  .swiper-scrollbar {
    background: var(--color-gray-200);
    border-radius: var(--border-radius-full);
    
    .swiper-scrollbar-drag {
      background: var(--color-primary-500);
      border-radius: var(--border-radius-full);
    }
  }
}

// ==========================================================================
// VIMA-SPECIFIC SLIDER STYLES
// ==========================================================================

.partners-slider {
  .swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
    
    img {
      max-width: 100%;
      max-height: 80px;
      object-fit: contain;
      filter: grayscale(100%);
      transition: var(--transition-all);
    }
    
    &:hover img {
      filter: grayscale(0%);
      transform: scale(1.05);
    }
  }
}

.hero-slider {
  .swiper-slide {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-xl);
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .slide-content {
      position: absolute;
      top: 50%;
      left: var(--spacing-8);
      transform: translateY(-50%);
      color: var(--color-white);
      z-index: 2;
      
      h2 {
        font-size: var(--font-size-4xl);
        font-weight: var(--font-weight-bold);
        margin-bottom: var(--spacing-4);
        
        @include mobile-only {
          font-size: var(--font-size-2xl);
        }
      }
      
      p {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-6);
        max-width: 500px;
        
        @include mobile-only {
          font-size: var(--font-size-base);
        }
      }
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        45deg,
        rgba(0, 0, 0, 0.6) 0%,
        rgba(0, 0, 0, 0.3) 50%,
        rgba(0, 0, 0, 0.1) 100%
      );
      z-index: 1;
    }
  }
}
