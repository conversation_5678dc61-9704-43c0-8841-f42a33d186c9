// =============================================================================
// SCROLLING TABS OVERRIDES
// =============================================================================
// Import and customize jquery-bootstrap-scrolling-tabs

// Import the original styles
@import '~jquery-bootstrap-scrolling-tabs/src/scss/jquery.scrolling-tabs';

// ==========================================================================
// SCROLLING TABS CUSTOMIZATIONS
// ==========================================================================

.scrtabs-tab-container {
  .nav-tabs {
    border-bottom: 2px solid var(--color-gray-200);
    
    .nav-link {
      border: none;
      border-bottom: 2px solid transparent;
      color: var(--color-gray-600);
      font-weight: var(--font-weight-medium);
      padding: var(--spacing-3) var(--spacing-4);
      transition: var(--transition-colors);
      
      &:hover {
        color: var(--color-primary-500);
        border-bottom-color: var(--color-primary-300);
      }
      
      &.active {
        color: var(--color-primary-500);
        border-bottom-color: var(--color-primary-500);
        background: none;
      }
    }
  }
  
  .scrtabs-tab-scroll-arrow {
    background: var(--color-white);
    border: 1px solid var(--color-gray-300);
    color: var(--color-gray-600);
    border-radius: var(--border-radius-base);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-all);
    
    &:hover {
      background: var(--color-primary-500);
      color: var(--color-white);
      border-color: var(--color-primary-500);
    }
    
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Mobile responsive adjustments
@include mobile-only {
  .scrtabs-tab-container {
    .nav-tabs {
      .nav-link {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-sm);
      }
    }
    
    .scrtabs-tab-scroll-arrow {
      width: 32px;
      height: 32px;
      
      &::before {
        font-size: 12px;
      }
    }
  }
}
