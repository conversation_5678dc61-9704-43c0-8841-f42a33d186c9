// =============================================================================
// GRID SYSTEM
// =============================================================================
// Enhanced grid system with CSS Grid and Flexbox

.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: calc(var(--spacing-3) * -1);
  margin-right: calc(var(--spacing-3) * -1);
}

.col {
  flex: 1 0 0%;
  padding-left: var(--spacing-3);
  padding-right: var(--spacing-3);
}

// Auto columns
@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    .col-#{$breakpoint}-auto {
      flex: 0 0 auto;
      width: auto;
    }
  }
}

// Sized columns
@for $i from 1 through 12 {
  @each $breakpoint in map-keys($breakpoints) {
    @include respond-to($breakpoint) {
      .col-#{$breakpoint}-#{$i} {
        flex: 0 0 auto;
        width: percentage($i / 12);
      }
    }
  }
}
