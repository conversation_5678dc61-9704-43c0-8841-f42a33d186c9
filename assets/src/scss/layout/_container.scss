// =============================================================================
// CONTAINER
// =============================================================================
// Responsive container system

.container {
  @include container;
}

.container-fluid {
  width: 100%;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
  
  @include respond-to(md) {
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
  
  @include respond-to(lg) {
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}
