// =============================================================================
// TYPOGRAPHY
// =============================================================================
// Base typography styles using design tokens

// ==========================================================================
// HEADINGS
// ==========================================================================

h1,
h2,
h3,
h4,
h5,
h6 {
  @extend %heading-base;
}

h1 {
  font-size: var(--font-size-5xl);
  
  @include mobile-only {
    font-size: var(--font-size-3xl);
  }
}

h2 {
  font-size: var(--font-size-4xl);
  
  @include mobile-only {
    font-size: var(--font-size-2xl);
  }
}

h3 {
  font-size: var(--font-size-3xl);
  
  @include mobile-only {
    font-size: var(--font-size-xl);
  }
}

h4 {
  font-size: var(--font-size-2xl);
  
  @include mobile-only {
    font-size: var(--font-size-lg);
  }
}

h5 {
  font-size: var(--font-size-xl);
}

h6 {
  font-size: var(--font-size-lg);
}

// ==========================================================================
// BODY TEXT
// ==========================================================================

p {
  @extend %body-text;
  margin-bottom: var(--spacing-4);
}

// ==========================================================================
// LINKS
// ==========================================================================

a {
  color: var(--color-primary-500);
  transition: var(--transition-colors);
  
  &:hover {
    color: var(--color-primary-700);
  }
}

// ==========================================================================
// LISTS
// ==========================================================================

ul,
ol {
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
}

// ==========================================================================
// EMPHASIS
// ==========================================================================

strong,
b {
  font-weight: var(--font-weight-bold);
}

em,
i {
  font-style: italic;
}

// ==========================================================================
// CODE
// ==========================================================================

code,
pre {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
}

code {
  background: var(--color-gray-100);
  padding: 0.125rem 0.25rem;
  border-radius: var(--border-radius-sm);
}

pre {
  background: var(--color-gray-100);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-base);
  overflow-x: auto;
  margin-bottom: var(--spacing-4);
}

// ==========================================================================
// BLOCKQUOTES
// ==========================================================================

blockquote {
  border-left: 4px solid var(--color-primary-500);
  padding-left: var(--spacing-4);
  margin: var(--spacing-6) 0;
  font-style: italic;
  color: var(--color-gray-600);
}
