// =============================================================================
// RESET & NORMALIZE
// =============================================================================
// Modern CSS reset with accessibility considerations

// ==========================================================================
// BOX SIZING
// ==========================================================================

*,
*::before,
*::after {
  box-sizing: border-box;
}

// ==========================================================================
// REMOVE DEFAULT MARGINS
// ==========================================================================

* {
  margin: 0;
}

// ==========================================================================
// HTML & BODY
// ==========================================================================

html {
  // Prevent adjustments of font size after orientation changes in iOS
  -webkit-text-size-adjust: 100%;
  // Enable smooth scrolling
  scroll-behavior: smooth;
  
  @include reduced-motion {
    scroll-behavior: auto;
  }
}

body {
  line-height: var(--line-height-normal);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  color: var(--color-gray-900);
  background-color: var(--color-white);
  @include font-smoothing;
  
  // Prevent horizontal scroll
  overflow-x: hidden;
}

// ==========================================================================
// TYPOGRAPHY ELEMENTS
// ==========================================================================

h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
  line-height: var(--line-height-tight);
}

p,
li,
figcaption {
  max-width: 65ch;
  overflow-wrap: break-word;
}

// ==========================================================================
// MEDIA ELEMENTS
// ==========================================================================

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

// ==========================================================================
// FORM ELEMENTS
// ==========================================================================

input,
button,
textarea,
select {
  font: inherit;
}

button {
  cursor: pointer;
}

// Remove default button styles
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: none;
  appearance: none;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

// ==========================================================================
// INTERACTIVE ELEMENTS
// ==========================================================================

// Remove default link styles
a {
  color: inherit;
  text-decoration: none;
}

// Focus styles
:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

// ==========================================================================
// ACCESSIBILITY
// ==========================================================================

// Respect user's motion preferences
@include reduced-motion {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// Screen reader only content
.sr-only {
  @include visually-hidden;
}

// Skip links
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary-500);
  color: var(--color-white);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-base);
  z-index: var(--z-index-tooltip);
  
  &:focus {
    top: 6px;
  }
}

// ==========================================================================
// UTILITY CLASSES
// ==========================================================================

// Remove list styles
.list-unstyled {
  list-style: none;
  padding: 0;
}

// Remove button styles
.btn-unstyled {
  @include button-reset;
}

// Text selection
::selection {
  background: rgba(var(--color-primary-500-rgb), 0.2);
  color: var(--color-gray-900);
}
