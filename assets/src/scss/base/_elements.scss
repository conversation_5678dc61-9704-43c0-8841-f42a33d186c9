// =============================================================================
// BASE ELEMENTS
// =============================================================================
// Base styling for HTML elements

// ==========================================================================
// TABLES
// ==========================================================================

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-4);
}

th,
td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200);
}

th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  background: var(--color-gray-50);
}

// ==========================================================================
// HORIZONTAL RULE
// ==========================================================================

hr {
  border: none;
  height: 1px;
  background: var(--color-gray-200);
  margin: var(--spacing-8) 0;
}

// ==========================================================================
// DETAILS & SUMMARY
// ==========================================================================

details {
  margin-bottom: var(--spacing-4);
}

summary {
  cursor: pointer;
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-2) 0;
  
  &:hover {
    color: var(--color-primary-500);
  }
}
