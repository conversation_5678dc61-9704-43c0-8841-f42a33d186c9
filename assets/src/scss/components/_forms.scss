// =============================================================================
// FORM COMPONENT SYSTEM - MOBILE-FIRST
// =============================================================================
// Comprehensive form components with mobile validation, real-time feedback,
// touch-friendly inputs, and mobile-first UX patterns

// ==========================================================================
// FORM VARIABLES
// ==========================================================================

// Touch-friendly sizing
$form-input-height: 48px; // WCAG AA touch target minimum
$form-input-height-sm: 40px; // Compact inputs
$form-input-height-lg: 56px; // Large inputs

// Spacing
$form-group-spacing: var(--spacing-6); // 24px
$form-group-spacing-sm: var(--spacing-4); // 16px
$form-label-spacing: var(--spacing-2); // 8px

// Border radius
$form-border-radius: var(--border-radius-lg);
$form-border-radius-sm: var(--border-radius-md);

// Colors
$form-border-color: var(--color-gray-300);
$form-border-color-focus: var(--color-primary-500);
$form-border-color-error: var(--color-danger-500);
$form-border-color-success: var(--color-success-500);

$form-bg-color: var(--color-white);
$form-bg-color-disabled: var(--color-gray-100);

// Animation
$form-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

// ==========================================================================
// BASE FORM STYLES
// ==========================================================================

.form {
  width: 100%;

  // Form sections
  &__section {
    margin-bottom: var(--spacing-8);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
    margin-bottom: var(--spacing-4);

    @include respond-to(md) {
      font-size: var(--font-size-2xl);
    }
  }

  &__subtitle {
    font-size: var(--font-size-base);
    color: var(--color-gray-600);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
  }
}

// ==========================================================================
// FORM GROUPS
// ==========================================================================

.form-group {
  position: relative;
  margin-bottom: $form-group-spacing;

  &--compact {
    margin-bottom: $form-group-spacing-sm;
  }

  &--inline {
    @include respond-to(md) {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-4);

      .form-label {
        flex-shrink: 0;
        width: 200px;
        margin-bottom: 0;
        padding-top: var(--spacing-3);
      }

      .form-control-wrapper {
        flex: 1;
      }
    }
  }
}

// ==========================================================================
// FORM LABELS
// ==========================================================================

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  margin-bottom: $form-label-spacing;
  line-height: 1.4;

  // Required indicator
  &--required::after {
    content: ' *';
    color: var(--color-danger-500);
    font-weight: var(--font-weight-bold);
  }

  // Optional indicator
  &--optional::after {
    content: ' (optional)';
    color: var(--color-gray-500);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-xs);
  }
}

// ==========================================================================
// FORM CONTROLS
// ==========================================================================

.form-control {
  display: block;
  width: 100%;
  min-height: $form-input-height;
  padding: var(--spacing-3) var(--spacing-4);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--color-gray-900);
  background-color: $form-bg-color;
  background-clip: padding-box;
  border: 2px solid $form-border-color;
  border-radius: $form-border-radius;
  transition: $form-transition;
  appearance: none;

  // Focus state
  &:focus {
    border-color: $form-border-color-focus;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(var(--color-primary-500-rgb), 0.1);
  }

  // Placeholder
  &::placeholder {
    color: var(--color-gray-500);
    opacity: 1;
  }

  // Disabled state
  &:disabled,
  &[readonly] {
    background-color: $form-bg-color-disabled;
    color: var(--color-gray-600);
    cursor: not-allowed;
    opacity: 0.7;
  }

  // Invalid state
  &:invalid,
  &.is-invalid {
    border-color: $form-border-color-error;

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--color-danger-500-rgb), 0.1);
    }
  }

  // Valid state
  &.is-valid {
    border-color: $form-border-color-success;

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--color-success-500-rgb), 0.1);
    }
  }
}

// ==========================================================================
// FORM CONTROL SIZES
// ==========================================================================

.form-control--sm {
  min-height: $form-input-height-sm;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: $form-border-radius-sm;
}

.form-control--lg {
  min-height: $form-input-height-lg;
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-lg);
}

// ==========================================================================
// TEXTAREA
// ==========================================================================

.form-textarea {
  @extend .form-control;
  min-height: 120px;
  resize: vertical;

  &--sm {
    min-height: 80px;
  }

  &--lg {
    min-height: 160px;
  }

  &--fixed {
    resize: none;
  }
}

// ==========================================================================
// SELECT CONTROLS
// ==========================================================================

.form-select {
  @extend .form-control;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--spacing-10);

  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  &[multiple] {
    background-image: none;
    padding-right: var(--spacing-4);
  }
}

// ==========================================================================
// CHECKBOX AND RADIO CONTROLS
// ==========================================================================

.form-check {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);

  &--inline {
    display: inline-flex;
    margin-right: var(--spacing-6);
    margin-bottom: var(--spacing-2);
  }
}

.form-check-input {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin: 0;
  background-color: $form-bg-color;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 2px solid $form-border-color;
  transition: $form-transition;
  cursor: pointer;

  &:focus {
    border-color: $form-border-color-focus;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(var(--color-primary-500-rgb), 0.1);
  }

  &:checked {
    background-color: var(--color-primary-600);
    border-color: var(--color-primary-600);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  // Checkbox specific
  &[type='checkbox'] {
    border-radius: var(--border-radius-sm);

    &:checked {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
    }

    &:indeterminate {
      background-color: var(--color-primary-600);
      border-color: var(--color-primary-600);
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
    }
  }

  // Radio specific
  &[type='radio'] {
    border-radius: 50%;

    &:checked {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='white'/%3e%3c/svg%3e");
    }
  }
}

.form-check-label {
  font-size: var(--font-size-base);
  color: var(--color-gray-700);
  line-height: 1.5;
  cursor: pointer;

  &--sm {
    font-size: var(--font-size-sm);
  }

  &--lg {
    font-size: var(--font-size-lg);
  }
}

// ==========================================================================
// INPUT GROUPS
// ==========================================================================

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;

  .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
  }

  .form-control:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .form-control:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.5;
  color: var(--color-gray-700);
  text-align: center;
  white-space: nowrap;
  background-color: var(--color-gray-100);
  border: 2px solid $form-border-color;
  border-radius: $form-border-radius;

  &:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
  }

  &:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
  }
}

// ==========================================================================
// FLOATING LABELS
// ==========================================================================

.form-floating {
  position: relative;

  .form-control {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
    padding: 1rem 0.75rem 0.25rem;

    &::placeholder {
      color: transparent;
    }

    &:focus,
    &:not(:placeholder-shown) {
      padding-top: 1.625rem;
      padding-bottom: 0.625rem;
    }

    &:focus ~ .form-label,
    &:not(:placeholder-shown) ~ .form-label {
      opacity: 0.65;
      transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }
  }

  .form-label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
    margin-bottom: 0;
  }
}

// ==========================================================================
// VALIDATION FEEDBACK
// ==========================================================================

.form-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  line-height: 1.4;

  &--valid {
    color: var(--color-success-600);
  }

  &--invalid {
    color: var(--color-danger-600);
  }

  &--warning {
    color: var(--color-warning-600);
  }

  &--info {
    color: var(--color-info-600);
  }
}

.form-help {
  @extend .form-feedback;
  color: var(--color-gray-600);

  &--sm {
    font-size: var(--font-size-xs);
  }
}

// ==========================================================================
// FORM VALIDATION STATES
// ==========================================================================

.was-validated {
  .form-control:valid,
  .form-control.is-valid {
    border-color: $form-border-color-success;

    &:focus {
      border-color: $form-border-color-success;
      box-shadow: 0 0 0 3px rgba(var(--color-success-500-rgb), 0.1);
    }
  }

  .form-control:invalid,
  .form-control.is-invalid {
    border-color: $form-border-color-error;

    &:focus {
      border-color: $form-border-color-error;
      box-shadow: 0 0 0 3px rgba(var(--color-danger-500-rgb), 0.1);
    }
  }

  .form-check-input:valid,
  .form-check-input.is-valid {
    border-color: $form-border-color-success;

    &:checked {
      background-color: var(--color-success-600);
      border-color: var(--color-success-600);
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--color-success-500-rgb), 0.1);
    }
  }

  .form-check-input:invalid,
  .form-check-input.is-invalid {
    border-color: $form-border-color-error;

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--color-danger-500-rgb), 0.1);
    }
  }
}

// ==========================================================================
// FORM LAYOUTS
// ==========================================================================

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(var(--spacing-3) * -1);

  .form-group {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 var(--spacing-3);

    @include respond-to(sm) {
      flex: 0 0 50%;
      max-width: 50%;
    }

    @include respond-to(md) {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }
  }

  &--2-cols .form-group {
    @include respond-to(sm) {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  &--3-cols .form-group {
    @include respond-to(md) {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }
  }

  &--4-cols .form-group {
    @include respond-to(lg) {
      flex: 0 0 25%;
      max-width: 25%;
    }
  }
}

// ==========================================================================
// LEGACY FORM SUPPORT
// ==========================================================================

// Support for existing form-group class
.form-group {
  // Legacy styles for compatibility
  .form-control {
    // Override legacy styles with new component styles
    background: $form-bg-color;
    border: 2px solid $form-border-color;
    border-radius: $form-border-radius;
    height: auto;
    min-height: $form-input-height;

    &:focus {
      border-color: $form-border-color-focus;
      box-shadow: 0 0 0 3px rgba(var(--color-primary-500-rgb), 0.1);
    }
  }

  textarea {
    min-height: 120px;
    resize: vertical;

    &::-webkit-input-placeholder,
    &:-ms-input-placeholder,
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
}

// ==========================================================================
// ACCESSIBILITY & MOTION
// ==========================================================================

// Respect reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .form-control,
  .form-check-input,
  .form-floating .form-label {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .form-control,
  .form-select,
  .form-check-input {
    border-width: 3px;
  }

  .form-check-input:checked {
    border-color: var(--color-primary-600);
  }
}
