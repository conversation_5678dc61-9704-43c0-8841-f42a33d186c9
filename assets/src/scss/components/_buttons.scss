// =============================================================================
// BUTTON COMPONENT LIBRARY - MOBILE-FIRST
// =============================================================================
// Comprehensive button system with touch-optimized sizing and BEM methodology
// Designed for 320px viewport first, enhanced progressively

// ==========================================================================
// BUTTON VARIABLES
// ==========================================================================

// Touch-friendly minimum sizes
$btn-min-height: 44px; // WCAG AA touch target minimum
$btn-min-height-small: 36px; // For compact layouts
$btn-min-height-large: 56px; // For prominent actions

// Spacing and sizing
$btn-padding-x-sm: var(--spacing-3); // 12px
$btn-padding-x-md: var(--spacing-4); // 16px
$btn-padding-x-lg: var(--spacing-6); // 24px
$btn-padding-x-xl: var(--spacing-8); // 32px

$btn-padding-y-sm: var(--spacing-2); // 8px
$btn-padding-y-md: var(--spacing-3); // 12px
$btn-padding-y-lg: var(--spacing-4); // 16px

// Border radius
$btn-border-radius-sm: var(--border-radius-md);
$btn-border-radius-md: var(--border-radius-lg);
$btn-border-radius-lg: var(--border-radius-xl);
$btn-border-radius-full: var(--border-radius-full);

// Animation
$btn-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

// ==========================================================================
// BASE BUTTON COMPONENT
// ==========================================================================

.btn {
  // Reset and base styles
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);

  // Typography
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  line-height: 1.2;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;

  // Sizing and spacing
  min-height: $btn-min-height;
  padding: $btn-padding-y-md $btn-padding-x-md;
  border: 2px solid transparent;
  border-radius: $btn-border-radius-md;

  // Interaction
  cursor: pointer;
  user-select: none;
  transition: $btn-transition;

  // Accessibility
  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }

  &:focus:not(:focus-visible) {
    outline: none;
  }

  // Disabled state
  &:disabled,
  &.btn--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  // Loading state
  &.btn--loading {
    position: relative;
    color: transparent;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid currentColor;
      border-radius: 50%;
      border-top-color: transparent;
      animation: btn-spin 0.8s linear infinite;
    }
  }

  // Icon handling
  &__icon {
    flex-shrink: 0;
    font-size: 1.1em;

    &--left {
      margin-right: var(--spacing-1);
    }

    &--right {
      margin-left: var(--spacing-1);
    }

    &--only {
      margin: 0;
    }
  }
}

// ==========================================================================
// BUTTON VARIANTS
// ==========================================================================

// Primary Button
.btn--primary {
  background: var(--color-primary-600);
  color: var(--color-white);
  border-color: var(--color-primary-600);

  &:hover:not(:disabled) {
    background: var(--color-primary-700);
    border-color: var(--color-primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    background: var(--color-primary-800);
    border-color: var(--color-primary-800);
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

// Secondary Button
.btn--secondary {
  background: transparent;
  color: var(--color-primary-600);
  border-color: var(--color-primary-600);

  &:hover:not(:disabled) {
    background: var(--color-primary-50);
    color: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }

  &:active {
    background: var(--color-primary-100);
    color: var(--color-primary-800);
    border-color: var(--color-primary-800);
  }
}

// Tertiary Button (Ghost)
.btn--tertiary {
  background: transparent;
  color: var(--color-primary-600);
  border-color: transparent;

  &:hover:not(:disabled) {
    background: var(--color-primary-50);
    color: var(--color-primary-700);
  }

  &:active {
    background: var(--color-primary-100);
    color: var(--color-primary-800);
  }
}

// Success Button
.btn--success {
  background: var(--color-success-600);
  color: var(--color-white);
  border-color: var(--color-success-600);

  &:hover:not(:disabled) {
    background: var(--color-success-700);
    border-color: var(--color-success-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    background: var(--color-success-800);
    border-color: var(--color-success-800);
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

// Warning Button
.btn--warning {
  background: var(--color-warning-600);
  color: var(--color-white);
  border-color: var(--color-warning-600);

  &:hover:not(:disabled) {
    background: var(--color-warning-700);
    border-color: var(--color-warning-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    background: var(--color-warning-800);
    border-color: var(--color-warning-800);
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

// Danger Button
.btn--danger {
  background: var(--color-danger-600);
  color: var(--color-white);
  border-color: var(--color-danger-600);

  &:hover:not(:disabled) {
    background: var(--color-danger-700);
    border-color: var(--color-danger-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    background: var(--color-danger-800);
    border-color: var(--color-danger-800);
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }
}

// ==========================================================================
// BUTTON SIZES
// ==========================================================================

// Small Button
.btn--sm {
  min-height: $btn-min-height-small;
  padding: $btn-padding-y-sm $btn-padding-x-sm;
  font-size: var(--font-size-sm);
  border-radius: $btn-border-radius-sm;

  .btn__icon {
    font-size: 1em;
  }
}

// Large Button
.btn--lg {
  min-height: $btn-min-height-large;
  padding: $btn-padding-y-lg $btn-padding-x-lg;
  font-size: var(--font-size-lg);
  border-radius: $btn-border-radius-lg;

  @include respond-to(md) {
    padding: $btn-padding-y-lg $btn-padding-x-xl;
    font-size: var(--font-size-xl);
  }
}

// Extra Large Button (Hero CTAs)
.btn--xl {
  min-height: 64px;
  padding: var(--spacing-5) var(--spacing-10);
  font-size: var(--font-size-xl);
  border-radius: $btn-border-radius-lg;

  @include respond-to(md) {
    min-height: 72px;
    padding: var(--spacing-6) var(--spacing-12);
    font-size: var(--font-size-2xl);
  }
}

// ==========================================================================
// BUTTON SHAPES
// ==========================================================================

// Rounded Button
.btn--rounded {
  border-radius: $btn-border-radius-full;
}

// Square Button (Icon only)
.btn--square {
  width: $btn-min-height;
  height: $btn-min-height;
  padding: 0;
  border-radius: $btn-border-radius-sm;

  &.btn--sm {
    width: $btn-min-height-small;
    height: $btn-min-height-small;
  }

  &.btn--lg {
    width: $btn-min-height-large;
    height: $btn-min-height-large;
  }
}

// Circle Button (Icon only)
.btn--circle {
  @extend .btn--square;
  border-radius: $btn-border-radius-full;
}

// ==========================================================================
// BUTTON LAYOUTS
// ==========================================================================

// Full Width Button
.btn--full {
  width: 100%;

  @include respond-to(md) {
    width: auto;
    min-width: 200px;
  }
}

// Block Button (always full width)
.btn--block {
  width: 100%;
}

// Fluid Button (responsive width)
.btn--fluid {
  width: 100%;

  @include respond-to(sm) {
    width: auto;
    min-width: 160px;
  }

  @include respond-to(md) {
    min-width: 200px;
  }
}

// ==========================================================================
// BUTTON GROUPS
// ==========================================================================

.btn-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);

  @include respond-to(sm) {
    flex-direction: row;
    align-items: center;
  }

  &--center {
    justify-content: center;
  }

  &--start {
    justify-content: flex-start;
  }

  &--end {
    justify-content: flex-end;
  }

  &--space-between {
    justify-content: space-between;
  }

  // Connected buttons
  &--connected {
    gap: 0;

    .btn {
      border-radius: 0;

      &:first-child {
        border-top-left-radius: $btn-border-radius-md;
        border-bottom-left-radius: $btn-border-radius-md;
      }

      &:last-child {
        border-top-right-radius: $btn-border-radius-md;
        border-bottom-right-radius: $btn-border-radius-md;
      }

      &:not(:last-child) {
        border-right-width: 1px;
      }
    }
  }
}

// ==========================================================================
// LEGACY BUTTON SUPPORT
// ==========================================================================

// Support for existing round-button class
.round-button {
  @extend .btn;
  @extend .btn--primary;
  @extend .btn--rounded;

  // Maintain existing sizing for compatibility
  min-width: 195px;

  &.box-shadow {
    background: linear-gradient(105deg, #00b0d4 0%, #1c5067 100%);
    box-shadow: 0 6px 10px #3490b8;

    &:hover:not(:disabled) {
      box-shadow: 0 8px 15px #3490b8;
    }
  }

  &.green {
    background: var(--color-success-600);
    box-shadow: 0px 6px 10px rgba(42, 118, 150, 0.69);

    &:hover:not(:disabled) {
      background: var(--color-success-700);
      box-shadow: 0px 8px 15px rgba(42, 118, 150, 0.69);
    }
  }
}

// ==========================================================================
// ANIMATIONS
// ==========================================================================

@keyframes btn-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// ==========================================================================
// ACCESSIBILITY & MOTION
// ==========================================================================

// Respect reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;

    &:hover:not(:disabled) {
      transform: none;
    }
  }

  .btn--loading::after {
    animation: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .btn {
    border-width: 3px;
  }

  .btn--tertiary {
    border-color: currentColor;
  }
}
