// =============================================================================
// NAVIGATION COMPONENT - MOBILE-FIRST
// =============================================================================
// Modern slide-in navigation panel with progressive disclosure
// Designed for 320px viewport first, enhanced progressively

// ==========================================================================
// NAVIGATION VARIABLES
// ==========================================================================

$nav-panel-width: 280px;
$nav-panel-width-tablet: 320px;
$nav-toggle-size: 44px; // Touch-friendly minimum
$nav-item-height: 48px; // Touch-friendly minimum
$nav-animation-duration: 0.3s;
$nav-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);

// ==========================================================================
// NAVIGATION HEADER
// ==========================================================================

.navigation {
  &__header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--spacing-14); // 56px
    background: var(--color-primary-600);
    z-index: var(--z-index-header);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-4);
    box-shadow: var(--shadow-sm);

    @include respond-to(md) {
      height: var(--spacing-20); // 80px
      padding: 0 var(--spacing-12);
    }
  }

  &__logo {
    height: var(--spacing-8); // 32px
    width: auto;
    transition: var(--transition-all);

    @include respond-to(md) {
      height: var(--spacing-12); // 48px
    }

    @include respond-to(lg) {
      height: var(--spacing-16); // 64px
    }
  }

  &__controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);

    @include respond-to(md) {
      gap: var(--spacing-4);
    }
  }
}

// ==========================================================================
// LANGUAGE SWITCHER
// ==========================================================================

.language-switcher {
  &__container {
    position: relative;
  }

  &__select {
    appearance: none;
    background: transparent;
    border: 2px solid var(--color-white);
    border-radius: var(--border-radius-md);
    color: var(--color-white);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-2) var(--spacing-6) var(--spacing-2) var(--spacing-3);
    min-width: 60px;
    cursor: pointer;
    transition: var(--transition-all);

    &:hover,
    &:focus {
      background: rgba(255, 255, 255, 0.1);
      outline: none;
    }

    @include respond-to(md) {
      font-size: var(--font-size-base);
      padding: var(--spacing-2) var(--spacing-8) var(--spacing-2)
        var(--spacing-4);
      min-width: 80px;
    }
  }

  &__icon {
    position: absolute;
    right: var(--spacing-2);
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    pointer-events: none;

    @include respond-to(md) {
      right: var(--spacing-3);
      font-size: var(--font-size-sm);
    }
  }
}

// ==========================================================================
// NAVIGATION TOGGLE BUTTON
// ==========================================================================

.navigation__toggle {
  position: relative;
  width: $nav-toggle-size;
  height: $nav-toggle-size;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: var(--spacing-3);
  border-radius: var(--border-radius-md);
  transition: var(--transition-all);
  z-index: var(--z-index-modal);

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid var(--color-white);
    outline-offset: 2px;
  }

  // Hamburger lines
  &__line {
    position: absolute;
    left: 50%;
    width: 20px;
    height: 2px;
    background: var(--color-white);
    border-radius: 1px;
    transform: translateX(-50%);
    transition: all $nav-animation-duration $nav-animation-easing;

    &:nth-child(1) {
      top: 12px;
    }

    &:nth-child(2) {
      top: 20px;
    }

    &:nth-child(3) {
      top: 28px;
    }
  }

  // Active state (X icon)
  &--active {
    .navigation__toggle__line {
      &:nth-child(1) {
        top: 20px;
        transform: translateX(-50%) rotate(45deg);
      }

      &:nth-child(2) {
        opacity: 0;
      }

      &:nth-child(3) {
        top: 20px;
        transform: translateX(-50%) rotate(-45deg);
      }
    }
  }
}

// ==========================================================================
// NAVIGATION PANEL (SLIDE-IN)
// ==========================================================================

.navigation__panel {
  position: fixed;
  top: var(--spacing-14); // Below header on mobile
  left: 0;
  width: $nav-panel-width;
  height: calc(100vh - var(--spacing-14));
  background: var(--color-white);
  box-shadow: var(--shadow-xl);
  transform: translateX(-100%);
  transition: transform $nav-animation-duration $nav-animation-easing;
  z-index: var(--z-index-dropdown);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  @include respond-to(md) {
    top: var(--spacing-20); // Below header on tablet+
    height: calc(100vh - var(--spacing-20));
    width: $nav-panel-width-tablet;
  }

  @include respond-to(lg) {
    // Hide on desktop - could implement different behavior
    display: none;
  }

  &--open {
    transform: translateX(0);
  }
}

// ==========================================================================
// NAVIGATION OVERLAY
// ==========================================================================

.navigation__overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all $nav-animation-duration $nav-animation-easing;
  z-index: calc(var(--z-index-dropdown) - 1);

  &--visible {
    opacity: 1;
    visibility: visible;
  }
}

// ==========================================================================
// NAVIGATION MENU
// ==========================================================================

.navigation__menu {
  padding: var(--spacing-6) 0;

  &__section {
    margin-bottom: var(--spacing-8);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__title {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-gray-500);
    padding: 0 var(--spacing-6);
    margin-bottom: var(--spacing-3);
  }

  &__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__item {
    margin: 0;
  }

  &__link {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-6);
    min-height: $nav-item-height;
    color: var(--color-gray-900);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: var(--transition-colors);
    border-left: 3px solid transparent;

    &:hover,
    &:focus {
      background: var(--color-gray-50);
      color: var(--color-primary-600);
      border-left-color: var(--color-primary-600);
    }

    &--active {
      background: var(--color-primary-50);
      color: var(--color-primary-600);
      border-left-color: var(--color-primary-600);
      font-weight: var(--font-weight-semibold);
    }
  }

  &__icon {
    margin-right: var(--spacing-3);
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
  }
}

// ==========================================================================
// PROGRESSIVE DISCLOSURE SUBMENU
// ==========================================================================

.navigation__submenu {
  &__toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--spacing-3) var(--spacing-6);
    min-height: $nav-item-height;
    background: transparent;
    border: none;
    color: var(--color-gray-900);
    font-weight: var(--font-weight-medium);
    text-align: left;
    cursor: pointer;
    transition: var(--transition-colors);
    border-left: 3px solid transparent;

    &:hover,
    &:focus {
      background: var(--color-gray-50);
      color: var(--color-primary-600);
      border-left-color: var(--color-primary-600);
    }

    &--active {
      background: var(--color-primary-50);
      color: var(--color-primary-600);
      border-left-color: var(--color-primary-600);
    }
  }

  &__icon {
    margin-right: var(--spacing-3);
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
  }

  &__arrow {
    font-size: var(--font-size-sm);
    transition: transform $nav-animation-duration $nav-animation-easing;

    &--expanded {
      transform: rotate(180deg);
    }
  }

  &__content {
    max-height: 0;
    overflow: hidden;
    transition: max-height $nav-animation-duration $nav-animation-easing;
    background: var(--color-gray-25);

    &--expanded {
      max-height: 300px; // Adjust based on content
    }
  }

  &__list {
    list-style: none;
    margin: 0;
    padding: var(--spacing-2) 0;
  }

  &__item {
    margin: 0;
  }

  &__link {
    display: block;
    padding: var(--spacing-2) var(--spacing-6) var(--spacing-2)
      var(--spacing-12);
    color: var(--color-gray-700);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: var(--transition-colors);

    &:hover,
    &:focus {
      background: var(--color-gray-100);
      color: var(--color-primary-600);
    }

    &--active {
      background: var(--color-primary-100);
      color: var(--color-primary-600);
      font-weight: var(--font-weight-medium);
    }
  }
}

// ==========================================================================
// NAVIGATION CONTACT INFO
// ==========================================================================

.navigation__contact {
  padding: var(--spacing-6);
  background: var(--color-gray-50);
  border-top: 1px solid var(--color-gray-200);
  margin-top: auto;

  &__title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--spacing-4);
  }

  &__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__item {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__icon {
    margin-right: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--color-primary-600);
    width: 16px;
    text-align: center;
  }
}

// ==========================================================================
// ACCESSIBILITY & MOTION
// ==========================================================================

// Respect reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .navigation__panel,
  .navigation__overlay,
  .navigation__toggle__line,
  .navigation__submenu__arrow,
  .navigation__submenu__content {
    transition: none;
  }
}

// Focus management
.navigation__panel:focus-within {
  outline: none;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .navigation__panel {
    border: 2px solid var(--color-gray-900);
  }

  .navigation__menu__link,
  .navigation__submenu__toggle {
    border-bottom: 1px solid var(--color-gray-300);
  }
}
