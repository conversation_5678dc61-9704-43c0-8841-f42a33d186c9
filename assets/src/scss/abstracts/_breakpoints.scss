// =============================================================================
// ENHANCED BREAKPOINT SYSTEM
// =============================================================================
// 7-breakpoint responsive system for comprehensive device coverage

// ==========================================================================
// BREAKPOINT DEFINITIONS
// ==========================================================================

$breakpoints: (
  xs: 0,          // Mobile portrait (320px+)
  sm: 576px,      // Mobile landscape (576px+)
  md: 768px,      // Tablet portrait (768px+)
  lg: 992px,      // Tablet landscape / Small desktop (992px+)
  xl: 1200px,     // Desktop (1200px+)
  xxl: 1400px,    // Large desktop (1400px+)
  xxxl: 1920px    // Ultra-wide desktop (1920px+)
) !default;

// ==========================================================================
// CONTAINER MAX WIDTHS
// ==========================================================================

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px,
  xxxl: 1800px
) !default;

// ==========================================================================
// RESPONSIVE MIXINS
// ==========================================================================

// Media query mixin for min-width breakpoints
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $breakpoint-value: map-get($breakpoints, $breakpoint);
    
    @if $breakpoint-value == 0 {
      @content;
    } @else {
      @media (min-width: $breakpoint-value) {
        @content;
      }
    }
  } @else {
    @warn "Invalid breakpoint: #{$breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// Media query mixin for max-width breakpoints
@mixin respond-to-max($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $breakpoint-value: map-get($breakpoints, $breakpoint);
    
    @if $breakpoint-value > 0 {
      @media (max-width: $breakpoint-value - 1px) {
        @content;
      }
    }
  } @else {
    @warn "Invalid breakpoint: #{$breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// Media query mixin for between breakpoints
@mixin respond-between($min-breakpoint, $max-breakpoint) {
  @if map-has-key($breakpoints, $min-breakpoint) and map-has-key($breakpoints, $max-breakpoint) {
    $min-value: map-get($breakpoints, $min-breakpoint);
    $max-value: map-get($breakpoints, $max-breakpoint);
    
    @media (min-width: $min-value) and (max-width: $max-value - 1px) {
      @content;
    }
  } @else {
    @warn "Invalid breakpoints: #{$min-breakpoint} or #{$max-breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// Media query mixin for only specific breakpoint
@mixin respond-only($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $breakpoint-keys: map-keys($breakpoints);
    $breakpoint-index: index($breakpoint-keys, $breakpoint);
    
    @if $breakpoint-index < length($breakpoint-keys) {
      $next-breakpoint: nth($breakpoint-keys, $breakpoint-index + 1);
      @include respond-between($breakpoint, $next-breakpoint) {
        @content;
      }
    } @else {
      @include respond-to($breakpoint) {
        @content;
      }
    }
  } @else {
    @warn "Invalid breakpoint: #{$breakpoint}. Available breakpoints: #{map-keys($breakpoints)}";
  }
}

// ==========================================================================
// DEVICE-SPECIFIC MIXINS
// ==========================================================================

// Mobile devices (portrait and landscape)
@mixin mobile-only {
  @include respond-to-max(md) {
    @content;
  }
}

// Tablet devices (portrait and landscape)
@mixin tablet-only {
  @include respond-between(md, xl) {
    @content;
  }
}

// Desktop devices
@mixin desktop-only {
  @include respond-to(xl) {
    @content;
  }
}

// Touch devices (mobile and tablet)
@mixin touch-devices {
  @include respond-to-max(lg) {
    @content;
  }
}

// Large screens (desktop and above)
@mixin large-screens {
  @include respond-to(lg) {
    @content;
  }
}

// Ultra-wide screens
@mixin ultra-wide {
  @include respond-to(xxxl) {
    @content;
  }
}

// ==========================================================================
// ORIENTATION MIXINS
// ==========================================================================

@mixin landscape {
  @media (orientation: landscape) {
    @content;
  }
}

@mixin portrait {
  @media (orientation: portrait) {
    @content;
  }
}

// ==========================================================================
// HIGH DPI / RETINA MIXINS
// ==========================================================================

@mixin retina {
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    @content;
  }
}

@mixin high-dpi {
  @media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi) {
    @content;
  }
}

// ==========================================================================
// PRINT STYLES
// ==========================================================================

@mixin print {
  @media print {
    @content;
  }
}

// ==========================================================================
// REDUCED MOTION
// ==========================================================================

@mixin reduced-motion {
  @media (prefers-reduced-motion: reduce) {
    @content;
  }
}

@mixin motion-ok {
  @media (prefers-reduced-motion: no-preference) {
    @content;
  }
}

// ==========================================================================
// DARK MODE
// ==========================================================================

@mixin dark-mode {
  @media (prefers-color-scheme: dark) {
    @content;
  }
}

@mixin light-mode {
  @media (prefers-color-scheme: light) {
    @content;
  }
}

// ==========================================================================
// UTILITY FUNCTIONS
// ==========================================================================

// Get breakpoint value
@function breakpoint($name) {
  @if map-has-key($breakpoints, $name) {
    @return map-get($breakpoints, $name);
  } @else {
    @warn "Invalid breakpoint: #{$name}. Available breakpoints: #{map-keys($breakpoints)}";
    @return null;
  }
}

// Get container max-width
@function container-max-width($name) {
  @if map-has-key($container-max-widths, $name) {
    @return map-get($container-max-widths, $name);
  } @else {
    @warn "Invalid container size: #{$name}. Available sizes: #{map-keys($container-max-widths)}";
    @return null;
  }
}

// ==========================================================================
// EXAMPLE USAGE
// ==========================================================================

/*
// Basic responsive design
.component {
  // Mobile first (xs)
  padding: 1rem;
  
  @include respond-to(sm) {
    padding: 1.5rem;
  }
  
  @include respond-to(md) {
    padding: 2rem;
  }
  
  @include respond-to(lg) {
    padding: 2.5rem;
  }
  
  @include respond-to(xl) {
    padding: 3rem;
  }
  
  @include respond-to(xxl) {
    padding: 3.5rem;
  }
  
  @include respond-to(xxxl) {
    padding: 4rem;
  }
}

// Device-specific styles
.navigation {
  @include mobile-only {
    // Mobile-specific navigation styles
  }
  
  @include tablet-only {
    // Tablet-specific navigation styles
  }
  
  @include desktop-only {
    // Desktop-specific navigation styles
  }
}

// Orientation-specific styles
.hero {
  @include landscape {
    // Landscape orientation styles
  }
  
  @include portrait {
    // Portrait orientation styles
  }
}
*/
