// =============================================================================
// DESIGN TOKENS
// =============================================================================
// CSS Custom Properties for the VIMA Holding design system
// These tokens provide a consistent foundation for all design decisions

:root {
  // ==========================================================================
  // COLORS
  // ==========================================================================
  
  // Primary Color Scale (VIMA Blue)
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  --color-primary-950: #082f49;

  // Secondary Color Scale (VIMA Orange/Gold)
  --color-secondary-50: #fffbeb;
  --color-secondary-100: #fef3c7;
  --color-secondary-200: #fde68a;
  --color-secondary-300: #fcd34d;
  --color-secondary-400: #fbbf24;
  --color-secondary-500: #f59e0b;
  --color-secondary-600: #d97706;
  --color-secondary-700: #b45309;
  --color-secondary-800: #92400e;
  --color-secondary-900: #78350f;
  --color-secondary-950: #451a03;

  // Neutral Gray Scale
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  // Semantic Colors
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;

  // Special Colors
  --color-white: #ffffff;
  --color-black: #000000;
  --color-transparent: transparent;

  // RGB Values for alpha transparency
  --color-primary-500-rgb: 14, 165, 233;
  --color-secondary-500-rgb: 245, 158, 11;
  --color-gray-900-rgb: 17, 24, 39;

  // ==========================================================================
  // SPACING SCALE
  // ==========================================================================
  
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem;  // 2px
  --spacing-1: 0.25rem;     // 4px
  --spacing-1-5: 0.375rem;  // 6px
  --spacing-2: 0.5rem;      // 8px
  --spacing-2-5: 0.625rem;  // 10px
  --spacing-3: 0.75rem;     // 12px
  --spacing-3-5: 0.875rem;  // 14px
  --spacing-4: 1rem;        // 16px
  --spacing-5: 1.25rem;     // 20px
  --spacing-6: 1.5rem;      // 24px
  --spacing-7: 1.75rem;     // 28px
  --spacing-8: 2rem;        // 32px
  --spacing-9: 2.25rem;     // 36px
  --spacing-10: 2.5rem;     // 40px
  --spacing-11: 2.75rem;    // 44px
  --spacing-12: 3rem;       // 48px
  --spacing-14: 3.5rem;     // 56px
  --spacing-16: 4rem;       // 64px
  --spacing-20: 5rem;       // 80px
  --spacing-24: 6rem;       // 96px
  --spacing-28: 7rem;       // 112px
  --spacing-32: 8rem;       // 128px
  --spacing-36: 9rem;       // 144px
  --spacing-40: 10rem;      // 160px
  --spacing-44: 11rem;      // 176px
  --spacing-48: 12rem;      // 192px
  --spacing-52: 13rem;      // 208px
  --spacing-56: 14rem;      // 224px
  --spacing-60: 15rem;      // 240px
  --spacing-64: 16rem;      // 256px
  --spacing-72: 18rem;      // 288px
  --spacing-80: 20rem;      // 320px
  --spacing-96: 24rem;      // 384px

  // ==========================================================================
  // TYPOGRAPHY
  // ==========================================================================
  
  // Font Families
  --font-family-primary: 'Proxima Nova Alt', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-heading: 'Proxima Nova Alt Bold', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Courier New', monospace;

  // Font Sizes
  --font-size-xs: 0.75rem;      // 12px
  --font-size-sm: 0.875rem;     // 14px
  --font-size-base: 1rem;       // 16px
  --font-size-lg: 1.125rem;     // 18px
  --font-size-xl: 1.25rem;      // 20px
  --font-size-2xl: 1.5rem;      // 24px
  --font-size-3xl: 1.875rem;    // 30px
  --font-size-4xl: 2.25rem;     // 36px
  --font-size-5xl: 3rem;        // 48px
  --font-size-6xl: 3.75rem;     // 60px
  --font-size-7xl: 4.5rem;      // 72px
  --font-size-8xl: 6rem;        // 96px
  --font-size-9xl: 8rem;        // 128px

  // Line Heights
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  // Font Weights
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  // ==========================================================================
  // BORDER RADIUS
  // ==========================================================================
  
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;    // 2px
  --border-radius-base: 0.25rem;   // 4px
  --border-radius-md: 0.375rem;    // 6px
  --border-radius-lg: 0.5rem;      // 8px
  --border-radius-xl: 0.75rem;     // 12px
  --border-radius-2xl: 1rem;       // 16px
  --border-radius-3xl: 1.5rem;     // 24px
  --border-radius-full: 9999px;

  // ==========================================================================
  // SHADOWS
  // ==========================================================================
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: none;

  // ==========================================================================
  // Z-INDEX SCALE
  // ==========================================================================
  
  --z-index-auto: auto;
  --z-index-0: 0;
  --z-index-10: 10;
  --z-index-20: 20;
  --z-index-30: 30;
  --z-index-40: 40;
  --z-index-50: 50;
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  // ==========================================================================
  // TRANSITIONS
  // ==========================================================================
  
  --transition-duration-75: 75ms;
  --transition-duration-100: 100ms;
  --transition-duration-150: 150ms;
  --transition-duration-200: 200ms;
  --transition-duration-300: 300ms;
  --transition-duration-500: 500ms;
  --transition-duration-700: 700ms;
  --transition-duration-1000: 1000ms;

  --transition-timing-linear: linear;
  --transition-timing-ease: ease;
  --transition-timing-ease-in: ease-in;
  --transition-timing-ease-out: ease-out;
  --transition-timing-ease-in-out: ease-in-out;

  // Common transitions
  --transition-all: all var(--transition-duration-150) var(--transition-timing-ease-in-out);
  --transition-colors: color var(--transition-duration-150) var(--transition-timing-ease-in-out), 
                       background-color var(--transition-duration-150) var(--transition-timing-ease-in-out), 
                       border-color var(--transition-duration-150) var(--transition-timing-ease-in-out);
  --transition-opacity: opacity var(--transition-duration-150) var(--transition-timing-ease-in-out);
  --transition-transform: transform var(--transition-duration-150) var(--transition-timing-ease-in-out);
}
