// =============================================================================
// UTILITY MIXINS
// =============================================================================
// Reusable mixins for common patterns and utilities

// ==========================================================================
// LAYOUT MIXINS
// ==========================================================================

// Flexbox utilities
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  @include flex-column;
  align-items: center;
  justify-content: center;
}

// Grid utilities
@mixin grid-center {
  display: grid;
  place-items: center;
}

// Absolute positioning
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin absolute-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// ==========================================================================
// TYPOGRAPHY MIXINS
// ==========================================================================

// Fluid typography
@mixin fluid-type($min-size, $max-size, $min-width: 320px, $max-width: 1200px) {
  font-size: $min-size;
  
  @media (min-width: $min-width) {
    font-size: calc(#{$min-size} + #{strip-unit($max-size - $min-size)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
  }
  
  @media (min-width: $max-width) {
    font-size: $max-size;
  }
}

// Text truncation
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-truncate-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Font smoothing
@mixin font-smoothing {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ==========================================================================
// VISUAL MIXINS
// ==========================================================================

// Border radius
@mixin border-radius($radius: var(--border-radius-base)) {
  border-radius: $radius;
}

// Box shadow
@mixin box-shadow($shadow: var(--shadow-base)) {
  box-shadow: $shadow;
}

// Gradient backgrounds
@mixin gradient-primary {
  background: linear-gradient(
    135deg,
    var(--color-primary-500) 0%,
    var(--color-primary-700) 100%
  );
}

@mixin gradient-secondary {
  background: linear-gradient(
    135deg,
    var(--color-secondary-500) 0%,
    var(--color-secondary-700) 100%
  );
}

// Glass morphism effect
@mixin glass-morphism($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// ==========================================================================
// INTERACTION MIXINS
// ==========================================================================

// Hover effects
@mixin hover-lift {
  transition: var(--transition-transform);
  
  &:hover {
    transform: translateY(-2px);
  }
}

@mixin hover-scale($scale: 1.05) {
  transition: var(--transition-transform);
  
  &:hover {
    transform: scale($scale);
  }
}

// Focus styles
@mixin focus-ring($color: var(--color-primary-500)) {
  outline: 2px solid transparent;
  outline-offset: 2px;
  
  &:focus {
    outline: 2px solid $color;
    outline-offset: 2px;
  }
  
  &:focus:not(:focus-visible) {
    outline: none;
  }
}

// Button reset
@mixin button-reset {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  
  &:focus {
    outline: none;
  }
}

// ==========================================================================
// ANIMATION MIXINS
// ==========================================================================

// Fade in animation
@mixin fade-in($duration: var(--transition-duration-300)) {
  opacity: 0;
  animation: fadeIn $duration var(--transition-timing-ease-out) forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

// Slide in animations
@mixin slide-in-up($duration: var(--transition-duration-300)) {
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp $duration var(--transition-timing-ease-out) forwards;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@mixin slide-in-left($duration: var(--transition-duration-300)) {
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInLeft $duration var(--transition-timing-ease-out) forwards;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Pulse animation
@mixin pulse($duration: 2s) {
  animation: pulse $duration var(--transition-timing-ease-in-out) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// ==========================================================================
// UTILITY MIXINS
// ==========================================================================

// Clearfix
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// Visually hidden (for screen readers)
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

// Aspect ratio
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// ==========================================================================
// HELPER FUNCTIONS
// ==========================================================================

// Strip unit from number
@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }
  
  @return $number;
}

// Convert px to rem
@function rem($pixels, $base: 16px) {
  @return $pixels / $base * 1rem;
}

// Convert px to em
@function em($pixels, $base: 16px) {
  @return $pixels / $base * 1em;
}

// ==========================================================================
// CONTAINER MIXINS
// ==========================================================================

// Responsive container
@mixin container($max-width: null) {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
  
  @if $max-width {
    max-width: $max-width;
  } @else {
    @include respond-to(sm) {
      max-width: container-max-width(sm);
    }
    
    @include respond-to(md) {
      max-width: container-max-width(md);
    }
    
    @include respond-to(lg) {
      max-width: container-max-width(lg);
    }
    
    @include respond-to(xl) {
      max-width: container-max-width(xl);
    }
    
    @include respond-to(xxl) {
      max-width: container-max-width(xxl);
    }
    
    @include respond-to(xxxl) {
      max-width: container-max-width(xxxl);
    }
  }
  
  @include respond-to(md) {
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
  
  @include respond-to(lg) {
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}
