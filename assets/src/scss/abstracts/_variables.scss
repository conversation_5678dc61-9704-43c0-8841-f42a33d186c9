// =============================================================================
// SCSS VARIABLES
// =============================================================================
// SCSS variables with actual values for calculations and mixins
// These correspond to our CSS custom properties but use real values

// ==========================================================================
// COLORS
// ==========================================================================

// Primary colors (VIMA Blue)
$color-primary-50: #f0f9ff;
$color-primary-100: #e0f2fe;
$color-primary-200: #bae6fd;
$color-primary-300: #7dd3fc;
$color-primary-400: #38bdf8;
$color-primary-500: #0ea5e9;
$color-primary-600: #0284c7;
$color-primary-700: #0369a1;
$color-primary-800: #075985;
$color-primary-900: #0c4a6e;
$color-primary-950: #082f49;

// Secondary colors (VIMA Orange/Gold)
$color-secondary-50: #fffbeb;
$color-secondary-100: #fef3c7;
$color-secondary-200: #fde68a;
$color-secondary-300: #fcd34d;
$color-secondary-400: #fbbf24;
$color-secondary-500: #f59e0b;
$color-secondary-600: #d97706;
$color-secondary-700: #b45309;
$color-secondary-800: #92400e;
$color-secondary-900: #78350f;
$color-secondary-950: #451a03;

// Neutral colors
$color-gray-50: #f9fafb;
$color-gray-100: #f3f4f6;
$color-gray-200: #e5e7eb;
$color-gray-300: #d1d5db;
$color-gray-400: #9ca3af;
$color-gray-500: #6b7280;
$color-gray-600: #4b5563;
$color-gray-700: #374151;
$color-gray-800: #1f2937;
$color-gray-900: #111827;
$color-gray-950: #030712;

// Semantic colors
$color-success: #22c55e;
$color-warning: #f59e0b;
$color-error: #ef4444;
$color-info: #3b82f6;

// Special colors
$color-white: #ffffff;
$color-black: #000000;
$color-transparent: transparent;

// ==========================================================================
// SPACING
// ==========================================================================

$spacing-0: 0;
$spacing-px: 1px;
$spacing-0-5: 0.125rem; // 2px
$spacing-1: 0.25rem; // 4px
$spacing-1-5: 0.375rem; // 6px
$spacing-2: 0.5rem; // 8px
$spacing-2-5: 0.625rem; // 10px
$spacing-3: 0.75rem; // 12px
$spacing-3-5: 0.875rem; // 14px
$spacing-4: 1rem; // 16px
$spacing-5: 1.25rem; // 20px
$spacing-6: 1.5rem; // 24px
$spacing-7: 1.75rem; // 28px
$spacing-8: 2rem; // 32px
$spacing-9: 2.25rem; // 36px
$spacing-10: 2.5rem; // 40px
$spacing-11: 2.75rem; // 44px
$spacing-12: 3rem; // 48px
$spacing-14: 3.5rem; // 56px
$spacing-16: 4rem; // 64px
$spacing-20: 5rem; // 80px
$spacing-24: 6rem; // 96px
$spacing-28: 7rem; // 112px
$spacing-32: 8rem; // 128px
$spacing-36: 9rem; // 144px
$spacing-40: 10rem; // 160px
$spacing-44: 11rem; // 176px
$spacing-48: 12rem; // 192px
$spacing-52: 13rem; // 208px
$spacing-56: 14rem; // 224px
$spacing-60: 15rem; // 240px
$spacing-64: 16rem; // 256px
$spacing-72: 18rem; // 288px
$spacing-80: 20rem; // 320px
$spacing-96: 24rem; // 384px

// ==========================================================================
// TYPOGRAPHY
// ==========================================================================

// Font families
$font-family-primary: 'Proxima Nova Alt', -apple-system, BlinkMacSystemFont,
  'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-heading: 'Proxima Nova Alt Bold', -apple-system, BlinkMacSystemFont,
  'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Courier New',
  monospace;

// Font sizes
$font-size-xs: 0.75rem; // 12px
$font-size-sm: 0.875rem; // 14px
$font-size-base: 1rem; // 16px
$font-size-lg: 1.125rem; // 18px
$font-size-xl: 1.25rem; // 20px
$font-size-2xl: 1.5rem; // 24px
$font-size-3xl: 1.875rem; // 30px
$font-size-4xl: 2.25rem; // 36px
$font-size-5xl: 3rem; // 48px
$font-size-6xl: 3.75rem; // 60px
$font-size-7xl: 4.5rem; // 72px
$font-size-8xl: 6rem; // 96px
$font-size-9xl: 8rem; // 128px

// Line heights
$line-height-none: 1;
$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

// Font weights
$font-weight-thin: 100;
$font-weight-extralight: 200;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

// ==========================================================================
// BORDER RADIUS
// ==========================================================================

$border-radius-none: 0;
$border-radius-sm: 0.125rem; // 2px
$border-radius-base: 0.25rem; // 4px
$border-radius-md: 0.375rem; // 6px
$border-radius-lg: 0.5rem; // 8px
$border-radius-xl: 0.75rem; // 12px
$border-radius-2xl: 1rem; // 16px
$border-radius-3xl: 1.5rem; // 24px
$border-radius-full: 9999px;

// ==========================================================================
// SHADOWS
// ==========================================================================

$shadow-sm: var(--shadow-sm);
$shadow-base: var(--shadow-base);
$shadow-md: var(--shadow-md);
$shadow-lg: var(--shadow-lg);
$shadow-xl: var(--shadow-xl);
$shadow-2xl: var(--shadow-2xl);
$shadow-inner: var(--shadow-inner);
$shadow-none: var(--shadow-none);

// ==========================================================================
// Z-INDEX
// ==========================================================================

$z-index-auto: var(--z-index-auto);
$z-index-0: var(--z-index-0);
$z-index-10: var(--z-index-10);
$z-index-20: var(--z-index-20);
$z-index-30: var(--z-index-30);
$z-index-40: var(--z-index-40);
$z-index-50: var(--z-index-50);
$z-index-dropdown: var(--z-index-dropdown);
$z-index-sticky: var(--z-index-sticky);
$z-index-fixed: var(--z-index-fixed);
$z-index-modal-backdrop: var(--z-index-modal-backdrop);
$z-index-modal: var(--z-index-modal);
$z-index-popover: var(--z-index-popover);
$z-index-tooltip: var(--z-index-tooltip);

// ==========================================================================
// TRANSITIONS
// ==========================================================================

$transition-duration-75: var(--transition-duration-75);
$transition-duration-100: var(--transition-duration-100);
$transition-duration-150: var(--transition-duration-150);
$transition-duration-200: var(--transition-duration-200);
$transition-duration-300: var(--transition-duration-300);
$transition-duration-500: var(--transition-duration-500);
$transition-duration-700: var(--transition-duration-700);
$transition-duration-1000: var(--transition-duration-1000);

$transition-timing-linear: var(--transition-timing-linear);
$transition-timing-ease: var(--transition-timing-ease);
$transition-timing-ease-in: var(--transition-timing-ease-in);
$transition-timing-ease-out: var(--transition-timing-ease-out);
$transition-timing-ease-in-out: var(--transition-timing-ease-in-out);

$transition-all: var(--transition-all);
$transition-colors: var(--transition-colors);
$transition-opacity: var(--transition-opacity);
$transition-transform: var(--transition-transform);

// ==========================================================================
// COMPONENT-SPECIFIC VARIABLES
// ==========================================================================

// Buttons
$btn-padding-y: $spacing-2-5;
$btn-padding-x: $spacing-4;
$btn-font-size: $font-size-base;
$btn-line-height: $line-height-normal;
$btn-border-radius: $border-radius-md;
$btn-font-weight: $font-weight-semibold;

// Forms
$input-padding-y: $spacing-3;
$input-padding-x: $spacing-4;
$input-font-size: $font-size-base;
$input-line-height: $line-height-normal;
$input-border-radius: $border-radius-md;
$input-border-width: 1px;
$input-border-color: $color-gray-300;
$input-focus-border-color: $color-primary-500;

// Cards
$card-padding: $spacing-6;
$card-border-radius: $border-radius-lg;
$card-shadow: $shadow-base;
$card-border-width: 1px;
$card-border-color: $color-gray-200;

// Navigation
$nav-link-padding-y: $spacing-2;
$nav-link-padding-x: $spacing-4;
$nav-link-font-size: $font-size-base;
$nav-link-font-weight: $font-weight-medium;

// Grid
$grid-gutter-width: $spacing-6;
$grid-breakpoints: $breakpoints;
$grid-container-max-widths: $container-max-widths;
