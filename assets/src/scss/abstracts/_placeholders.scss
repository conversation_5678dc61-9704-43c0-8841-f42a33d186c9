// =============================================================================
// PLACEHOLDER SELECTORS
// =============================================================================
// Reusable placeholder selectors for common patterns

// ==========================================================================
// LAYOUT PLACEHOLDERS
// ==========================================================================

%flex-center {
  @include flex-center;
}

%flex-between {
  @include flex-between;
}

%flex-column {
  @include flex-column;
}

%flex-column-center {
  @include flex-column-center;
}

%absolute-center {
  @include absolute-center;
}

%absolute-cover {
  @include absolute-cover;
}

// ==========================================================================
// TYPOGRAPHY PLACEHOLDERS
// ==========================================================================

%text-truncate {
  @include text-truncate;
}

%font-smoothing {
  @include font-smoothing;
}

%heading-base {
  font-family: $font-family-heading;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  color: $color-gray-900;
  margin-bottom: $spacing-4;
}

%body-text {
  font-family: $font-family-primary;
  font-weight: $font-weight-normal;
  line-height: $line-height-relaxed;
  color: $color-gray-700;
}

// ==========================================================================
// INTERACTION PLACEHOLDERS
// ==========================================================================

%button-reset {
  @include button-reset;
}

%focus-ring {
  @include focus-ring;
}

%hover-lift {
  @include hover-lift;
}

%hover-scale {
  @include hover-scale;
}

// ==========================================================================
// VISUAL PLACEHOLDERS
// ==========================================================================

%card-base {
  background: $color-white;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: $card-padding;
  border: $card-border-width solid $card-border-color;
}

%gradient-primary {
  @include gradient-primary;
}

%gradient-secondary {
  @include gradient-secondary;
}

%glass-morphism {
  @include glass-morphism;
}

// ==========================================================================
// UTILITY PLACEHOLDERS
// ==========================================================================

%visually-hidden {
  @include visually-hidden;
}

%clearfix {
  @include clearfix;
}

// ==========================================================================
// ANIMATION PLACEHOLDERS
// ==========================================================================

%fade-in {
  @include fade-in;
}

%slide-in-up {
  @include slide-in-up;
}

%slide-in-left {
  @include slide-in-left;
}

%pulse {
  @include pulse;
}
