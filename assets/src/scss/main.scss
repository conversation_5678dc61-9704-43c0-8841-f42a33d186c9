// =============================================================================
// VIMA HOLDING - MAIN STYLESHEET
// =============================================================================
// Modern SCSS architecture following the 7-1 pattern with BEM methodology

// ==========================================================================
// ABSTRACTS
// ==========================================================================
// Variables, functions, mixins, placeholders - no CSS output
@import 'abstracts/index';

// ==========================================================================
// VENDORS
// ==========================================================================
// Third-party CSS and overrides
@import 'vendors/bootstrap';
@import 'vendors/swiper';
@import 'vendors/scrolling-tabs';

// ==========================================================================
// BASE
// ==========================================================================
// Reset, typography, base element styles
@import 'base/reset';
@import 'base/typography';
@import 'base/elements';

// ==========================================================================
// LAYOUT
// ==========================================================================
// Grid system, header, footer, navigation
@import 'layout/grid';
@import 'layout/container';
@import 'layout/header';
@import 'layout/footer';
@import 'layout/navigation';

// ==========================================================================
// COMPONENTS
// ==========================================================================
// Reusable UI components
@import 'components/navigation';
@import 'components/buttons';
@import 'components/forms';
@import 'components/cards';
@import 'components/hero';
@import 'components/partners';
@import 'components/international';
@import 'components/business-units';
@import 'components/contact';
@import 'components/language-switcher';
@import 'components/sliders';

// ==========================================================================
// PAGES
// ==========================================================================
// Page-specific styles
@import 'pages/home';
@import 'pages/about';
@import 'pages/business-units';
@import 'pages/partnerships';

// ==========================================================================
// THEMES
// ==========================================================================
// Theme variations (if needed)
// @import 'themes/dark';

// ==========================================================================
// UTILITIES
// ==========================================================================
// Utility classes for quick styling
@import 'utilities/spacing';
@import 'utilities/typography';
@import 'utilities/colors';
@import 'utilities/display';
@import 'utilities/flexbox';
@import 'utilities/animations';
