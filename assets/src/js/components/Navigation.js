/**
 * Navigation Component - Mobile-First
 * 
 * Modern slide-in navigation panel with progressive disclosure
 * Designed for touch-friendly interaction and accessibility
 */

class Navigation {
  constructor() {
    this.isOpen = false;
    this.activeSubmenu = null;
    this.focusableElements = [];
    this.lastFocusedElement = null;
    
    this.init();
  }

  init() {
    this.createElements();
    this.bindEvents();
    this.setupAccessibility();
    this.setupFocusManagement();
  }

  createElements() {
    // Get existing elements or create new ones
    this.header = document.querySelector('.navigation__header') || this.createHeader();
    this.toggle = document.querySelector('.navigation__toggle') || this.createToggle();
    this.panel = document.querySelector('.navigation__panel') || this.createPanel();
    this.overlay = document.querySelector('.navigation__overlay') || this.createOverlay();
    
    // Cache frequently used elements
    this.body = document.body;
    this.toggleLines = this.toggle.querySelectorAll('.navigation__toggle__line');
    this.menuLinks = this.panel.querySelectorAll('.navigation__menu__link');
    this.submenuToggles = this.panel.querySelectorAll('.navigation__submenu__toggle');
  }

  createHeader() {
    // This would be called if header doesn't exist
    // For now, assume header exists in HTML
    return document.querySelector('header');
  }

  createToggle() {
    const toggle = document.createElement('button');
    toggle.className = 'navigation__toggle';
    toggle.setAttribute('aria-label', 'Toggle navigation menu');
    toggle.setAttribute('aria-expanded', 'false');
    toggle.setAttribute('aria-controls', 'navigation-panel');
    
    // Create hamburger lines
    for (let i = 0; i < 3; i++) {
      const line = document.createElement('span');
      line.className = 'navigation__toggle__line';
      toggle.appendChild(line);
    }
    
    return toggle;
  }

  createPanel() {
    const panel = document.createElement('div');
    panel.className = 'navigation__panel';
    panel.id = 'navigation-panel';
    panel.setAttribute('role', 'navigation');
    panel.setAttribute('aria-label', 'Main navigation');
    
    return panel;
  }

  createOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'navigation__overlay';
    overlay.setAttribute('aria-hidden', 'true');
    
    return overlay;
  }

  bindEvents() {
    // Toggle button
    this.toggle.addEventListener('click', (e) => {
      e.preventDefault();
      this.toggleNavigation();
    });

    // Overlay click to close
    this.overlay.addEventListener('click', () => {
      this.closeNavigation();
    });

    // Submenu toggles
    this.submenuToggles.forEach(toggle => {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleSubmenu(toggle);
      });
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      this.handleKeydown(e);
    });

    // Close on escape
    document.addEventListener('keyup', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeNavigation();
      }
    });

    // Handle resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // Handle focus trap
    this.panel.addEventListener('keydown', (e) => {
      this.handleFocusTrap(e);
    });
  }

  setupAccessibility() {
    // Set initial ARIA states
    this.toggle.setAttribute('aria-expanded', 'false');
    this.panel.setAttribute('aria-hidden', 'true');
    
    // Add role and labels to submenu toggles
    this.submenuToggles.forEach(toggle => {
      toggle.setAttribute('aria-expanded', 'false');
      const submenuContent = toggle.nextElementSibling;
      if (submenuContent) {
        const submenuId = `submenu-${Math.random().toString(36).substr(2, 9)}`;
        submenuContent.id = submenuId;
        toggle.setAttribute('aria-controls', submenuId);
      }
    });
  }

  setupFocusManagement() {
    // Get all focusable elements in the panel
    this.updateFocusableElements();
  }

  updateFocusableElements() {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ];
    
    this.focusableElements = Array.from(
      this.panel.querySelectorAll(focusableSelectors.join(', '))
    ).filter(el => !el.hasAttribute('aria-hidden'));
  }

  toggleNavigation() {
    if (this.isOpen) {
      this.closeNavigation();
    } else {
      this.openNavigation();
    }
  }

  openNavigation() {
    this.isOpen = true;
    this.lastFocusedElement = document.activeElement;
    
    // Update classes and attributes
    this.toggle.classList.add('navigation__toggle--active');
    this.toggle.setAttribute('aria-expanded', 'true');
    this.panel.classList.add('navigation__panel--open');
    this.panel.setAttribute('aria-hidden', 'false');
    this.overlay.classList.add('navigation__overlay--visible');
    this.body.style.overflow = 'hidden';
    
    // Focus management
    setTimeout(() => {
      this.focusFirstElement();
    }, 100);
    
    // Announce to screen readers
    this.announceToScreenReader('Navigation menu opened');
  }

  closeNavigation() {
    this.isOpen = false;
    
    // Update classes and attributes
    this.toggle.classList.remove('navigation__toggle--active');
    this.toggle.setAttribute('aria-expanded', 'false');
    this.panel.classList.remove('navigation__panel--open');
    this.panel.setAttribute('aria-hidden', 'true');
    this.overlay.classList.remove('navigation__overlay--visible');
    this.body.style.overflow = '';
    
    // Close all submenus
    this.closeAllSubmenus();
    
    // Restore focus
    if (this.lastFocusedElement) {
      this.lastFocusedElement.focus();
    }
    
    // Announce to screen readers
    this.announceToScreenReader('Navigation menu closed');
  }

  toggleSubmenu(toggle) {
    const submenuContent = toggle.nextElementSibling;
    const arrow = toggle.querySelector('.navigation__submenu__arrow');
    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';
    
    if (isExpanded) {
      // Close submenu
      toggle.setAttribute('aria-expanded', 'false');
      toggle.classList.remove('navigation__submenu__toggle--active');
      submenuContent.classList.remove('navigation__submenu__content--expanded');
      if (arrow) arrow.classList.remove('navigation__submenu__arrow--expanded');
      this.activeSubmenu = null;
    } else {
      // Close other submenus first
      this.closeAllSubmenus();
      
      // Open this submenu
      toggle.setAttribute('aria-expanded', 'true');
      toggle.classList.add('navigation__submenu__toggle--active');
      submenuContent.classList.add('navigation__submenu__content--expanded');
      if (arrow) arrow.classList.add('navigation__submenu__arrow--expanded');
      this.activeSubmenu = toggle;
    }
    
    // Update focusable elements
    this.updateFocusableElements();
  }

  closeAllSubmenus() {
    this.submenuToggles.forEach(toggle => {
      const submenuContent = toggle.nextElementSibling;
      const arrow = toggle.querySelector('.navigation__submenu__arrow');
      
      toggle.setAttribute('aria-expanded', 'false');
      toggle.classList.remove('navigation__submenu__toggle--active');
      submenuContent.classList.remove('navigation__submenu__content--expanded');
      if (arrow) arrow.classList.remove('navigation__submenu__arrow--expanded');
    });
    
    this.activeSubmenu = null;
  }

  handleKeydown(e) {
    if (!this.isOpen) return;
    
    switch (e.key) {
      case 'Tab':
        // Handle tab navigation within panel
        break;
      case 'ArrowDown':
        e.preventDefault();
        this.focusNextElement();
        break;
      case 'ArrowUp':
        e.preventDefault();
        this.focusPreviousElement();
        break;
      case 'Home':
        e.preventDefault();
        this.focusFirstElement();
        break;
      case 'End':
        e.preventDefault();
        this.focusLastElement();
        break;
    }
  }

  handleFocusTrap(e) {
    if (e.key !== 'Tab') return;
    
    const firstElement = this.focusableElements[0];
    const lastElement = this.focusableElements[this.focusableElements.length - 1];
    
    if (e.shiftKey) {
      // Shift + Tab
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  handleResize() {
    // Close navigation on desktop breakpoint
    if (window.innerWidth >= 1024 && this.isOpen) {
      this.closeNavigation();
    }
  }

  focusFirstElement() {
    if (this.focusableElements.length > 0) {
      this.focusableElements[0].focus();
    }
  }

  focusLastElement() {
    if (this.focusableElements.length > 0) {
      this.focusableElements[this.focusableElements.length - 1].focus();
    }
  }

  focusNextElement() {
    const currentIndex = this.focusableElements.indexOf(document.activeElement);
    const nextIndex = (currentIndex + 1) % this.focusableElements.length;
    this.focusableElements[nextIndex].focus();
  }

  focusPreviousElement() {
    const currentIndex = this.focusableElements.indexOf(document.activeElement);
    const prevIndex = currentIndex === 0 ? this.focusableElements.length - 1 : currentIndex - 1;
    this.focusableElements[prevIndex].focus();
  }

  announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  // Public API
  open() {
    this.openNavigation();
  }

  close() {
    this.closeNavigation();
  }

  toggle() {
    this.toggleNavigation();
  }

  destroy() {
    // Clean up event listeners and restore state
    this.closeNavigation();
    // Remove event listeners...
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Navigation;
}

// Auto-initialize if DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.vimaNavigation = new Navigation();
  });
} else {
  window.vimaNavigation = new Navigation();
}
