document.addEventListener('DOMContentLoaded', function () {
  const languageSelector = document.getElementById('language-selector');
  const defaultLanguage = sessionStorage.getItem('language') || 'en'; // Check session storage for language

  languageSelector.value = defaultLanguage; // Set the selector to the stored language

  languageSelector.addEventListener('change', function () {
    const selectedLanguage = languageSelector.value;
    sessionStorage.setItem('language', selectedLanguage); // Store selected language in session storage
    loadTranslations(selectedLanguage);
  });

  function loadTranslations(language) {
    fetch(`assets/i18n/${language}.json`)
      .then((response) => response.json())
      .then((translations) => {
        console.log(translations); // Log the translations object
        updateTranslations(translations);
        console.log('Translations loaded:', translations);
      })
      .catch((error) => console.error('Error loading translations:', error));
  }

  function updateTranslations(translations) {
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach((element) => {
      const keys = element.getAttribute('data-i18n').split('.');
      let translation = translations;
      let keyExists = true; // Flag to check if key exists

      keys.forEach((key) => {
        if (translation && translation[key] !== undefined) {
          translation = translation[key];
        } else {
          keyExists = false; // Key does not exist
        }
      });

      if (keyExists) {
        element.textContent = translation;
      } else {
        console.warn(
          `Translation key not found: ${element.getAttribute('data-i18n')}`
        );
      }
    });
  }

  // Initialize with default language
  loadTranslations(defaultLanguage);
});
