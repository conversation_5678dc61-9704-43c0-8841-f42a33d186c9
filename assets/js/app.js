window.$ = window.jQuery = require('jquery');
require('popper.js');
require('bootstrap');
require('jquery-bootstrap-scrolling-tabs');

import {Swiper, Navigation, Pagination, Scrollbar, EffectCoverflow, Autoplay} from 'swiper';

Swiper.use([Navigation, Pagination, Scrollbar, EffectCoverflow, Autoplay]);

// Example starter JavaScript for disabling form submissions if there are invalid fields
(function () {
    'use strict';
    window.addEventListener('load', function () {
        // Fetch all the forms we want to apply custom Bootstrap validation styles to
        var forms = document.getElementsByClassName('needs-validation');
        // Loop over them and prevent submission
        var validation = Array.prototype.filter.call(forms, function (form) {
            form.addEventListener('submit', function (event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);

    document.querySelector('#toggle').addEventListener('click', function () {
        this.classList.toggle('active');
        document.querySelector('#overlay').classList.toggle('open');
        document.querySelector('body').classList.toggle('open-menu');
    });

    document.querySelector('.nav-button').addEventListener('click', function () {
        /*  Toggle the CSS closed class which reduces the height of the UL thus
            hiding all LI apart from the first */
        this.parentNode.parentNode.classList.toggle('closed');
    }, false);


// Swiper: Slider
    new Swiper('.swiper-container', {
        loop: true,
        slidesPerView: 2,
        paginationClickable: true,
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        spaceBetween: 20,
        autoplay: {
            delay: 3000,
        },
        breakpoints: {
            1920: {
                slidesPerView: 3,
                spaceBetween: 30
            },
            1028: {
                slidesPerView: 3,
                spaceBetween: 30
            },
            572: {
                slidesPerView: 2,
                spaceBetween: 10
            }
        }
    });


})();
