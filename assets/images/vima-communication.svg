<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="569.649" height="450.003" viewBox="0 0 569.649 450.003">
  <defs>
    <filter id="Path_14217" x="226.313" y="59.261" width="173.247" height="252.578" filterUnits="userSpaceOnUse">
      <feOffset dx="3" dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.353"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_14278" x="223.313" y="99.523" width="68.051" height="75.794" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.251"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient" x1="0.5" x2="1.033" y2="1.116" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e1f2fe"/>
      <stop offset="1" stop-color="#a9ecfc"/>
    </linearGradient>
    <filter id="Path_14228" x="306.867" y="87.846" width="154.302" height="239.302" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-3"/>
    </filter>
    <filter id="Path_14228-2" x="306.867" y="87.846" width="154.302" height="239.302" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-4"/>
      <feFlood result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur-4"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
    <filter id="Rectangle_3061" x="270.202" y="0" width="153.175" height="105.109" filterUnits="userSpaceOnUse">
      <feOffset dy="30" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-5"/>
      <feFlood flood-opacity="0.043"/>
      <feComposite operator="in" in2="blur-5"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Rectangle_3060" x="49.332" y="161.819" width="153.175" height="105.109" filterUnits="userSpaceOnUse">
      <feOffset dy="30" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-6"/>
      <feFlood flood-opacity="0.043"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_14188" x="194.528" y="230.863" width="84.818" height="109.38" filterUnits="userSpaceOnUse">
      <feOffset dy="20" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-7"/>
      <feFlood flood-opacity="0.102"/>
      <feComposite operator="in" in2="blur-7"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_14190" x="265.958" y="340.623" width="84.817" height="109.38" filterUnits="userSpaceOnUse">
      <feOffset dy="20" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-8"/>
      <feFlood flood-opacity="0.102"/>
      <feComposite operator="in" in2="blur-8"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_14193" x="127.225" y="352.855" width="81.279" height="96.583" filterUnits="userSpaceOnUse">
      <feOffset dy="20" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur-9"/>
      <feFlood flood-opacity="0.102"/>
      <feComposite operator="in" in2="blur-9"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_16459" data-name="Group 16459" transform="translate(-614.997 -5650.46)">
    <path id="Path_14277" data-name="Path 14277" d="M-37.24,126.73-76.175,152.3-339.353-16.74l41.108-24.374Z" transform="translate(1137.025 5861.699)" opacity="0.111"/>
    <g id="Group_16458" data-name="Group 16458" transform="translate(706.997 5747.734)">
      <g id="Group_16435" data-name="Group 16435">
        <g id="Group_14343" data-name="Group 14343" transform="translate(-92 179.502)" opacity="0.316">
          <g id="Group_13480" data-name="Group 13480">
            <path id="Path_548" data-name="Path 548" d="M-143.313,53.466-149.8,42.208l6.488-11.262H-130.3l6.5,11.262-6.5,11.258Z" transform="translate(149.801 -30.947)" fill="#40ebf4" opacity="0.6"/>
            <path id="Path_549" data-name="Path 549" d="M-143.313,55.724-149.8,44.467l6.488-11.262H-130.3l6.5,11.262-6.5,11.258Z" transform="translate(149.801 -15.656)" fill="#00b0d4" opacity="0.5"/>
            <path id="Path_550" data-name="Path 550" d="M-143.313,57.984-149.8,46.726l6.488-11.262H-130.3l6.5,11.262-6.5,11.258Z" transform="translate(149.801 -0.363)" fill="#00b0d4" opacity="0.4"/>
            <path id="Path_551" data-name="Path 551" d="M-143.313,60.242-149.8,48.984l6.488-11.262H-130.3l6.5,11.262-6.5,11.258Z" transform="translate(149.801 14.928)" fill="#2cc9d5" opacity="0.3"/>
            <path id="Path_552" data-name="Path 552" d="M-143.313,62.5-149.8,51.247l6.488-11.266H-130.3l6.5,11.266L-130.3,62.5Z" transform="translate(149.801 30.218)" fill="#a9ecfc" opacity="0.2"/>
            <path id="Path_553" data-name="Path 553" d="M-143.313,64.759-149.8,53.5l6.488-11.262H-130.3l6.5,11.262-6.5,11.258Z" transform="translate(149.801 45.512)" fill="#e1f2fe" opacity="0.1"/>
            <path id="Path_554" data-name="Path 554" d="M-143.313,67.017-149.8,55.763l6.488-11.265H-130.3l6.5,11.265-6.5,11.255Z" transform="translate(149.801 60.803)" fill="#18a6b5" opacity="0"/>
          </g>
          <path id="Path_555" data-name="Path 555" d="M-143.313,53.466-149.8,42.208l6.488-11.262H-130.3l6.5,11.262-6.5,11.258Z" transform="translate(149.801 -30.947)" fill="#02b49c" opacity="0.5"/>
        </g>
        <g id="Group_16418" data-name="Group 16418" transform="translate(104.317)">
          <g id="Group_16243" data-name="Group 16243" transform="translate(50.72 5.152)">
            <g id="Group_16236" data-name="Group 16236" transform="translate(-14.724 -37.164)">
              <g transform="matrix(1, 0, 0, 1, -232.31, -65.26)" filter="url(#Path_14217)">
                <path id="Path_14217-2" data-name="Path 14217" d="M420.6,301.525,265.353,195.147V66.948L420.6,173.327Z" transform="translate(-33.04 -1.69)" fill="#e6effa"/>
              </g>
              <path id="Path_14219" data-name="Path 14219" d="M360.648,188.342l40.559,23.417V164.925l-40.559-23.417Z" transform="translate(-259.096 -39.48)" fill="#b9d0eb"/>
              <path id="Path_14221" data-name="Path 14221" d="M273.474,164.5l33.106,19.114V145.39l-33.106-19.114Z" transform="translate(-265.628 -37.659)" fill="#ffa391"/>
              <path id="Path_14222" data-name="Path 14222" d="M314.055,192.92l56.268,32.485c3.067,1.772,5.553-.109,5.553-4.2V191.925c0-4.089-2.486-8.841-5.553-10.613l-56.268-32.485c-3.067-1.772-5.553.109-5.553,4.2v29.283C308.5,186.4,310.988,191.149,314.055,192.92Z" transform="translate(-264.607 -35.665)" fill="#a9ecfc"/>
              <path id="Path_14223" data-name="Path 14223" d="M373.746,154.342l-63.088-36.424a5.051,5.051,0,0,1-2.1-4.016h0c0-1.542.945-2.257,2.1-1.59l63.088,36.424a5.054,5.054,0,0,1,2.1,4.016h0C375.848,154.295,374.9,155.009,373.746,154.342Z" transform="translate(-264.579 -49.373)" fill="#4a99ba"/>
              <path id="Path_14224" data-name="Path 14224" d="M373.746,172.308l-63.088-36.424a5.049,5.049,0,0,1-2.1-4.016h0c0-1.541.945-2.257,2.1-1.588L373.746,166.7a5.049,5.049,0,0,1,2.1,4.016h0C375.848,172.261,374.9,172.975,373.746,172.308Z" transform="translate(-264.579 -42.373)" fill="#4a99ba"/>
              <path id="Path_14225" data-name="Path 14225" d="M357.939,154.2l-47.3-27.311a5.053,5.053,0,0,1-2.1-4.016h0c0-1.541.947-2.255,2.1-1.588l47.3,27.311a5.052,5.052,0,0,1,2.1,4.016h0C360.039,154.15,359.094,154.866,357.939,154.2Z" transform="translate(-262.221 -45.565)" fill="#63ccf8"/>
              <path id="Path_14226" data-name="Path 14226" d="M340.042,161.831l-29.437-17a5.051,5.051,0,0,1-2.1-4.016h0c0-1.541.945-2.257,2.1-1.59l29.438,17a5.053,5.053,0,0,1,2.1,4.016h0C342.144,161.784,341.2,162.5,340.042,161.831Z" transform="translate(-260.098 -38.036)" fill="#63ccf8"/>
              <g transform="matrix(1, 0, 0, 1, -232.31, -65.26)" filter="url(#Path_14278)">
                <path id="Path_14278-2" data-name="Path 14278" d="M14.449,0C22.428,0,28.9,11.2,28.9,25.026S22.428,50.051,14.448,50.051,0,38.847,0,25.026,6.469,0,14.449,0Z" transform="translate(232.31 119.97) rotate(-30)" fill="#e1f2fe"/>
              </g>
            </g>
            <g id="Group_16245" data-name="Group 16245" transform="translate(73.33 -0.785)">
              <g data-type="innerShadowGroup">
                <g transform="matrix(1, 0, 0, 1, -320.37, -101.64)" filter="url(#Path_14228)">
                  <path id="Path_14228-3" data-name="Path 14228" d="M471.19,207.166l-127.3-73.5V265.884l127.3,73.5Z" transform="translate(-23.52 -32.03)" stroke="#a9ecfc" stroke-width="9" fill="url(#linear-gradient)"/>
                </g>
                <path id="Path_14228-4" data-name="Path 14228" d="M471.19,207.166l-127.3-73.5V265.884l127.3,73.5Z" transform="translate(-343.888 -133.668)" fill="url(#linear-gradient)"/>
                <g transform="matrix(1, 0, 0, 1, -320.37, -101.64)" filter="url(#Path_14228-2)">
                  <path id="Path_14228-5" data-name="Path 14228" d="M471.19,207.166l-127.3-73.5V265.884l127.3,73.5Z" transform="translate(-23.52 -32.03)" fill="#fff"/>
                </g>
                <path id="Path_14228-6" data-name="Path 14228" d="M471.19,207.166l-127.3-73.5V265.884l127.3,73.5Z" transform="translate(-343.888 -133.668)" fill="none" stroke="#a9ecfc" stroke-width="9"/>
              </g>
              <path id="Path_14230" data-name="Path 14230" d="M356.153,237.367l53.6,30.947c2.922,1.686,5.292-.1,5.292-4v-27.9c0-3.9-2.37-8.422-5.292-10.11l-53.6-30.945c-2.922-1.688-5.292.1-5.292,4v27.894C350.861,231.153,353.231,235.679,356.153,237.367Z" transform="translate(-340.699 -105.742)" fill="#63ccf8"/>
              <path id="Path_14231" data-name="Path 14231" d="M413.017,198.906l-60.1-34.7a4.814,4.814,0,0,1-2-3.826h0c0-1.468.9-2.15,2-1.513l60.1,34.7a4.81,4.81,0,0,1,2,3.826h0C415.019,198.861,414.119,199.543,413.017,198.906Z" transform="translate(-340.674 -122.251)" fill="#ff8067"/>
              <path id="Path_14232" data-name="Path 14232" d="M413.017,216.874l-60.1-34.7a4.814,4.814,0,0,1-2-3.826h0c0-1.468.9-2.15,2-1.513l60.1,34.7a4.812,4.812,0,0,1,2,3.826h0C415.019,216.827,414.119,217.509,413.017,216.874Z" transform="translate(-340.674 -114.034)" fill="#ff8067"/>
              <path id="Path_14233" data-name="Path 14233" d="M397.956,199.2l-45.061-26.017a4.814,4.814,0,0,1-2-3.826h0c0-1.469.9-2.15,2-1.514l45.061,26.017a4.812,4.812,0,0,1,2,3.826h0C399.958,199.148,399.057,199.83,397.956,199.2Z" transform="translate(-340.685 -118.149)" fill="#ff8067"/>
              <path id="Path_14234" data-name="Path 14234" d="M380.906,207.317l-28.042-16.191a4.814,4.814,0,0,1-2-3.826h0c0-1.468.9-2.15,2-1.513l28.042,16.19a4.812,4.812,0,0,1,2,3.826h0C382.908,207.272,382.007,207.953,380.906,207.317Z" transform="translate(-340.699 -109.94)" fill="#ff8067"/>
              <path id="Path_14235" data-name="Path 14235" d="M432.671,206.593v-.031a4.906,4.906,0,0,0-2.4-4.312l-21.034-12.143a4.8,4.8,0,0,0-7.2,4.155V277.2a4.8,4.8,0,0,0,2.4,4.155L425.475,293.5a4.8,4.8,0,0,0,7.2-4.155Z" transform="translate(-317.29 -108.152)" fill="#ffe97c"/>
            </g>
            <g id="Group_16242" data-name="Group 16242" transform="translate(9.095 110.687)" opacity="0">
              <g id="Group_16241" data-name="Group 16241" transform="translate(64.977 17.742)">
                <path id="Path_14260" data-name="Path 14260" d="M319.363,226.263c-1.533-1.108-4.806-.183-4.806-.183l.009.014a3.6,3.6,0,0,0-4.216,3.735c-.113,3.766,2.36,6.666,5.891,6.334a5.788,5.788,0,0,0,3.339-1.029l.014-.01a6.474,6.474,0,0,0,2.316-3.389C322.317,230.323,322.288,228.381,319.363,226.263Z" transform="translate(-264.513 -225.718)" fill="#ffc49c"/>
                <path id="Path_14261" data-name="Path 14261" d="M322.278,230.917a11.973,11.973,0,0,1,1.922,5.641l-10.5,9.015a35.414,35.414,0,0,1-7.955,5.161L284.1,258.916A7.257,7.257,0,0,1,274.4,254a8.136,8.136,0,0,1-.138-.828h0c-.444-3.933,2.146-7.36,5.757-7.617l21.241-6.983,18.153-10.434S320.825,228.549,322.278,230.917Z" transform="translate(-274.209 -225.071)" fill="#2e456e"/>
              </g>
              <path id="Path_14262" data-name="Path 14262" d="M291.009,354.193s.425,7.3-1.594,9.061-21.043,2.9-21.761-1.107,10.491-5.929,10.491-5.929Z" transform="translate(-211 -173.508)" fill="#543b3a"/>
              <path id="Path_14263" data-name="Path 14263" d="M292.392,294.216l-2.643,78.142s-4.69,4.519-12.864,2.024l-4.57-82.961a97.9,97.9,0,0,0,16.1-2.586c2.572-.684,1.386-1.682,3.443-2.348Z" transform="translate(-209.74 -191.673)" fill="#20304d"/>
              <path id="Path_14264" data-name="Path 14264" d="M277.228,356.963s.042,7.423-2.037,9.115-20.855,1.984-21.354-2.113,10.63-5.534,10.63-5.534Z" transform="translate(-214.702 -172.765)" fill="#6b4b4a"/>
              <path id="Path_14265" data-name="Path 14265" d="M281.658,296.157l-6.638,79.107s-4.847,4.369-12.762,1.469l-.211-84.324a94.009,94.009,0,0,0,15.972-1.89c2.566-.577,4.915-1.187,6.973-1.769Z" transform="translate(-212.494 -191.066)" fill="#273b5e"/>
              <path id="Path_14266" data-name="Path 14266" d="M275.02,246.353h0c-6.3.14-11.932-5.28-12.517-12.046l-.672-7.768c-.585-6.765,4.091-12.415,10.391-12.555h0c6.3-.141,11.932,5.28,12.517,12.045l.672,7.768C286,240.562,281.319,246.212,275.02,246.353Z" transform="translate(-212.566 -211.126)" fill="#ffc49c"/>
              <path id="Path_14268" data-name="Path 14268" d="M286.1,228.551a12.982,12.982,0,0,0-12.607-16.815l-.119,0a11.223,11.223,0,0,0-4.391.7c-4.9,1.514-6.126,4.924-6.127,4.927a14.889,14.889,0,0,0-1.909,11.161s1.589,11.952,6.029,15.041,14.039-3.051,11.984-10.158,2.668-9.657,3.109-5.676.9,6.528,1.867,6.122a4.059,4.059,0,0,0,1.168-2.135l0,.006C285.43,230.734,285.765,229.659,286.1,228.551Z" transform="translate(-212.877 -211.73)" fill="#2e456e"/>
              <path id="Path_14271" data-name="Path 14271" d="M229.972,273.337l12.758-11.274a3.505,3.505,0,0,0,.879-3.032L238.327,233c-.214-1.057-.955-1.412-1.655-.793l-12.758,11.274a3.505,3.505,0,0,0-.879,3.032l5.281,26.036C228.531,273.6,229.272,273.956,229.972,273.337Z" transform="translate(-222.977 -206.319)" fill="#20304d"/>
              <path id="Path_14272" data-name="Path 14272" d="M230.233,267.7l9.877-8.728a2.713,2.713,0,0,0,.68-2.348L236.7,236.464c-.166-.818-.739-1.093-1.281-.614l-9.877,8.728a2.717,2.717,0,0,0-.681,2.348l4.089,20.156C229.119,267.9,229.692,268.175,230.233,267.7Z" transform="translate(-222.483 -205.322)" fill="#fff"/>
              <path id="Path_14273" data-name="Path 14273" d="M230.927,269.037a12.48,12.48,0,0,1-4.371-3.325c-1.544-1.815-.644-4.634.6-5.633a7.493,7.493,0,0,1,7.006-1.1l-.008.019c2.887.81,4.773,2.247,4.887,5.261.124,3.308-1.661,5.461-4.6,5.414h0a7.879,7.879,0,0,1-3.5-.63Z" transform="translate(-222.235 -199.167)" fill="#ffc49c"/>
              <path id="Path_14274" data-name="Path 14274" d="M234.018,267.591a13.566,13.566,0,0,0,.58,6.189l19.293,4.477c2.454.391,4.391-.5,6.374-2.529l18.19-18.952a7.762,7.762,0,0,0-2.433-9.91h0a6.165,6.165,0,0,0-9.061,1.991l-13.88,16.982-17.675-2.105S234.337,264.723,234.018,267.591Z" transform="translate(-220.042 -202.622)" fill="#2e456e"/>
              <path id="Path_14275" data-name="Path 14275" d="M233.778,239.344,228.1,244.21c-.6.511-1.278.249-1.514-.583h0a2.564,2.564,0,0,1,.655-2.444l5.676-4.868c.6-.511,1.278-.249,1.516.583h0A2.57,2.57,0,0,1,233.778,239.344Z" transform="translate(-222.031 -205.204)" fill="#d1d1d1"/>
              <path id="Path_14276" data-name="Path 14276" d="M322.994,225.824l-7.186,3.544a1.518,1.518,0,0,1-2.027-.689h0a1.516,1.516,0,0,1,.687-2.027l7.186-3.545a1.518,1.518,0,0,1,2.027.689h0A1.519,1.519,0,0,1,322.994,225.824Z" transform="translate(-198.657 -208.719)" fill="#ffc49c"/>
            </g>
          </g>
          <g id="Group_16460" data-name="Group 16460" transform="translate(8 -173.441)">
            <g transform="matrix(1, 0, 0, 1, -204.32, 76.17)" filter="url(#Rectangle_3061)">
              <path id="Rectangle_3061-2" data-name="Rectangle 3061" d="M30,0H93.175a30,30,0,0,1,30,30v.109a30,30,0,0,1-30,30H0a0,0,0,0,1,0,0V30A30,30,0,0,1,30,0Z" transform="translate(285.2)" fill="#fff"/>
            </g>
            <g id="Group_16233" data-name="Group 16233" transform="translate(107.019 100.672)">
              <ellipse id="Ellipse_36" data-name="Ellipse 36" cx="6.405" cy="6.405" rx="6.405" ry="6.405" transform="translate(0 0)" fill="#020202"/>
              <ellipse id="Ellipse_37" data-name="Ellipse 37" cx="6.405" cy="6.405" rx="6.405" ry="6.405" transform="translate(30.547 0.985)" fill="#020202"/>
              <ellipse id="Ellipse_38" data-name="Ellipse 38" cx="6.405" cy="6.405" rx="6.405" ry="6.405" transform="translate(60.109 0.985)" fill="#020202"/>
            </g>
          </g>
          <g transform="matrix(1, 0, 0, 1, -196.32, -97.27)" filter="url(#Rectangle_3060)">
            <path id="Rectangle_3060-2" data-name="Rectangle 3060" d="M30,0H93.175a30,30,0,0,1,30,30v.109a30,30,0,0,1-30,30H0a0,0,0,0,1,0,0V30A30,30,0,0,1,30,0Z" transform="translate(64.33 161.82)" fill="#fff"/>
          </g>
          <g id="Group_16232" data-name="Group 16232" transform="translate(-113.876 86.325)">
            <ellipse id="Ellipse_36-2" data-name="Ellipse 36" cx="6.405" cy="6.405" rx="6.405" ry="6.405" transform="translate(0 0)" fill="#020202"/>
            <ellipse id="Ellipse_37-2" data-name="Ellipse 37" cx="6.405" cy="6.405" rx="6.405" ry="6.405" transform="translate(30.547 0.985)" fill="#020202"/>
            <ellipse id="Ellipse_38-2" data-name="Ellipse 38" cx="6.405" cy="6.405" rx="6.405" ry="6.405" transform="translate(60.109 0.985)" fill="#020202"/>
          </g>
        </g>
      </g>
      <g id="Group_16244" data-name="Group 16244" transform="translate(50.225 133.589)">
        <g id="Group_16188" data-name="Group 16188" transform="translate(0 0)">
          <g id="Group_16179" data-name="Group 16179" transform="translate(67.303 0)">
            <g id="Group_16176" data-name="Group 16176">
              <g transform="matrix(1, 0, 0, 1, -209.53, -230.86)" filter="url(#Path_14188)">
                <path id="Path_14188-2" data-name="Path 14188" d="M1652.1,516.386a20.714,20.714,0,1,0-24.552,0,23.781,23.781,0,0,0-15.133,22.15V553.4h54.818v-14.86A23.782,23.782,0,0,0,1652.1,516.386Z" transform="translate(-1402.89 -248.15)" fill="#e1f2fe"/>
              </g>
            </g>
            <g id="Group_16178" data-name="Group 16178" transform="translate(6.916)">
              <g id="Group_16177" data-name="Group 16177">
                <path id="Path_14189" data-name="Path 14189" d="M1640.446,479.016a20.7,20.7,0,0,0-20.493,17.844c.417,0,15.515.061,20.493-9.227,0,0,4.869,9.2,20.491,9.227A20.7,20.7,0,0,0,1640.446,479.016Z" transform="translate(-1619.952 -479.016)" fill="#47748c"/>
              </g>
            </g>
          </g>
          <g id="Group_16183" data-name="Group 16183" transform="translate(138.733 109.76)">
            <g id="Group_16180" data-name="Group 16180">
              <g transform="matrix(1, 0, 0, 1, -280.96, -340.62)" filter="url(#Path_14190)">
                <path id="Path_14190-2" data-name="Path 14190" d="M1729.945,636a20.714,20.714,0,1,0-24.553,0,23.781,23.781,0,0,0-15.132,22.15v14.86h54.817v-14.86A23.782,23.782,0,0,0,1729.945,636Z" transform="translate(-1409.3 -258.01)" fill="#e1f2fe"/>
              </g>
            </g>
            <g id="Group_16182" data-name="Group 16182" transform="translate(6.915)">
              <g id="Group_16181" data-name="Group 16181">
                <path id="Path_14191" data-name="Path 14191" d="M1718.289,598.633a20.7,20.7,0,0,0-20.494,17.845c.418,0,15.516.06,20.494-9.227,0,0,4.868,9.2,20.491,9.227A20.7,20.7,0,0,0,1718.289,598.633Z" transform="translate(-1697.796 -598.633)" fill="#59595b"/>
              </g>
            </g>
          </g>
          <g id="Group_16187" data-name="Group 16187" transform="translate(0 110.324)">
            <g id="Group_16184" data-name="Group 16184" transform="translate(3.77)">
              <path id="Path_14192" data-name="Path 14192" d="M1543.177,619.352v14.017a19.985,19.985,0,0,0,2.539,9.765,22.257,22.257,0,0,1,7.824-5.2,19.036,19.036,0,0,1-7.673-18.245c2.407-.05,14.713-.685,19.044-8.448V599.248h-1.631A20.1,20.1,0,0,0,1543.177,619.352Z" transform="translate(-1543.177 -599.248)" fill="#d67729"/>
            </g>
            <g id="Group_16185" data-name="Group 16185" transform="translate(0 11.668)">
              <g transform="matrix(1, 0, 0, 1, -142.23, -352.85)" filter="url(#Path_14193)">
                <path id="Path_14193-2" data-name="Path 14193" d="M1584,644.027a22.374,22.374,0,0,0-7.866-5.231,19.409,19.409,0,0,0,7.922-15.641,19.642,19.642,0,0,0-.209-2.7c-2.42-.05-14.79-.689-19.145-8.492-4.355,7.8-16.725,8.442-19.145,8.492a19.623,19.623,0,0,0-.21,2.7,19.407,19.407,0,0,0,7.923,15.641,22.4,22.4,0,0,0-14.208,20.8v13.953h51.279V659.594A22.225,22.225,0,0,0,1584,644.027Z" transform="translate(-1396.84 -259.11)" fill="#e1f2fe"/>
              </g>
            </g>
            <g id="Group_16186" data-name="Group 16186" transform="translate(25.505)">
              <path id="Path_14194" data-name="Path 14194" d="M1568.77,599.248h-1.906v3.189c.032,0,.065,0,.1,0H1567a.243.243,0,0,1,.044,0c.045,0,.087.007.135.01-.06,0-.119-.01-.179-.01-.045,0-.09.006-.135.007v.583c.032,0,.065,0,.1,0l.04,0-.138.007v8.208c.03-.057.068-.108.1-.165,0,0,.022.033.04.063.015-.022.029-.041.041-.063,0,0,3.086,5.172,11.29,7.5.067.019.13.039.2.056.31.087.626.167.952.244.439.1.892.2,1.361.283.219.042.439.081.663.117.594.094,1.2.178,1.839.24l.382.036c.748.064,1.519.11,2.321.127h.054l.083,0c.163,0,.325.01.492.01h0c-.167,0-.326-.005-.492-.01a19.058,19.058,0,0,1-7.673,18.252,22.225,22.225,0,0,1,7.777,5.155c.016.017.033.032.05.049a19.985,19.985,0,0,0,2.537-9.765V619.352A20.1,20.1,0,0,0,1568.77,599.248Z" transform="translate(-1566.864 -599.248)" fill="#d67729"/>
            </g>
          </g>
        </g>
        <g id="Group_16224" data-name="Group 16224" transform="translate(57.458 86.319)">
          <path id="Path_14208" data-name="Path 14208" d="M1649.64,605.461h-4.249v-2.124h3.99l-1.476-2.557,1.84-1.062,2.125,3.68-1.84,1.063-.389-.675Zm-6.374,0h-4.249v-2.124h4.249Zm-6.373,0h-4.249v-2.124h4.249Zm-6.374,0h-4.249v-2.124h4.249Zm-6.373,0H1619.9v-2.124h4.249Zm-6.374,0h-4.249v-2.124h4.249Zm-6.373,0h-4.249v-2.124h4.249Zm-6.374,0h-4.249v-2.124h4.249Zm-6.374,0H1594.4v-2.124h4.249Zm-6.373,0h-4.249v-2.124h4.249Zm-6.374,0h-4l-.92-1.593,2.124-3.68,1.84,1.062-1.2,2.086h2.161Zm.1-6.051-1.84-1.062,2.125-3.68,1.84,1.063Zm60.833-.471-2.125-3.68,1.84-1.062,2.125,3.68Zm-57.646-5.049-1.84-1.063,2.125-3.68,1.84,1.063Zm54.459-.471-2.125-3.68,1.84-1.063,2.124,3.68Zm-51.272-5.049-1.84-1.063,2.124-3.68,1.84,1.062Zm48.085-.471-2.124-3.68,1.84-1.063,2.124,3.68Zm-44.9-5.049-1.84-1.062,2.124-3.68,1.84,1.062Zm41.712-.47-2.124-3.68,1.84-1.062,2.125,3.68Zm-38.525-5.049-1.84-1.062,2.125-3.68,1.84,1.063Zm35.339-.471-2.125-3.68,1.84-1.062,2.125,3.68Zm-32.152-5.049-1.84-1.063,2.124-3.68,1.84,1.062Zm28.965-.471-2.125-3.68,1.84-1.062,2.124,3.68Zm-25.778-5.049-1.84-1.062,2.124-3.68,1.84,1.062Zm22.591-.47-2.124-3.68,1.84-1.063,2.124,3.68Zm-19.4-5.049-1.84-1.062,2.125-3.68,1.84,1.062Zm16.218-.471-2.124-3.68,1.84-1.062,2.125,3.68Zm-13.031-5.049-1.84-1.062,2.124-3.68,1.84,1.063Zm9.845-.471-2.125-3.68,1.84-1.062,2.125,3.68Zm-6.658-5.049-1.84-1.063,2.124-3.68,1.081.624-.019-.032,1.84-1.063L1620,548.2l-1.84,1.062-1.6-2.771Z" transform="translate(-1580.984 -544.521)" fill="#1a475a"/>
        </g>
      </g>
    </g>
  </g>
</svg>
