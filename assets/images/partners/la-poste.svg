<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="243" height="111" viewBox="0 0 243 111">
  <defs>
    <pattern id="pattern" preserveAspectRatio="none" width="100%" height="100%" viewBox="0 0 150 57">
      <image width="150" height="57" xlink:href="data:image/png;base64,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"/>
    </pattern>
    <filter id="header" x="0" y="0" width="243" height="111" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#header)">
    <rect id="header-2" data-name="header" width="213" height="81" transform="translate(15 12)" fill="url(#pattern)"/>
  </g>
</svg>
