<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="195" height="87" viewBox="0 0 195 87">
  <defs>
    <filter id="QaBou" x="0" y="0" width="195" height="87" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#QaBou)">
    <text id="QaBou-2" data-name="QaBou" transform="translate(15 57)" font-size="47" font-family="Montserrat-Medium, Montserrat" font-weight="500"><tspan x="0" y="0">QaBou</tspan></text>
  </g>
</svg>
