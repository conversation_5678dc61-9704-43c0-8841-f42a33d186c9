.scrtabs-tab-container * {
  box-sizing: border-box;
}

.scrtabs-tab-container {
  margin-top: 25px;
  height: auto;
  display: flex;
  @media(min-width: 992px) {
    margin-top: 60px;
  }

  .nav-tabs {
    border: 0;
    margin-bottom: 25px;
    padding-right: 0;
    max-width: 100%;
    @media(min-width: 992px) {
      margin-bottom: 65px;
    }

    .nav-link {
      border: 0;

      &.active {
        color: $blue;
        background: transparent;
        border: 0;

        &:after {
          content: '';
          width: 100%;
          height: 6px;
          border-radius: 8px;
          background: transparent linear-gradient(90deg, #A9ECFC 0%, #3490B8 100%) 0% 0% no-repeat;
          display: block;
          @media(min-width: 992px) {
            height: 15px;
          }
        }
      }

      &:hover,
      &:focus {
        border: 0;
        color: $blue;
      }
    }

    .exception {
      @media(min-width: 992px) {
        min-width: 215px;
        text-align: center;
      }
    }
  }
}

.scrtabs-tab-container .tab-content {
  clear: left;
}

.scrtabs-tab-container .scrtabs-tabs-movable-container > .navbar-nav {
  -ms-flex-direction: row;
  flex-direction: row;
}

.scrtabs-tabs-fixed-container {
  float: left;
  overflow: hidden;
  width: 100%;
  height: auto;
}

.scrtabs-tabs-movable-container {
  position: relative;
  transition: transform 2s;
}

.scrtabs-tabs-movable-container .tab-content {
  display: none;
}

.scrtabs-tab-container.scrtabs-rtl .scrtabs-tabs-movable-container > ul.nav-tabs {
  padding-right: 0;
}

.scrtabs-tab-scroll-arrow {
  border: 0;
  border-top: none;
  cursor: pointer;
  display: none;
  float: left;
  font-size: 12px;
  height: 100%;
  padding-left: 0.5em;
  padding-top: 1.3em;
  width: 20px;
}

.scrtabs-tab-scroll-arrow:hover {
  background-color: transparent;
}

.scrtabs-tab-scroll-arrow > span {
  border-right: 3px solid $blue;
  border-bottom: 3px solid $blue;
  display: block;
  width: 8px;
  height: 8px;
  transform: rotate(-45deg);
}

.scrtabs-tab-scroll-arrow > span:hover {
  border-right-color: $blue;
  border-bottom-color: $blue;
}

.scrtabs-tab-scroll-arrow-left > span {
  transform: rotate(135deg);
}

.scrtabs-tab-scroll-arrow-right {
  padding-left: 0.4em;
}

.scrtabs-tab-scroll-arrow,
.scrtabs-tab-scroll-arrow .scrtabs-click-target {
  cursor: pointer;
}

.scrtabs-tab-scroll-arrow.scrtabs-with-click-target {
  cursor: default;
}

.scrtabs-tab-scroll-arrow.scrtabs-disable,
.scrtabs-tab-scroll-arrow.scrtabs-disable .scrtabs-click-target {
  color: #ddd;
  cursor: default;
}

.scrtabs-tab-scroll-arrow.scrtabs-disable > span,
.scrtabs-tab-scroll-arrow.scrtabs-disable .scrtabs-click-target > span {
  border-color: #ddd;
}

.scrtabs-tab-scroll-arrow.scrtabs-disable:hover {
  background-color: initial;
}

.scrtabs-tabs-fixed-container ul.nav-tabs > li {
  white-space: nowrap;
}

.nav-tabs .dropdown-menu {
  border-top-color: transparent;
  margin-top: 0;
}
