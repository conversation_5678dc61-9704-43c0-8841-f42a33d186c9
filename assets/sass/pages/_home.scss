/*
  • banner
  ---------- ---------- ---------- ---------- ----------
*/

.banner {
  padding: 0 0 100px;
  position: relative;
  background: $white;
  @media (min-width: 992px) {
    padding: 100px 0;
  }

  &__image {
    margin-top: 25px;
    @media (min-width: 992px) {
      margin-top: 0;
    }

    img {
      @media (min-width: 992px) {
        position: absolute;
        top: -100px;
        left: -30px;
      }
    }
  }

  a {
    margin-top: 30px;
    @media (max-width: 992px) {
      margin-top: 60px;
    }
  }

  h1 {
    position: relative;
    @media (max-width: 992px) {
      font-size: 28px;
    }

    &:before {
      content: '';
      width: 62px;
      height: 134px;
      position: absolute;
      background: url('../../images/quote-open.svg');
      left: -70px;
      top: -30px;
      @media (max-width: 992px) {
        width: 23px;
        height: 34px;
        left: -30px;
        top: -10px;
        background-size: cover;
      }
    }

    .last-word {
      position: relative;

      &:after {
        content: '';
        width: 62px;
        height: 134px;
        position: absolute;
        background: url('../../images/quote-close.svg');
        bottom: -10px;
        right: -90px;
        @media (max-width: 992px) {
          width: 23px;
          height: 54px;
          bottom: -5px;
          right: -35px;
          background-size: cover;
        }
      }
    }
  }
}

/*
  • partners
  ---------- ---------- ---------- ---------- ----------
*/

.partners {
  padding: 30px 0 0;
  background: $white;
  position: relative;
  height: 258px;
  box-shadow: -5px 1px 33px -24px rgba(0, 0, 0, 0.69);
  -webkit-box-shadow: -5px 1px 33px -24px rgba(0, 0, 0, 0.69);
  -moz-box-shadow: -5px 1px 33px -24px rgba(0, 0, 0, 0.69);
  @media (min-width: 992px) {
    padding: 60px 0 0;
    height: 438px;
  }

  &:before {
    content: '';
    background: url('../../images/image-slider.svg') no-repeat;
    position: absolute;
    top: 0;
    width: 358px;
    height: 100%;
    z-index: 0;
    @media (min-width: 992px) {
      height: 438px;
    }
  }

  img {
    filter: grayscale(100%);
    max-height: 120px;

    &:hover {
      filter: none;
    }
  }

  h2 {
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
    @media (min-width: 992px) {
      margin-bottom: 60px;
    }
  }

  &__container {
    .container {
      @media (min-width: 1200px) {
        max-width: 1200px;
      }
    }
  }

  .view-link {
    text-align: center;
    position: relative;
    @media (min-width: 992px) {
      margin-top: 50px;
    }

    a {
      font-size: 18px;
      font-family: $bold-font;
      text-decoration: underline;

      &:hover {
        text-decoration: none;
        color: $blue-darker;
      }
    }
  }
}

/*
  • Slider
  ---------- ---------- ---------- ---------- ----------
*/

.swiper-container {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  padding: 0 20px;
}

.with-line {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 55px;
  @media (min-width: 992px) {
    margin-bottom: 140px;
  }

  &:before {
    content: '';
    width: 55px;
    height: 10px;
    background: $primary-color;
    border-radius: 10px;
    margin-right: 30px;
    display: inline-block;
  }
}

.swiper-button-prev {
  left: 0;
  @media (max-width: 992px) {
    left: -8px;
  }
}

.swiper-button-next {
  right: -6px;
  @media (max-width: 992px) {
    right: -8px;
  }
}

.swiper-button-prev,
.swiper-button-next {
  &:after {
    color: $blue;
    font-size: 30px;
    @media (max-width: 992px) {
      font-size: 20px;
    }
  }
}

/*
  • international
  ---------- ---------- ---------- ---------- ----------
*/

.international {
  background: $white;
  padding-top: 30px;
  @media (min-width: 992px) {
    padding-top: 60px;
  }

  &__countries {
    .country {
      margin-right: 20px;
      display: inline-flex;
      margin-bottom: 15px;
      font-size: 12px;
      @media (min-width: 992px) {
        margin-bottom: 30px;
        margin-right: 40px;
        font-size: 20px;
      }

      &::before {
        content: '';
        width: 20px;
        height: 20px;
        border-radius: 20px;
        display: inline-block;
        position: relative;
        top: 3px;
        margin-right: 10px;
        background: $blue;
      }

      &.canada {
        &:before {
          background: #a9ecfc;
        }
      }

      &.usa {
        &:before {
          background: #1bb49c;
        }
      }

      &.mexico {
        &:before {
          background: #fbfe8d;
        }
      }

      &.brazil {
        &:before {
          background: #d41593;
        }
      }

      &.algeria {
        &:before {
          background: #ff0000;
        }
      }

      &.mauritania {
        &:before {
          background: #ab00d4;
        }
      }

      &.senegal {
        &:before {
          background: #6200d4;
        }
      }

      &.guinea {
        &:before {
          background: #0f62d4;
        }
      }

      &.libya {
        &:before {
          background: #6d8007;
        }
      }

      &.jordan {
        &:before {
          background: #b899e2;
        }
      }

      &.poland {
        &:before {
          background: #c9b589;
        }
      }

      &.romania {
        &:before {
          background: #1c5067;
        }
      }

      &.turkey {
        &:before {
          background: #949bd1;
        }
      }

      &.ukraine {
        &:before {
          background: #9eccb9;
        }
      }

      &.quatar {
        &:before {
          background: #f4afaf;
        }
      }

      &.uae {
        &:before {
          background: #00b0d4;
        }
      }

      &.mali {
        &:before {
          background: #16b210;
        }
      }

      &.ivory {
        &:before {
          background: #d4bc00;
        }
      }

      &.tunisia {
        &:before {
          background: #800505;
        }
      }

      &.dubai {
        &:before {
          background: #3490b8;
        }
      }

      &.oman {
        &:before {
          background: #1a475a;
        }
      }

      &.china {
        &:before {
          background: #f47627;
        }
      }
    }
  }
}

/*
  • What we do
  ---------- ---------- ---------- ---------- ----------
*/

.what-we-do {
  &__box {
    padding: 0 0 55px;
    @media (min-width: 992px) {
      padding: 150px 0;
    }

    p {
      margin: 35px 0;
      font-size: 18px;
      @media (min-width: 992px) {
        margin: 60px 0;
        font-size: 30px;
      }
    }

    h2 {
      @media (max-width: 992px) {
        font-size: 25px;
      }
    }
  }

  .box-image {
    position: relative;
    margin-top: 55px;
    @media (min-width: 992px) {
      margin-top: 0;
    }

    img {
      @media (min-width: 992px) {
        position: absolute;
        top: -60px;
        left: -25px;
      }
    }
  }

  .with-line {
    @media (min-width: 992px) {
      margin-bottom: 0;
    }
  }
}

/*
  • Form
  ---------- ---------- ---------- ---------- ----------
*/

.form {
  .sep {
    width: 277px;
    height: 10px;
    background: $primary-color;
    border-radius: 10px;
    text-align: center;
    margin: 0 auto 100px;
  }

  h2 {
    margin-bottom: 85px;
    position: relative;

    &:after {
      content: url('../../images/form-icon.svg');
      display: block;
      position: absolute;
      right: -155px;
      top: -40px;
      @media (max-width: 992px) {
        right: -5px;
        top: -80px;
      }
    }
  }

  form {
    position: relative;
    @media (min-width: 992px) {
      &:before {
        content: url('../../images/big-points.png');
        position: absolute;
        left: -240px;
        top: calc(50% - 80px);
      }
    }
  }
}

.language-switcher-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px;
  padding-bottom: 20px;
  background-color: #000;
  border-bottom: 1px solid #e0e0e0;

  @media (max-width: 768px) {
    justify-content: center;
    padding-bottom: 30px;
  }
}

.language-option {
  margin: 0 5px;
  padding: 5px 10px;
  font-size: 16px;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 5px;
  &:hover,
  &.active {
    background-color: #f1f1f1;
    color: #007bff;
  }
  @media (max-width: 768px) {
    font-size: 14px;
    padding: 4px 8px;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;
  @media (max-width: 992px) {
    justify-content: space-between;
  }
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
}

.button_container {
  margin-top: 10px;
  // Add any additional styles needed for alignment
}
