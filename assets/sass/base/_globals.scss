//------------------------------------------------------------------------
// Global style
//------------------------------------------------------------------------


* {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  box-sizing: border-box;
  vertical-align: baseline;
}

html {
  font-size: 100%;
}

body {
  font-family: $primary-font;
  font-size: 16px;
  line-height: 28px;
  color: $primary-color;
  background-color: $grey;
  height: 100vh;
}

.menu-open {
  overflow-y: hidden;
}

/* begin General CSS */

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: $bold-font;
}

b,
strong {
  font-weight: 500;
}

ul {
  list-style: none;
}

a {
  color: $primary-color;

  @include transition-all;

  &:hover,
  &:focus,
  &:active,
  &.active {
    color: darken($primary-color, 10%);
    text-decoration: none;
    outline: 0 none;
  }

  & > img {
    border: 0 none;
  }
}

img {
  max-width: 100%;
  height: auto;
}

.clear {
  clear: both;
}

.relative {
  position: relative;
}

.hidden {
  visibility: hidden !important;
  display: none !important;
}

/* end General CSS */

main {
  min-height: 40vh;
}

@media(min-width: 768px) {
  .row-md-reverse {
    flex-direction: row-reverse;
  }
}

.main {
  padding-top: 80px;
  padding-bottom: 100px;
  @media(min-width: 992px) {
    padding-top: 200px;
  }
}

.single-page {
  margin-top: 70px;
}

.open-menu {
  .content-image {
    z-index: 0;
  }
}


//------------------------------------------------------------------------
// Helpers
//------------------------------------------------------------------------

.height-100 {
  height: 100%;
}

.content-container {
  margin: 0 30px;
  @media(min-width: 992px) {
    margin: 0 50px;
  }
}

.section {
  margin-bottom: 35px;
  @media(min-width: 992px) {
    margin-bottom: 80px;
  }
}


