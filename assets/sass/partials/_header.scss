header {
  height: 56px;
  background: $primary-color;
  padding: 0px 0px;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 2;
  @media (min-width: 992px) {
    height: 80px;
    padding: 5px 50px;
  }

  .vima-logo {
    @media (min-width: 992px) {
      width: 165px;
    }
    @media (max-width: 992px) {
      max-height: 30px;
    }
  }
}

$color-background: #f5f5f5;
$color-main: #0b486b;
$color-active: #fff;
$color-link: #fff;

$button-height: 27px;
$button-width: 35px;
$font: montserrat, sans-serif;

.button_container {
  position: relative;
  height: $button-height;
  width: $button-width;
  cursor: pointer;
  z-index: 100;
  transition: opacity 0.25s ease;
  border: 0;
  background-color: transparent;

  &:hover {
    opacity: 0.7;
  }

  &.active {
    .top {
      transform: translateY(11px) translateX(0) rotate(45deg);
      background: $color-active;
    }

    .middle {
      opacity: 0;
      background: $color-active;
    }

    .bottom {
      transform: translateY(-11px) translateX(0) rotate(-45deg);
      background: $color-active;
    }
  }

  span {
    background: $white;
    border: none;
    height: 4px;
    border-radius: 10px;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transition: all 0.35s ease;
    cursor: pointer;

    &:nth-of-type(2) {
      top: 11px;
    }

    &:nth-of-type(3) {
      top: 22px;
    }
  }
}

.overlay {
  position: fixed;
  background: $white;
  top: 56px;
  left: 0;
  width: 100%;
  height: 0;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.35s, visibility 0.35s, height 0.35s;
  overflow: hidden;
  overflow-y: scroll;
  @media (min-width: 992px) {
    top: 90px;
  }

  &.open {
    opacity: 1;
    visibility: visible;
    height: 100%;
    z-index: 1;

    li {
      animation: fadeInRight 0.5s ease forwards;
      animation-delay: 0.35s;

      &:nth-of-type(2) {
        animation-delay: 0.4s;
      }

      &:nth-of-type(3) {
        animation-delay: 0.45s;
      }

      &:nth-of-type(4) {
        animation-delay: 0.5s;
      }

      &:nth-of-type(5) {
        animation-delay: 0.55s;
      }

      &:nth-of-type(6) {
        animation-delay: 0.6s;
      }
    }

    nav {
      .drop-down {
        &.closed {
          opacity: 1;
        }
      }
    }
  }

  nav {
    position: relative;
    height: calc(100% + 170px);
    top: 40px;
    text-align: center;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0 auto;
    display: block;
    position: relative;

    li {
      display: block;
      position: relative;
      opacity: 0;
      margin-bottom: 20px;
      @media (min-width: 1300px) {
        margin-bottom: 40px;
      }

      a {
        display: block;
        position: relative;
        color: $primary-color;
        text-decoration: none;
        overflow: hidden;
        font-size: 20px;
        line-height: 35px;
        font-family: $bold-font;
        @media (min-width: 992px) {
          font-size: 30px;
        }
        @media (min-width: 1300px) {
          font-size: 40px;
        }

        &:hover:after,
        &:focus:after,
        &:active:after {
          width: 100%;
          color: $blue;
        }
      }
    }
  }

  .links {
    margin-top: 30px;
    @media (min-width: 992px) {
      margin-top: 100px;
    }

    span {
      display: block;
      margin: 0 25px 15px;
      @media (min-width: 992px) {
        display: inline-block;
        margin: 0 25px;
      }

      i {
        @media (max-width: 992px) {
          display: block;
          margin-bottom: 5px;
        }
      }

      &:hover {
        color: #1bb49c;
        cursor: pointer;
      }
    }
  }

  .overlay-menu {
    .menu-items {
      &:after {
        content: '';
        display: block;
        width: 70%;
        height: 1px;
        background: $near-grey;
        margin: 0 auto 30px;
        @media (min-width: 992px) {
          width: 40%;
        }
      }
    }
  }

  .menu-items {
    li {
      a {
        &:hover {
          color: $blue;
        }
      }
    }
  }
}

nav {
  .drop-down {
    list-style: none;
    overflow: hidden; /* When ul height is reduced, ensure overflowing li are not shown */
    margin: 0 auto;
    padding: 0;
    text-align: center;
    -webkit-transition: height 0.3s ease;
    transition: height 0.3s ease;

    &.closed {
      height: 60px;
      opacity: 0;

      li {
        .nav-button {
          font-size: 20px;
          @media (min-width: 992px) {
            font-size: 30px;
          }
          @media (min-width: 1300px) {
            font-size: 40px;
          }

          i {
            &:before {
              transform: rotate(0);
            }
          }
        }
      }
    }

    li {
      a {
        color: $primary-color;
        display: block;
        font-size: 16px;
        line-height: 30px;
        font-family: $bold-font;
        @media (min-width: 992px) {
          font-size: 30px;
        }
        @media (min-width: 1300px) {
          font-size: 40px;
          line-height: 40px;
        }
      }

      .nav-button {
        display: inline-block;
        text-decoration: underline;
        min-width: 140px;

        i {
          &:before {
            position: relative;
            top: -2px;
            font-size: 10px;
            right: -10px;
            transform: rotate(180deg);
            display: inline-block;
            @media (min-width: 992px) {
              top: -7px;
            }
          }
        }
      }
    }

    &__submenu {
      align-items: center;
      justify-content: center;
      @media (min-width: 992px) {
        display: flex !important;
        margin-top: 20px;
      }

      li {
        @media (min-width: 992px) {
          margin: 0 75px;
        }
      }
    }
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    left: 20%;
  }
  100% {
    opacity: 1;
    left: 0;
  }
}
